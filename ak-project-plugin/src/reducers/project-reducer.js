import {
  CREATE_PROJECT,
  LIST_PROJECT,
  LIST_ORG_PROJECT,
  UPDATE_PROJECT,
  GET_PROJECT,
  GET_LIVE_PROJECT,
  SAVE_PROJECT,
  POST_IMAGE,
  POST_VIDEO,
  PROJECT_SELECTED,
  <PERSON><PERSON>ME<PERSON>_SELECTED,
  <PERSON>LEMENT_PROPERTY_CHANGE,
  <PERSON><PERSON>MENT_EVENT_CHANGE,
  DELETE_PROJECT,
  ADD_ELEMENT_TO_SELECTED_ELEMENT,
  EDIT_PAGE,
  UPDATE_PAGES,
  CHANGE_SCREEN_TYPE,
  ADD_ELEMENT_TO_PAGE,
  GET_PAGE,
  GET_LIVE_PAGE,
  SELECTED_ACTION,
  DELETE_ACTION,
  ADD_ACTION,
  UPDATE_ACTION,
  ADD_STEP,
  DELETE_STEP,
  UPDATE_VARIABLE,
  REMOVE_VARIABLE,
  POPULATE_VARIABLE,
  PAGE_EVENT_CHANGE,
  <PERSON>GE_PROPERTY_CHANGE,
  ACTIONS_TEST_URL,
  ADD_ELEMENT_TO_REUSE,
  UPDATE_INSTANCES,
  UPDATE_ASSETS,
  ADD_ICON,
  <PERSON>XECUTE_ACTION_STARTED,
  EXECUTE_ACTION_ENDED,
  GET_PROJECT_VERSIONS,
  ADD_ELEMENT_DRAGGED_ITEM,
  REUSE_ELEMENT_DRAGGED_ITEM,
  GET_DIALOG,
  ADD_ELEMENT_TO_DIALOG,
  ADD_DIALOG,
  DIALOG_EVENT_CHANGE,
  DIALOG_PROPERTY_CHANGE,
  UPDATE_DIALOG_INSTANCES,
  TOGGLE_PREVIEW_DIALOG,
  DOUBLE_CLICK_ELEMENT,
  UPDATE_TARGET_VARIABLE,
  POPULATE_TARGET_VARIABLE,
  REMOVE_TARGET_VARIABLE,
  SELECTED_TARGET,
  ADD_TARGET,
  UPDATE_TARGET,
  DELETE_TARGET,
  ADD_DOMAIN,
  DELETE_DOMAIN,
  CANVAS_DIMENSION,
  COPY_ELEMENTS_SCREENTYPE,
  RESYNC_ELEMENTS,
  POST_FAVICON,
  PUBLISH_TO_TARGET,
  DELETE_ELEMENT_FROM_PAGE,
  DELETE_ELEMENT_FROM_CONTAINER,
  MOVE_LAYERS_PAGE_ITEMS,
  MOVE_LAYERS_CONTAINER_ITEMS,
  COPY_TO_SELECTEDPAGES,
  UPDATE_STEP,
  EXPORT_PROJECT,
  CHANGE_ELEMENT_LAYER,
  CHANGE_ELEMENT_LAYER_INSIDE_CONTAINER,
  UPDATE_PAGE_TITLE,
  SET_LIVE_BASE_PATH,
  SET_PROJECT,
  SET_PAGE,
  SET_INITIAL_PAGE,
  DELETE_ELEMENT_FROM_DIALOG,
  SET_DIALOG,
  EDIT_DIALOG,
  LOAD_SITEMAP,
  MODIFY_SITEMAP_PAGES,
  SAVE_SITEMAP,
  MODIFY_ROOT_DOMAIN,
  ADD_SITEMAP_CUSTOM_LINK,
  GET_CMS_LINKS,
  ADD_SITEMAP_CMS_FILTER_CONDITIONS,
  RESYNC_CMS_LINKS,
  LOAD_SETTINGS,
  SAVE_ACCESS_MGMT,
  SAVE_SETTINGS,
  UPDATE_PAGE_SETTINGS,
  VIEWPORT_POSITION,
  LAST_ELEMENT_PAGE,
  LAST_ELEMENT_SELECTED_ELEMENT,
  LAST_ELEMENT_DIALOG,
  ELEMENT_HEIGHT_CHANGE,
  COLLAPSIBLE_NAV_CONTENT,
  NAV_COLLAPSIBLE,
  CONTAINER_VIEWPORT_POSITION,
  PUBLISH_PEGA_COMPONENT,
  LIST_PEGA_COMPONENT,
  UPDATE_AMIMATIONS,
  IMPORT_PROJECT,
  LIST_BRANCHES,
  CREATE_BRANCH,
  GET_BRANCH,
  COPY_TO_SELECTED_DIALOGS,
  GET_COMMIT,
  LIST_COMMITS,
  SET_BRANCH,
  SET_COMMIT,
  SET_BRANCH_POPUP,
  READY_TO_MERGE,
  MERGE_BRANCH,
  SET_OPEN_CONTAINER,
  SET_OPEN_INPUT,
  SET_OPEN_OTHERS,
  SET_OPEN_MUI,
  SET_OPEN_PEGA,
  DOUBLE_CLICK_PAGE
} from '../actions/ActionType';
import { findIndex, find, cloneDeep, isEmpty, set } from 'lodash';

import { utils } from '@astrakraft/core-lib';
const { stringUtil } = utils;
const { randstr } = stringUtil;

const { produceWithPatches } = require('immer');
import * as ActionTypes from '../actions/ActionType';

const NESTED_DEPTH_LIMIT = 4;

function updateElementWithPathClone(
  parentElement,
  modifiedElement,
  parentPath = [],
  currentDepth = 0,
  maxDepth = NESTED_DEPTH_LIMIT
) {
  // If the depth exceeds the max depth, stop the traversal
  if (currentDepth > maxDepth) {
    return null;
  }

  if (parentElement.id === modifiedElement.id) {
    // Deep clone of the parentPath elements to avoid modifying the original data
    let clonedPath = parentPath.map((element) => ({ ...element, items: [...element.items] }));

    // Update the item in the cloned path
    let updatedItem = { ...parentElement, ...modifiedElement };

    if (clonedPath.length > 0) {
      // Manually update the items of each parent in the cloned path, starting from the deepest child
      for (let i = clonedPath.length - 1; i >= 0; i--) {
        if (i === clonedPath.length - 1) {
          // Directly update the last item in the path
          let itemIndex = findIndex(clonedPath[i].items, { id: modifiedElement.id });
          clonedPath[i].items.splice(itemIndex, 1, updatedItem);
        } else {
          // For all other items, replace their child with the updated child
          let childIndex = findIndex(clonedPath[i].items, { id: clonedPath[i + 1].id });
          clonedPath[i].items.splice(childIndex, 1, clonedPath[i + 1]);
        }
      }

      // Return the top parent which has now an updated tree path to the modified element
      return clonedPath[0];
    }
    // If there's no parent, it means we were updating the root
    return updatedItem;
  }

  // Go deeper in the tree if not at max depth
  if (parentElement.items && currentDepth < maxDepth) {
    for (let i = 0; i < parentElement.items.length; i++) {
      let result = updateElementWithPathClone(
        parentElement.items[i],
        modifiedElement,
        [...parentPath, parentElement],
        currentDepth + 1,
        maxDepth
      );
      if (result) return result;
    }
  }

  // No update was done, return null. This helps to identify if an update was successful or not
  return null;
}

function updateElements(selectedScreen, modifiedElement) {
  let updatedScreen = updateElementWithPathClone(selectedScreen, modifiedElement, [], 0, NESTED_DEPTH_LIMIT);
  // If update is not done, means we didn't find the element, we can decide to return the original page or handle the error
  return updatedScreen ? updatedScreen : selectedScreen;
}

function updateElementProps(element, selectedElement, themedElementProps) {
  const reuseId = element.data.reuseId;
  if (reuseId && reuseId === selectedElement?.id) {
    Object.assign(element.data.renderProps, themedElementProps);
    element.data.id = selectedElement.data.id;
  }
  if (element.items && element.items.length) {
    for (const nestedItem of element.items) {
      //eslint-disable-line no-unused-vars
      updateElementProps(nestedItem, selectedElement, themedElementProps);
    }

    return {
      ...element,
      data: {
        ...element.data,
        renderProps: {
          ...element.data.renderProps,
          ...themedElementProps
        },
        id: selectedElement.data.id
      }
    };
  }
  if (element.items && element.items.length) {
    return {
      ...element,
      items: element.items.map((nestedItem) => updateElementProps(nestedItem, selectedElement, themedElementProps))
    };
  }
  return element;
}

const findAndDeleteByIndex = (items, containerId, deleteIndex) => {
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (item.id === containerId) {
      if (item.items && item.items.length > deleteIndex) {
        item.items.splice(deleteIndex, 1);
        return true;
      }
    }

    if (item.items && item.items.length > 0) {
      const found = findAndDeleteByIndex(item.items, containerId, deleteIndex);
      if (found) return true;
    }
  }
  return false;
};

const findAndMoveLayers = (items, containerId, layer, selectedElementId) => {
  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    if (item.id === containerId && item.items) {
      const index = findIndex(item.items, { id: selectedElementId });

      if (index !== -1) {
        switch (layer) {
          case 'moveToFront':
            item.items.push(item.items.splice(index, 1)[0]);
            break;
          case 'moveToback': {
            const [movedItem] = item.items.splice(index, 1);
            item.items.unshift(movedItem);
            break;
          }
          case 'shiftOneLayerFront':
            if (index < item.items.length - 1) {
              [item.items[index], item.items[index + 1]] = [item.items[index + 1], item.items[index]];
            }
            break;
          case 'shiftOneLayerBack':
            if (index > 0) {
              [item.items[index], item.items[index - 1]] = [item.items[index - 1], item.items[index]];
            }
            break;
          default:
            return false;
        }
        return true;
      }
    }

    if (item.items && item.items.length > 0) {
      const found = findAndMoveLayers(item.items, containerId, layer, selectedElementId);
      if (found) return true;
    }
  }
  return false;
};

const moveItemInContainer = (items, itemId, newIndex) => {
  const itemIndex = findIndex(items, { id: itemId });

  if (itemIndex !== -1) {
    const itemToMove = items.splice(itemIndex, 1)[0];
    items.splice(newIndex, 0, itemToMove);
    return true;
  }

  for (let item of items) {
    //eslint-disable-line no-unused-vars
    if (item.items && item.items.length > 0) {
      const moved = moveItemInContainer(item.items, itemId, newIndex);
      if (moved) {
        return true;
      }
    }
  }

  return false;
};

function handleMoveAction(updatedItems, index, action) {
  const updatedItemsCopy = [...updatedItems]; // Create a copy of updatedItems

  switch (action) {
    case 'moveToFront':
      updatedItemsCopy.push(updatedItemsCopy.splice(index, 1)[0]);
      break;
    case 'moveToback':
      updatedItemsCopy.unshift(updatedItemsCopy.splice(index, 1)[0]);
      break;
    case 'shiftOneLayerFront':
      if (index < updatedItemsCopy.length - 1) {
        [updatedItemsCopy[index], updatedItemsCopy[index + 1]] = [updatedItemsCopy[index + 1], updatedItemsCopy[index]];
      }
      break;
    case 'shiftOneLayerBack':
      if (index > 0) {
        [updatedItemsCopy[index], updatedItemsCopy[index - 1]] = [updatedItemsCopy[index - 1], updatedItemsCopy[index]];
      }
      break;
    default:
      break;
  }

  return updatedItemsCopy; // Return the updated copy
}

const updateChangedData = (
  changedValues,
  changedData,
  index,
  existingPropertyIndex,
  isSubmenu = false,
  submenuIndex = -1
) => {
  const updatedData = { ...changedData };

  const existingOptionIndex = updatedData.customisedData[existingPropertyIndex].optionsCustomised.findIndex(
    (opt) => opt.optionIndex === index
  );
  if (existingOptionIndex === -1) {
    const newOption = {
      optionIndex: index,
      changedValueFrom: isSubmenu ? {} : changedValues,
      submenuCustomised: isSubmenu ? [{ submenuIndex, changedValueFrom: changedValues }] : []
    };

    updatedData.customisedData[existingPropertyIndex].optionsCustomised.push(newOption);
  } else {
    const existingOption = updatedData.customisedData[existingPropertyIndex].optionsCustomised[existingOptionIndex];

    if (isSubmenu) {
      if (!existingOption.submenuCustomised) {
        existingOption.submenuCustomised = [];
      }

      const existingSubmenuIndex = existingOption.submenuCustomised.findIndex(
        (sub) => sub.submenuIndex === submenuIndex
      );
      if (existingSubmenuIndex === -1) {
        existingOption.submenuCustomised.push({
          submenuIndex,
          changedValueFrom: changedValues
        });
      } else {
        const submenuCustom = existingOption.submenuCustomised[existingSubmenuIndex];
        submenuCustom.changedValueFrom = {
          ...submenuCustom.changedValueFrom,
          ...changedValues
        };
      }
    } else {
      if (!existingOption.changedValueFrom) {
        existingOption.changedValueFrom = {};
      }

      existingOption.changedValueFrom = {
        ...existingOption.changedValueFrom,
        ...changedValues
      };
    }
  }

  return updatedData;
};

function getScreenType(page, selectedScreenType) {
  // If page is '_Theme', always return 'desktop'
  if (page?.name === '_Theme') {
    return 'desktop';
  }
  return selectedScreenType || 'desktop';
}

function updateCustomisedData(draftElement, draftSelectedPage, screenType, eventProps) {
  const updateScreenType = getScreenType(draftSelectedPage, screenType);

  const items = draftSelectedPage.screen[updateScreenType].items || [];
  let observerElement;

  if (draftElement.parent?.id) {
    const container = find(items, { id: draftElement.parent.id });
    if (container) {
      observerElement = find(container.items || [], { id: draftElement.referenceId });
    }
  } else {
    observerElement = find(items, { id: draftElement.referenceId });
  }

  if (!observerElement) return draftSelectedPage;

  const observerData = {
    x: observerElement.data.x,
    y: observerElement.data.y,
    ...observerElement.data.eventProps,
    ...observerElement.data.renderProps
  };
  const subjectData = {
    x: draftElement.data.x,
    y: draftElement.data.y,
    ...draftElement.data.eventProps,
    ...draftElement.data.renderProps
  };
  const existingElementIndex = findIndex(draftSelectedPage.customisedElements, { instanceId: draftElement.id });
  const changedData =
    existingElementIndex !== -1
      ? { ...draftSelectedPage.customisedElements[existingElementIndex] }
      : { instanceId: draftElement.id, customisedData: [] };

  for (const key of Object.keys(subjectData)) {
    //eslint-disable-line no-unused-vars
    if (key === 'columns' || key === 'options' || key === 'actionButtons') {
      if (!subjectData['optionType'] || subjectData['optionType'] === 'static') {
        for (const [index, option] of subjectData[key].entries()) {
          //eslint-disable-line no-unused-vars
          const observerOptions = observerData[key]?.[index] || {};
          const existingPropertyIndex = findIndex(changedData.customisedData || [], { propertyName: key });

          let optionChanged = false;
          const changedValues = {};

          for (const property of Object.keys(option)) {
            //eslint-disable-line no-unused-vars
            if (property === 'submenu') {
              for (const [submenuIndex, submenu] of option.submenu.entries()) {
                //eslint-disable-line no-unused-vars
                const observerSubmenu = observerOptions.submenu?.[submenuIndex] || {};
                let submenuChanged = false;
                const changedSubmenuValues = {};

                for (const submenuProperty of Object.keys(submenu)) {
                  //eslint-disable-line no-unused-vars
                  if (submenu[submenuProperty] !== observerSubmenu[submenuProperty]) {
                    changedSubmenuValues[submenuProperty] = submenu[submenuProperty];
                    submenuChanged = true;
                  }
                }

                if (submenuChanged) {
                  if (existingPropertyIndex === -1) {
                    changedData.customisedData.push({
                      propertyName: key,
                      optionsCustomised: [
                        {
                          optionIndex: index,
                          changedValueFrom: {},
                          submenuCustomised: [{ submenuIndex, changedValueFrom: changedSubmenuValues }]
                        }
                      ]
                    });
                  } else {
                    updateChangedData(
                      changedSubmenuValues,
                      changedData,
                      index,
                      existingPropertyIndex,
                      true,
                      submenuIndex
                    );
                  }
                }
              }
            } else if (option[property] !== observerOptions[property]) {
              changedValues[property] = option[property];
              optionChanged = true;
            }
          }
          if (optionChanged) {
            if (existingPropertyIndex === -1) {
              changedData.customisedData.push({
                propertyName: key,
                optionsCustomised: [{ optionIndex: index, changedValueFrom: changedValues }]
              });
            } else {
              updateChangedData(changedValues, changedData, index, existingPropertyIndex);
            }
          }
        }
      }
    } else if (observerData[key] !== subjectData[key]) {
      const existingPropertyIndex = findIndex(changedData.customisedData || [], { propertyName: key });
      if (existingPropertyIndex === -1) {
        if (key === 'x' || key === 'y') {
          const otherKey = key === 'x' ? 'y' : 'x';
          changedData.customisedData.push(
            { propertyName: otherKey, changedValueFrom: subjectData[otherKey] },
            { propertyName: key, changedValueFrom: subjectData[key] }
          );
        } else {
          changedData.customisedData.push({ propertyName: key, changedValueFrom: subjectData[key] });
        }
      } else {
        changedData.customisedData[existingPropertyIndex].changedValueFrom = subjectData[key];
      }
    }
    if (eventProps) {
      const selectedElementEventProps = draftElement.data.eventProps || {};
      const findOtherAssociatedKeys = key.includes('__');
      if (findOtherAssociatedKeys) {
        const eventKey = key.split('__')[0];
        for (const subKey of Object.keys(selectedElementEventProps)) {
          // eslint-disable-line no-unused-vars
          const existingEventSubKey = findIndex(changedData.customisedData || [], { propertyName: eventKey });
          if (subKey === eventKey && existingEventSubKey === -1) {
            changedData.customisedData.push({
              propertyName: subKey,
              changedValueFrom: selectedElementEventProps[subKey]
            });
          }
        }
      }
    }
  }

  const updatedDraftSelectedPage = draftSelectedPage;
  if (existingElementIndex !== -1) {
    updatedDraftSelectedPage.customisedElements[existingElementIndex].customisedData = changedData.customisedData;
  } else {
    updatedDraftSelectedPage.customisedElements = updatedDraftSelectedPage.customisedElements || [];
    updatedDraftSelectedPage.customisedElements.push(changedData);
  }
  return updatedDraftSelectedPage;
}

function handleMobileScreenType(draftElement, draftSelectedPage, eventProps) {
  const otherScreenTypes = ['mobileLandscape', 'tablet', 'desktop'];
  const propKey = eventProps ? 'eventProps' : 'renderProps';

  for (const screenType of otherScreenTypes) {
    //eslint-disable-line no-unused-vars
    let items = draftSelectedPage.screen[screenType]?.items || [];
    if (items && items.length) {
      const isContainerElementSelected = draftElement?.parent?.id;
      if (isContainerElementSelected) {
        const findIndexOfContainer = findIndex(items, { referenceId: draftElement.parent.id });
        if (findIndexOfContainer !== -1) {
          items = items[findIndexOfContainer].items;
        }
      }

      for (const item of items) {
        //eslint-disable-line no-unused-vars
        if (item.referenceId === draftElement?.id) {
          const existingElementIndex = findIndex(draftSelectedPage.customisedElements, { instanceId: item.id });
          if (existingElementIndex !== -1) {
            const existingElement = draftSelectedPage.customisedElements[existingElementIndex];
            existingElement.customisedData = existingElement.customisedData || [];
            const subjectData = { x: draftElement.data.x, y: draftElement.data.y, ...draftElement.data[propKey] };

            for (const key of Object.keys(subjectData)) {
              //eslint-disable-line no-unused-vars
              const findPropertyChanged = find(existingElement.customisedData, { propertyName: key });

              if (
                (key === 'columns' || key === 'options' || key === 'actionButtons') &&
                findPropertyChanged &&
                findPropertyChanged?.optionsCustomised
              ) {
                const updatedOptions = JSON.parse(JSON.stringify(subjectData));

                for (const option of findPropertyChanged.optionsCustomised) {
                  //eslint-disable-line no-unused-vars
                  if (item.data[propKey][key] && item.data[propKey][key][option.optionIndex]) {
                    const updatedItemOptions = JSON.parse(JSON.stringify(item.data[propKey][key][option.optionIndex]));
                    if (option.changedValueFrom) {
                      for (const property of Object.keys(option.changedValueFrom)) {
                        //eslint-disable-line no-unused-vars
                        if (updatedOptions[key] && updatedOptions[key][option.optionIndex]) {
                          if (updatedOptions[key][option.optionIndex][property])
                            delete updatedOptions[key][option.optionIndex][property];
                          item.data[propKey][key][option.optionIndex] = {
                            ...updatedItemOptions,
                            ...updatedOptions[key][option.optionIndex]
                          };
                        }
                      }
                    }
                    if (option.submenuCustomised && option.submenuCustomised.length) {
                      const submenuOptions = updatedItemOptions.submenu || [];
                      const updatedSubmenuOptions = item.data[propKey][key][option.optionIndex].submenu || [];
                      for (const submenu of option.submenuCustomised || []) {
                        //eslint-disable-line no-unused-vars
                        if (updatedSubmenuOptions[submenu.submenuIndex]) {
                          const updatedSubmenuItemOptions = JSON.parse(
                            JSON.stringify(updatedSubmenuOptions[submenu.submenuIndex])
                          );
                          for (const submenuProperty of Object.keys(submenu.changedValueFrom)) {
                            //eslint-disable-line no-unused-vars
                            if (
                              submenuOptions[submenu.submenuIndex] &&
                              submenuOptions[submenu.submenuIndex][submenuProperty]
                            ) {
                              updatedSubmenuOptions[submenu.submenuIndex] = {
                                ...updatedSubmenuItemOptions,
                                ...submenuOptions[submenu.submenuIndex]
                              };
                            }
                          }
                        }
                      }
                    }
                  }

                  // Ensure all subjectData options are in item.data[propKey][key]
                  subjectData[key].forEach((option, index) => {
                    const findOptionChanged = find(findPropertyChanged.optionsCustomised, { optionIndex: index });
                    if (!findOptionChanged) {
                      if (!item.data[propKey][key][index]) {
                        item.data[propKey][key][index] = option;
                      } else {
                        item.data[propKey][key][index] = {
                          ...item.data[propKey][key][index],
                          ...option
                        };
                      }
                    }
                  });

                  // Handle deletion of options that no longer exist in subjectData
                  item.data[propKey][key] = item.data[propKey][key].filter((option, index) => {
                    const findOptionChanged = find(findPropertyChanged.optionsCustomised, { optionIndex: index });
                    const optionExist = subjectData[key]?.[index];
                    return findOptionChanged || optionExist;
                  });
                }
              } else if (!findPropertyChanged) {
                if (key === 'x' || key === 'y') {
                  item.data[key] = subjectData[key];
                } else {
                  if (!item.data[propKey]) {
                    item.data[propKey] = {};
                  }
                  item.data[propKey][key] = subjectData[key];
                }
              }
            }
          } else {
            item.data[propKey] = draftElement.data[propKey];
            item.data.x = draftElement.data.x;
            item.data.y = draftElement.data.y;
          }
        }
      }
    }
  }
}

function updateScreenTypes(draftSelectedPage, result, selectedElement) {
  const otherScreenTypes = ['mobileLandscape', 'tablet', 'desktop'];
  const updatedSelectedPage = draftSelectedPage;
  for (const screenType of otherScreenTypes) {
    // eslint-disable-line no-unused-vars
    if (selectedElement) {
      updatedSelectedPage.screen[screenType].items = updatedSelectedPage.screen[screenType].items || [];
      const findIndexOfContainer = findIndex(updatedSelectedPage.screen[screenType].items, {
        referenceId: selectedElement.id
      });
      if (findIndexOfContainer !== -1) {
        const containerElement = updatedSelectedPage.screen[screenType].items[findIndexOfContainer];
        containerElement.items = containerElement.items || [];
        containerElement.items.push({ ...result, id: randstr(`${result.data.id}_`), referenceId: result.id });
      }
    } else {
      updatedSelectedPage.screen[screenType].items.push({
        ...result,
        id: randstr(`${result.data.id}_`),
        referenceId: result.id
      });
    }
  }
  return updatedSelectedPage;
}

// fucntion for allowing other reducer action
function checkForOtherReducerActions(actionType) {
  return Object.values(ActionTypes).some((definedType) => actionType.startsWith(definedType));
}

const allowedViewActionTypes = [
  // Commit-related actions
  GET_COMMIT,
  LIST_COMMITS,

  // Branch-related actions
  LIST_BRANCHES,
  GET_BRANCH,
  SET_BRANCH,
  CREATE_BRANCH,

  // Project-related actions
  GET_PROJECT,
  LIST_PROJECT,
  LIST_ORG_PROJECT,
  CREATE_PROJECT,

  // Page and Dialog actions
  GET_PAGE,
  SET_DIALOG,

  // Navigation actions
  COLLAPSIBLE_NAV_CONTENT,
  NAV_COLLAPSIBLE,
  CANVAS_DIMENSION,

  // Selection actions
  SELECTED_ACTION,

  // Selection actions
  SELECTED_TARGET,

  // Settings and Sitemap
  LOAD_SETTINGS,
  LOAD_SITEMAP,

  // Screen type and data resetting
  CHANGE_SCREEN_TYPE,
  SET_BRANCH_POPUP,

  // other
  LAST_ELEMENT_SELECTED_ELEMENT,
  LAST_ELEMENT_DIALOG,
  LAST_ELEMENT_PAGE,
  ADD_ELEMENT_DRAGGED_ITEM,
  VIEWPORT_POSITION
];

const SUCCESS_MESSAGES = ['Merge successful', 'Conflicts merged successfully'];
// quick action
let lastChangeMasterData = []; // Array to store actions and their data

// all the changes the user made in the wizard
let changes = [];
// the inverse of all the changes made in the wizard
let inverseChanges = [];

/* eslint-disable no-param-reassign */

const projectReducer = (state = {}, action) => {
  if (
    state.myProject?.settings &&
    !(state.myProject.settings.enableMasterEdit ?? true) &&
    !action.type.startsWith('SAVE_SETTINGS')
  ) {
    if (state.selectedBranch && state.selectedBranch.name === 'master') {
      const checkOtherReducerActions = checkForOtherReducerActions(action.type);
      const isAllowedAction = allowedViewActionTypes.some((baseType) => action.type.startsWith(baseType));
      // special case for ELEMENT_SELECTED
      if (action.type === 'ELEMENT_SELECTED' && !action.result) {
        return {
          ...state,
          showCreateBranchDialog: false
        };
      }

      if (!isAllowedAction && checkOtherReducerActions && !state.showCreateBranchDialog) {
        lastChangeMasterData = [action];
        return {
          ...state,
          showCreateBranchDialog: true
        };
      }
    }
  }

  switch (action.type) {
    case `${SET_BRANCH_POPUP}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.showCreateBranchDialog = false;
        draft.selectedElement = null;
      });
      return newState;
    }

    case `${ELEMENT_SELECTED}`: {
      const { result } = action;
      const [newState] = produceWithPatches(state, (draft) => {
        if (result) {
          draft.selectedElement = result.selectedElement;
          draft.selectedElementProps = result.selectedElementProps;
          draft.selectedEventProps = result.selectedEventProps;
          draft.selectedGridIndex = result.selectedGridIndex;
        } else {
          draft.selectedElement = null;
          draft.selectedElementProps = null;
          draft.selectedEventProps = null;
          draft.selectedGridIndex = null;
        }
      });

      return newState;
    }

    case `${EDIT_PAGE}`: {
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const { page, editDetails } = action.result;

        let pageIndex;
        const pagesPopulated = draft.myProject.pagesPopulated;

        if (page._id) {
          pageIndex = findIndex(pagesPopulated, { _id: page._id });
        } else {
          pageIndex = findIndex(pagesPopulated, { name: page.name });
        }

        pagesPopulated[pageIndex] = Object.assign({}, pagesPopulated[pageIndex], editDetails);
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${UPDATE_PAGES}`: {
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        draft.myProject.pagesPopulated = action.result;
        const pages = action.result.map((pageItem) => {
          return pageItem._id;
        });
        draft.myProject.pages = pages;
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${ADD_DIALOG}`: {
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        draft.myProject.dialogsPopulated = action.result;
        const dialogs = action.result.map((item) => {
          return item._id;
        });
        draft.myProject.dialogs = dialogs;
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${GET_PAGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const myProject = state.myProject;
        const pagePath = action.result;
        if (myProject) {
          const pageData = find(myProject.pagesPopulated, { path: pagePath });
          draft.selectedPage = pageData;
        }
      });
      return newState;
    }

    case `${GET_DIALOG}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const myProject = state.myProject;
        const dialogPath = action.result;
        const dialogData = find(myProject.dialogsPopulated, { path: dialogPath });
        draft.selectedDialog = dialogData;
      });

      return newState;
    }

    case `${GET_LIVE_PAGE}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        // draft.selectedPage = null;
        draft.error = null;
        draft.resolvedPath = action.result.resolvedPath;
      });
      return newState;
    }
    case `${GET_LIVE_PAGE}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedPage = {
          ...action.result,
          resolvedPath: state.resolvedPath
        };
        draft.error = null;
      });
      return newState;
    }
    case `${GET_LIVE_PAGE}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedPage = null;
        draft.error = action.error;
      });
      return newState;
    }

    // case `${GET_LIVE_PAGE}`: {
    //   const [newState] = produceWithPatches(state, draft => {
    //     const myProject = draft.myProject;
    //     const pageResolvedPath = action.result.join('/');
    //     const pagesPopulated = myProject.pagesPopulated;

    //     // Preprocess paths in the pathsArray
    //     const processedPathsArray = pagesPopulated.map((pathObj) => {
    //       return {
    //         ...pathObj,
    //         path: pathObj.path.replace(/\{\{(.*?)\}\}/g, ':$1')
    //       };

    //     });

    //     const modifiedMatchedPage = matchUrlToPath(pageResolvedPath, processedPathsArray);

    //     const pageData = find(pagesPopulated, { id: modifiedMatchedPage.id });
    //     pageData.resolvedPath = pageResolvedPath;
    //     draft.selectedPage = pageData;
    //   });

    //   return newState;

    // }

    case `${CHANGE_SCREEN_TYPE}`: {
      const { result } = action;

      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const myProject = draft.myProject;
        myProject.selectedScreenType = result;
      });

      changes.push(patches);
      inverseChanges.push(inversePatches);

      return newState;
    }

    case `${ADD_ELEMENT_TO_PAGE}`: {
      const { result } = action;

      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedPage = state.selectedPage;
        const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

        let pageIndex;
        const pagesPopulated = draft.myProject.pagesPopulated;

        if (selectedPage._id) {
          pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
        } else {
          pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
        }

        let draftSelectedPage = draft.myProject.pagesPopulated[pageIndex];
        draftSelectedPage.screen[selectedScreenType].items = draftSelectedPage.screen[selectedScreenType].items || [];
        draftSelectedPage.screen[selectedScreenType].items.push(result);

        if ((selectedScreenType === 'mobile' || selectedScreenType === 'mobileLandscape') && result.liveReplicate) {
          draftSelectedPage = updateScreenTypes(draftSelectedPage, result);
        }

        draft.selectedPage = draftSelectedPage;

        if (draftSelectedPage.name === '_Theme') {
          draft.myProject.reuse = draft.myProject.reuse || [];
          result.data.reuseId = `reuse_${result.id}`;
          draft.myProject.reuse.push(result);
        }
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${ADD_ELEMENT_TO_SELECTED_ELEMENT}`: {
      const { result } = action;

      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedElement = draft.selectedElement;
        if (!isEmpty(selectedElement) && draft.selectedPage?.name !== '_Theme') {
          selectedElement.items = selectedElement.items || [];
          selectedElement.items.push(result);
        }

        //udpate myProject
        if (state.selectedPage) {
          const selectedPage = state.selectedPage;
          const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

          const pagesPopulated = draft.myProject.pagesPopulated;
          let pageIndex;

          if (selectedPage._id) {
            pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
          } else {
            pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
          }

          let draftSelectedPage = draft.myProject.pagesPopulated[pageIndex];

          const screenTypeScreen = updateElements(selectedPage.screen[selectedScreenType], selectedElement);
          draftSelectedPage.screen[selectedScreenType] = screenTypeScreen;

          if (selectedScreenType === 'mobile' || selectedScreenType === 'mobileLandscape') {
            draftSelectedPage = updateScreenTypes(draftSelectedPage, result, selectedElement);
          }

          draft.selectedPage = draftSelectedPage;
        } else if (state.selectedDialog) {
          const selectedDialog = state.selectedDialog;

          const dialogsPopulated = draft.myProject.dialogsPopulated;
          let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

          const draftSelectedDialog = draft.myProject.dialogsPopulated[dialogIndex];

          const screenTypeScreen = updateElements(selectedDialog.screen, selectedElement);
          draftSelectedDialog.screen = screenTypeScreen;

          draft.selectedDialog = draftSelectedDialog;
        }
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${MOVE_LAYERS_PAGE_ITEMS}`: {
      const { result } = action;
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        if (draft.selectedPage) {
          const selectedPage = state.selectedPage;
          const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

          const pagesPopulated = draft.myProject.pagesPopulated;

          const pageIndex = selectedPage._id
            ? findIndex(pagesPopulated, { _id: selectedPage._id })
            : findIndex(pagesPopulated, { name: selectedPage.name });

          const draftSelectedPage = pagesPopulated[pageIndex];
          const updatedItems = draftSelectedPage.screen[selectedScreenType].items;
          const index = findIndex(updatedItems, { id: state.selectedElement.id });

          let resultItems = handleMoveAction(updatedItems, index, result);
          draftSelectedPage.screen[selectedScreenType].items = resultItems;
          draft.selectedPage = draftSelectedPage;
        } else if (state.selectedDialog) {
          const selectedDialog = state.selectedDialog;
          const dialogsPopulated = draft.myProject.dialogsPopulated;

          const dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

          const draftSelectedDialog = dialogsPopulated[dialogIndex];
          const updatedItems = draftSelectedDialog.screen.items;
          const index = findIndex(updatedItems, { id: state.selectedElement.id });

          let resultItems = handleMoveAction(updatedItems, index, result);
          draftSelectedDialog.screen.items = resultItems;
          draft.selectedDialog = draftSelectedDialog;
        }
      });
      changes.push(...patches);
      inverseChanges.push(...inversePatches);
      return newState;
    }

    case `${DELETE_ELEMENT_FROM_PAGE}`: {
      const { index, element } = action.result;
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedPage = state.selectedPage;
        const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

        // Function to check if element is customized in a specific screen type
        const isElementCustomizedInScreenType = (page, elementId, screenType) => {
          if (!page.screen[screenType]?.items) return false;

          // Find element by reference ID
          const elementInScreenType = page.screen[screenType].items.find((item) => item.referenceId === elementId);
          if (!elementInScreenType) return false;

          // Check for customizations
          if (!Array.isArray(page.customisedElements)) return false;

          const customizedElement = page.customisedElements.find((ce) => ce.instanceId === elementInScreenType.id);

          // Return true only if element has customization data
          return !!(customizedElement?.customisedData?.length > 0);
        };

        let pageIndex;
        const pagesPopulated = draft.myProject.pagesPopulated;

        if (selectedPage._id) {
          pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
        } else {
          pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
        }
        const draftSelectedPage = pagesPopulated[pageIndex];

        // Delete from current screen type
        draftSelectedPage.screen[selectedScreenType].items.splice(index, 1);

        // If mobile or mobileLandscape view with liveReplicate, check other screen types
        if (
          (selectedScreenType === 'mobile' || selectedScreenType === 'mobileLandscape') &&
          draft.myProject.settings.liveReplicate === true
        ) {
          // Check for customizations in mobileLandscape, tablet and desktop
          const otherScreenTypes = ['mobileLandscape', 'tablet', 'desktop'];

          otherScreenTypes.forEach((screenType) => {
            // Only delete from screens where element is not customized
            if (!isElementCustomizedInScreenType(selectedPage, element.id, screenType)) {
              draftSelectedPage.screen[screenType].items = draftSelectedPage.screen[screenType].items.filter(
                (item) => item.referenceId !== element.id
              );
            }
          });
        }
        draft.selectedPage = draftSelectedPage;

        // Handle Theme page specific logic
        if (draft.selectedPage.name === '_Theme') {
          const {
            myProject: { reuse = [] }
          } = draft;
          const reuseElementIndex = findIndex(reuse, { id: element.id });
          if (reuseElementIndex > -1) reuse.splice(reuseElementIndex, 1);
          if (draft.reuseElementDraggedItem && draft.reuseElementDraggedItem.id === element.id) {
            draft.reuseElementDraggedItem = null;
          }
        }
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }
    case `${DELETE_ELEMENT_FROM_DIALOG}`: {
      const { result } = action;
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedDialog = state.selectedDialog;

        const dialogsPopulated = draft.myProject.dialogsPopulated;

        let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

        const draftSelectedDialog = dialogsPopulated[dialogIndex];

        draftSelectedDialog.screen.items.splice(result, 1);
        draft.selectedDialog = draftSelectedDialog;
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${MOVE_LAYERS_CONTAINER_ITEMS}`: {
      const { result } = action;
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const { selectedPage, myProject } = state;
        const selectedScreenType = getScreenType(selectedPage, myProject?.selectedScreenType);

        const pagesPopulated = draft.myProject.pagesPopulated;

        const pageIndex = selectedPage._id
          ? findIndex(pagesPopulated, { _id: selectedPage._id })
          : findIndex(pagesPopulated, { name: selectedPage.name });

        const draftSelectedPage = pagesPopulated[pageIndex];
        const pageItems = draftSelectedPage.screen[selectedScreenType].items;

        findAndMoveLayers(pageItems, result.containerId, result.layer, state.selectedElement.id);

        if (state.selectedDialog) {
          const selectedDialog = state.selectedDialog;
          const dialogsPopulated = draft.myProject.dialogsPopulated;
          const dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });
          const draftSelectedDialog = dialogsPopulated[dialogIndex];
          const dialogItems = draftSelectedDialog.screen.items;
          findAndMoveLayers(dialogItems, result.containerId, result.layer, state.selectedElement.id);

          draft.selectedDialog = draftSelectedDialog;
        }
        draft.selectedPage = draftSelectedPage;
      });
      changes.push(...patches);
      inverseChanges.push(...inversePatches);
      return newState;
    }

    case `${CHANGE_ELEMENT_LAYER}`: {
      const { itemId, newIndex, isDialog } = action.result;

      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedEntity = isDialog ? draft.selectedDialog : draft.selectedPage;
        const selectedPage = draft.selectedPage;
        const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

        let items;
        if (isDialog) {
          items = selectedEntity.screen.items || [];
        } else {
          const pageIndex = findIndex(draft.myProject.pagesPopulated, { _id: selectedEntity._id });
          const draftSelectedPage = draft.myProject.pagesPopulated[pageIndex];
          items = draftSelectedPage.screen[selectedScreenType].items || [];
        }

        moveItemInContainer(items, itemId, newIndex);

        if (isDialog) {
          draft.selectedDialog.screen.items = items;
        } else {
          const pageIndex = findIndex(draft.myProject.pagesPopulated, { _id: selectedEntity._id });
          draft.myProject.pagesPopulated[pageIndex].screen[selectedScreenType].items = items;
          draft.selectedPage = draft.myProject.pagesPopulated[pageIndex];
        }
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);
      return newState;
    }

    case `${CHANGE_ELEMENT_LAYER_INSIDE_CONTAINER}`: {
      const { itemId, newIndex, containerId, isDialog } = action.result;

      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedEntity = isDialog ? draft.selectedDialog : draft.selectedPage;
        const selectedPage = draft.selectedPage;
        const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

        let pageItems;
        if (isDialog) {
          pageItems = selectedEntity.screen.items || [];
        } else {
          const pageIndex = findIndex(draft.myProject.pagesPopulated, { _id: selectedEntity._id });
          const draftSelectedPage = draft.myProject.pagesPopulated[pageIndex];
          pageItems = draftSelectedPage.screen[selectedScreenType].items || [];
        }

        const container = find(pageItems, { id: containerId });
        if (container && container.items) {
          moveItemInContainer(container.items, itemId, newIndex);
        }

        if (isDialog) {
          draft.selectedDialog.screen.items = pageItems;
        } else {
          const pageIndex = findIndex(draft.myProject.pagesPopulated, { _id: selectedEntity._id });
          draft.myProject.pagesPopulated[pageIndex].screen[selectedScreenType].items = pageItems;
          draft.selectedPage = draft.myProject.pagesPopulated[pageIndex];
        }
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);
      return newState;
    }

    case `${DELETE_ELEMENT_FROM_CONTAINER}`: {
      const { result } = action;
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        if (state.selectedPage) {
          const selectedPage = state.selectedPage;
          const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

          const pagesPopulated = draft.myProject.pagesPopulated;
          let pageIndex = findIndex(pagesPopulated, { id: selectedPage.id });
          const draftSelectedPage = pagesPopulated[pageIndex];
          const pageItems = draftSelectedPage.screen[selectedScreenType].items;

          findAndDeleteByIndex(pageItems, result.containerId, result.deleteIndex);

          draft.selectedPage = draftSelectedPage;
        } else if (state.selectedDialog) {
          const selectedDialog = state.selectedDialog;
          const dialogsPopulated = draft.myProject.dialogsPopulated;
          let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });
          const draftSelectedDialog = dialogsPopulated[dialogIndex];
          const dialogItems = draftSelectedDialog.screen.items;

          findAndDeleteByIndex(dialogItems, result.containerId, result.deleteIndex);

          draft.selectedDialog = draftSelectedDialog;
        }
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${ADD_ELEMENT_TO_DIALOG}`: {
      const { result } = action;

      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedDialog = state.selectedDialog;

        const dialogsPopulated = draft.myProject.dialogsPopulated;

        let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

        const draftSelectedDialog = draft.myProject.dialogsPopulated[dialogIndex];
        draftSelectedDialog.screen.items = draftSelectedDialog.screen.items || [];
        draftSelectedDialog.screen.items.push(result);

        draft.selectedDialog = draftSelectedDialog;
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${ELEMENT_EVENT_CHANGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        let selectedElement = draft.selectedElement;
        if (!isEmpty(selectedElement)) {
          const selectedEventProps = action.result.selectedEventProps;
          if (selectedEventProps) {
            for (const key in selectedEventProps) {
              //eslint-disable-line no-unused-vars
              if (selectedEventProps[key] === 'None') {
                // delete any subkey that starts with the null key
                selectedEventProps && delete selectedEventProps[key]; //eslint-disable-line no-unused-expressions
                if (selectedElement.data?.eventProps) {
                  delete selectedElement.data.eventProps[key];
                }
                for (const subKey in selectedEventProps) {
                  //eslint-disable-line no-unused-vars
                  if (subKey.startsWith(`${key}__`)) {
                    delete selectedEventProps[subKey];
                    delete selectedElement.data.eventProps[subKey];
                  }
                }
              }
            }
            selectedElement.data.eventProps = selectedElement.data.eventProps || {};
            Object.assign(selectedElement.data.eventProps, selectedEventProps);
          }

          if (state.selectedPage) {
            const selectedPage = state.selectedPage;
            let pageIndex;
            const pagesPopulated = draft.myProject.pagesPopulated;

            if (selectedPage._id) {
              pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
            } else {
              pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
            }
            let draftSelectedPage = pagesPopulated[pageIndex];
            const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

            draftSelectedPage.screen[selectedScreenType] = updateElements(
              selectedPage.screen[selectedScreenType],
              selectedElement
            );

            if (selectedEventProps['onClick__targetDialog']) {
              const { dialogsPopulated = [] } = draft.myProject;
              draftSelectedPage.dialogs = draftSelectedPage.dialogs || [];
              const targetDialog = find(dialogsPopulated, { path: selectedEventProps['onClick__targetDialog'] });
              if (targetDialog && !draftSelectedPage.dialogs.includes(targetDialog.id)) {
                draftSelectedPage.dialogs.push(targetDialog.id);
              }
            }

            if (selectedElement?.referenceId) {
              draftSelectedPage = updateCustomisedData(selectedElement, draftSelectedPage, 'mobile', 'eventProps');
            } else if (selectedScreenType === 'mobile' || selectedScreenType === 'mobileLandscape') {
              const liveReplicate = state.myProject?.settings.liveReplicate === true;
              if (liveReplicate) {
                handleMobileScreenType(selectedElement, draftSelectedPage, 'eventProps');
              }
            }
            draft.selectedPage = draftSelectedPage;
          } else if (state.selectedDialog) {
            const selectedDialog = state.selectedDialog;

            const dialogsPopulated = draft.myProject.dialogsPopulated;

            let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

            const draftSelectedDialog = dialogsPopulated[dialogIndex];
            draftSelectedDialog.screen = updateElements(selectedDialog.screen, selectedElement);
            draft.selectedDialog = draftSelectedDialog;
          }
        }
      });

      return newState;
    }

    case CANVAS_DIMENSION: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.canvasDimension = action.result;
      });

      return newState;
    }

    case `${DELETE_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.deletedProject = null;
        draft.error = null;
      });

      return newState;
    }

    case `${DELETE_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.deletedProject = action.result;
        draft.error = null;
      });

      return newState;
    }

    case `${DELETE_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.deletedProject = null;
        draft.error = action.error;
      });

      return newState;
    }

    case `${CREATE_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectCreated = null;
        draft.error = null;
      });
      return newState;
    }

    case `${CREATE_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectCreated = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${CREATE_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectCreated = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${UPDATE_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.updatedProject = null;
        draft.error = null;
      });
      return newState;
    }

    case `${UPDATE_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.updatedProject = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${UPDATE_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.updatedProject = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${LIST_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projects = null;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projects = action.result;
        draft.orgProjects = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projects = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${LIST_ORG_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.orgProjects = null;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_ORG_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.orgProjects = action.result.projects;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_ORG_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.orgProjects = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${SET_LIVE_BASE_PATH}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.basePath = action.result || '';
      });
      return newState;
    }

    case `${GET_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject = null;
        draft.selectedBranch = null;
        draft.projectBranches = null;
        draft.branchCommits = null;
        draft.selectedCommit = null;
        draft.error = null;
      });
      return newState;
    }

    case `${GET_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject = action.result;
        draft.error = null;
        if (action.result.actions && action.result.actions.length) {
          draft.selectedAction = action.result.actions[0];
        }
      });
      return newState;
    }

    case `${GET_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject = null;
        draft.error = action.error;
      });
      return newState;
    }

    // branches

    case `${GET_BRANCH}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedBranch = null;
        draft.error = null;
      });
      return newState;
    }

    case `${GET_BRANCH}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.tags = action.result.tags;
        draft.selectedBranch = action.result;
        draft.myOriginalProject = draft.myProject;
        if (action.result && action.result.name === 'master') {
          draft.showCreateBranchDialog = false;
        }
        draft.error = null;
        lastChangeMasterData = [];
      });
      return newState;
    }

    case `${GET_BRANCH}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedBranch = null;
        draft.error = action.error;
      });
      return newState;
    }

    // set branch details null when changing
    case `${SET_BRANCH}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedBranch = action.result;
        draft.selectedElement = null;
      });
      return newState;
    }

    case `${LIST_BRANCHES}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectBranches = null;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_BRANCHES}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectBranches = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_BRANCHES}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectBranches = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${CREATE_BRANCH}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.newBranchCreated = null;
        draft.error = null;
      });
      return newState;
    }

    case `${CREATE_BRANCH}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.newBranchCreated = action.result;
        draft.selectedBranch = action.result;
        draft.selectedCommit = null;
        draft.showCreateBranchDialog = false;
        draft.error = null;
      });
      return newState;
    }

    case `${CREATE_BRANCH}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.newBranchCreated = null;
        draft.error = action.error;
      });
      return newState;
    }
    // ends

    // commits

    // set commit
    case `${SET_COMMIT}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedCommit = action.result;
      });
      return newState;
    }

    case `${GET_COMMIT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedCommit = null;
        draft.error = null;
      });
      return newState;
    }

    case `${GET_COMMIT}_RESULT`: {
      let [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.pagesPopulated = action.result.pages || [];
        draft.myProject.dialogsPopulated = action.result.dialogs || [];
        draft.myProject.reuse = action.result.reuse || [];
        draft.myProject.actions = action.result.actions || [];
        draft.myProject.variables = action.result.variables || [];
        draft.myProject.targets = action.result.targets || [];
        draft.myProject.instances = action.result.instances || [];
        draft.myProject.assets = action.result.assets || {};
        draft.myProject.i18nResources = action.result.i18nResources || {};
        draft.myProject.selectedScreenType = action.result.selectedScreenType;
        draft.myProject._commitId = action.result._id;

        draft.selectedCommit = action.result;
        draft.myOriginalProject = draft.myProject;
        draft.showCreateBranchDialog = false;
        draft.selectedElement = null;
        draft.selectedPage = null;
        draft.selectedDialog = null;
        draft.error = null;
      });

      // automatically update the last change made in master branch
      if (lastChangeMasterData.length > 0) {
        lastChangeMasterData.map((item) => {
          newState = projectReducer(newState, item);
        });
        lastChangeMasterData = [];
      }
      return newState;
    }

    case `${GET_COMMIT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedCommit = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${LIST_COMMITS}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.branchCommits = null;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_COMMITS}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.branchCommits = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${LIST_COMMITS}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.branchCommits = null;
        draft.error = action.error;
      });
      return newState;
    }

    // commits ends

    // merge

    case `${READY_TO_MERGE}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.readyToMerge = null;
      });
      return newState;
    }
    case `${READY_TO_MERGE}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.readyToMerge = action.result.ready;
      });
      return newState;
    }
    case `${READY_TO_MERGE}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.readyToMerge = action.error.ready;
      });
      return newState;
    }

    case `${MERGE_BRANCH}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.mergedStatus = null;
        draft.conflicts = null;
      });
      return newState;
    }
    case `${MERGE_BRANCH}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        if (SUCCESS_MESSAGES.includes(action.result.message)) {
          draft.selectedCommit = null;
          draft.selectedBranch = null;
          draft.selectedPage = null;
          draft.selectedDialog = null;
          draft.mergedStatus = action.result.message;
        } else {
          draft.mergedStatus = action.result.message;
        }
      });
      return newState;
    }
    case `${MERGE_BRANCH}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        if (action.error.message === 'Merge conflicts detected') {
          draft.conflicts = action.error.conflicts;
        } else {
          draft.mergedStatus = action.error;
        }
      });
      return newState;
    }

    // merge ends

    case `${GET_LIVE_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject = null;
        draft.error = null;
      });
      return newState;
    }
    case `${GET_LIVE_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject = action.result;
        draft.error = null;
      });
      return newState;
    }
    case `${GET_LIVE_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${SAVE_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.saveProject = null;
        draft.error = null;
        draft.conflicts = null;
      });
      return newState;
    }
    case `${SAVE_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.saveProject = action.result;
        draft.error = null;
      });
      return newState;
    }
    case `${SAVE_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        if (action.error.message === 'Merge conflicts detected') {
          draft.conflicts = action.error.conflicts;
          draft.currentBranchHasConflicts = true;
        } else {
          draft.saveProject = null;
          draft.error = action.error;
        }
      });
      return newState;
    }

    case `${IMPORT_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.saveProject = null;
        draft.error = null;
      });
      return newState;
    }
    case `${IMPORT_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.saveProject = action.result;
        draft.error = null;
      });
      return newState;
    }
    case `${IMPORT_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.saveProject = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${PUBLISH_TO_TARGET}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.publishToTarget = null;
        draft.error = null;
      });
      return newState;
    }
    case `${PUBLISH_TO_TARGET}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.publishToTarget = action.result;
        draft.error = null;
      });
      return newState;
    }
    case `${PUBLISH_TO_TARGET}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.publishToTarget = null;
        draft.error = action.error;
      });
      return newState;
    }

    case PROJECT_SELECTED: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedProject = action.result;
      });
      return newState;
    }

    case `${POST_VIDEO}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.assets = null;
        draft.error = null;
      });
      return newState;
    }

    case `${POST_VIDEO}_RESULT`: {
      if (!action.result || !Array.isArray(action.result)) {
        return state;
      }

      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.assets = draft.myProject.assets || {};
        draft.myProject.assets.videos = draft.myProject.assets.videos || [];
        draft.myProject.assets.videos.push(...action.result);
      });

      return newState;
    }

    case `${POST_VIDEO}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.assets = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${POST_IMAGE}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.assets = null;
        draft.error = null;
      });
      return newState;
    }

    case `${POST_IMAGE}_RESULT`: {
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        draft.myProject.assets = draft.myProject.assets || {};
        draft.myProject.assets.images = draft.myProject.assets.images || [];
        draft.myProject.assets.images.push(...action.result);
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${POST_IMAGE}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.assets = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${POST_FAVICON}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.assets = null;
        draft.error = null;
      });
      return newState;
    }
    case `${POST_FAVICON}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        if (draft.myProject.assets?.favIcons) {
          draft.myProject.assets.favIcons.unshift(...action.result);
        } else {
          if (!draft.myProject.assets) draft.myProject.assets = {};
          draft.myProject.assets.favIcons = [...action.result];
        }
      });
      return newState;
    }
    case `${POST_FAVICON}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.assets = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${ADD_ICON}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        if (draft.myProject.assets?.icons) {
          draft.myProject.assets.icons.unshift(action.result);
        } else {
          if (!draft.myProject.assets) draft.myProject.assets = {};
          draft.myProject.assets.icons = [action.result];
        }
      });
      return newState;
    }

    case UPDATE_VARIABLE: {
      const [newState] = produceWithPatches(state, (draft) => {
        const variables = draft.myProject.variables || [];
        if (action.result.type === 'NEW_VARIABLE') {
          variables.push(action.result.payload);
        } else {
          variables[action.result.index] = action.result.payload;
        }

        draft.myProject.variables = variables;
      });

      return newState;
    }

    case `${POPULATE_VARIABLE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.storage = draft.storage || {};
        set(draft.storage, action.result.name, action.result.value);
      });
      return newState;
    }

    case REMOVE_VARIABLE: {
      const [newState] = produceWithPatches(state, (draft) => {
        const variables = draft.myProject.variables || [];
        variables.splice(action.result, 1);
      });
      return newState;
    }

    case UPDATE_TARGET_VARIABLE: {
      const { type, payload, index } = action.result;
      const [newState] = produceWithPatches(state, (draft) => {
        const selectedTarget = draft.selectedTarget;
        const targets = draft.myProject.targets || [];
        const variables = selectedTarget.variables || [];
        const selectedTargetIndex = findIndex(targets, { name: selectedTarget?.name });
        // If type is 'NEW_VARIABLE', update variables in all targets
        if (type === 'NEW_VARIABLE') {
          if (selectedTargetIndex > -1) {
            selectedTarget.variables.push(payload);

            // For other targets, add a variable with an empty value
            for (const [index, target] of targets.entries()) {
              //eslint-disable-line no-unused-vars
              if (index !== selectedTargetIndex) {
                target.variables.push({
                  name: payload.name,
                  type: payload.type,
                  value: ''
                });
              }
            }
          }
        } else {
          variables[index] = action.result.payload;
        }

        if (selectedTargetIndex > -1) {
          draft.myProject.targets[selectedTargetIndex].variables = variables;
        }
      });
      return newState;
    }

    case `${POPULATE_TARGET_VARIABLE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.storage = draft.storage || {};
        draft.storage[action.result.name] = action.result.value;
      });
      return newState;
    }

    case REMOVE_TARGET_VARIABLE: {
      const [newState] = produceWithPatches(state, (draft) => {
        const selectedTargetIndex = draft.myProject.targets.findIndex(
          (target) => target.name === draft.selectedTarget.name
        );
        if (selectedTargetIndex > -1) {
          const variables = draft.myProject.targets[selectedTargetIndex].variables || [];
          variables.splice(action.result, 1);
          // Since selectedTarget is a shallow copy/reference to a target within myProject.targets,
          // the update on myProject.targets automatically reflects on selectedTarget.
        }
      });
      return newState;
    }

    case `${EXECUTE_ACTION_STARTED}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.inProgress = true;
        draft.eventName = action.result.eventName;
      });
      return newState;
    }

    case `${EXECUTE_ACTION_ENDED}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.inProgress = false;
      });
      return newState;
    }

    case `${ADD_ACTION}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const newAction = action.result;
        if (!draft.myProject.actions) {
          draft.myProject.actions = [];
        }
        draft.myProject.actions.push(newAction);
        draft.selectedAction = newAction;
      });
      return newState;
    }

    case `${UPDATE_ACTION}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const { actions: reorderedActions, steps: reorderedSteps, id: actionId, name } = action.result;
        if (reorderedActions && !actionId && !reorderedSteps && !name) {
          draft.myProject.actions = reorderedActions;
          return;
        }
        if (reorderedSteps && actionId && !name && !reorderedActions) {
          const actionIndex = draft.myProject.actions.findIndex((action) => action.id === actionId);

          if (actionIndex > -1) {
            draft.myProject.actions[actionIndex].steps = reorderedSteps;

            if (draft.selectedAction?.id === actionId) {
              draft.selectedAction.steps = reorderedSteps;
            }
          }
          return;
        }
        if (actionId && name && !reorderedActions && !reorderedSteps) {
          const actionIndex = draft.myProject.actions.findIndex((action) => action.id === actionId);

          if (actionIndex > -1) {
            draft.myProject.actions[actionIndex].name = name;

            if (draft.selectedAction?.id === actionId) {
              draft.selectedAction.name = name;
            }
          }
          return;
        }
      });
      return newState;
    }

    case `${SELECTED_ACTION}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedAction = action.result;
      });
      return newState;
    }

    case `${DELETE_ACTION}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const deletedAction = action.result;

        let actionIndex;
        const actions = draft.myProject.actions;

        if (deletedAction.id) {
          actionIndex = findIndex(actions, { id: deletedAction.id });
        } else {
          actionIndex = findIndex(actions, { name: deletedAction.name });
        }

        if (actionIndex > -1) {
          draft.myProject.actions.splice(actionIndex, 1);

          // Adjusting selectedAction based on the remaining actions
          if (draft.myProject.actions.length) {
            draft.selectedAction = draft.myProject.actions[Math.max(0, actionIndex - 1)];
          } else {
            draft.selectedAction = null;
          }
        }
      });
      return newState;
    }

    case `${ADD_STEP}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const steps = draft.selectedAction.steps || [];
        const { step, index } = action.result;
        steps.splice(index, 0, step);
        let actionIndex;
        const actions = draft.myProject.actions;
        const draftSelectedAction = draft.selectedAction;
        if (draftSelectedAction.id) {
          actionIndex = findIndex(actions, { id: draftSelectedAction.id });
        } else {
          actionIndex = findIndex(actions, { name: draftSelectedAction.name });
        }
        if (actionIndex > -1) {
          draft.myProject.actions[actionIndex].steps = steps;
        }
      });
      return newState;
    }

    case `${UPDATE_STEP}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const { stepIndex, questionAnswer } = action.result || {};
        let actionIndex;
        const actions = draft.myProject.actions;
        const draftSelectedAction = draft.selectedAction;

        if (draftSelectedAction.id) {
          actionIndex = findIndex(actions, { id: draftSelectedAction.id });
        } else {
          actionIndex = findIndex(actions, { name: draftSelectedAction.name });
        }

        if (stepIndex > -1) {
          draft.myProject.actions[actionIndex].steps[stepIndex] = questionAnswer;
          draft.selectedAction = draft.myProject.actions[actionIndex];
        }
      });
      return newState;
    }

    case `${DELETE_STEP}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const index = action.result;
        const steps = draft.selectedAction.steps || [];

        if (index > -1 && index < steps.length) {
          steps.splice(index, 1);
        }

        let actionIndex;
        const actions = draft.myProject.actions;
        const draftSelectedAction = draft.selectedAction;

        if (draftSelectedAction.id) {
          actionIndex = findIndex(actions, { id: draftSelectedAction.id });
        } else {
          actionIndex = findIndex(actions, { name: draftSelectedAction.name });
        }

        if (actionIndex > -1) {
          draft.myProject.actions[actionIndex].steps = steps;
        }
      });
      return newState;
    }

    case `${PAGE_EVENT_CHANGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const draftSelectedPage = draft.selectedPage;
        const selectedScreenType = getScreenType(draftSelectedPage, state.myProject?.selectedScreenType);

        const screen = draftSelectedPage.screen[selectedScreenType];

        if (!isEmpty(draftSelectedPage)) {
          const selectedEventProps = action.result.selectedEventProps;

          if (selectedEventProps) {
            screen.data.eventProps = screen.data.eventProps || {};
            Object.assign(screen.data.eventProps, selectedEventProps);
          }

          let pageIndex;
          const pagesPopulated = draft.myProject.pagesPopulated;

          if (draftSelectedPage._id) {
            pageIndex = findIndex(pagesPopulated, { _id: draftSelectedPage._id });
          } else {
            pageIndex = findIndex(pagesPopulated, { name: draftSelectedPage.name });
          }
          pagesPopulated[pageIndex] = draftSelectedPage;
        }
      });
      return newState;
    }

    case `${DIALOG_EVENT_CHANGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const eventProps = action.result.selectedEventProps || {};
        const selectedDialog = state.selectedDialog;

        const dialogsPopulated = draft.myProject.dialogsPopulated;

        let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

        const draftSelectedDialog = dialogsPopulated[dialogIndex];
        draftSelectedDialog.screen.data.eventProps = { ...eventProps };
        draft.selectedDialog = draftSelectedDialog;
        draft.selectedEventProps = action.result.selectedEventProps;
      });
      return newState;
    }

    case `${PAGE_PROPERTY_CHANGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const draftSelectedPage = draft.selectedPage;
        const selectedScreenType = getScreenType(draftSelectedPage, state.myProject?.selectedScreenType);

        const screen = draftSelectedPage.screen[selectedScreenType];

        if (!isEmpty(draftSelectedPage)) {
          const selectedPageProps = action.result.selectedPageProps;

          if (selectedPageProps) {
            if (selectedPageProps.items) {
              screen.items = selectedPageProps.items;
              delete selectedPageProps.items;
            }
            Object.assign(screen.data.renderProps, selectedPageProps);
          }

          let pageIndex;
          const pagesPopulated = draft.myProject.pagesPopulated;

          if (draftSelectedPage._id) {
            pageIndex = findIndex(pagesPopulated, { _id: draftSelectedPage._id });
          } else {
            pageIndex = findIndex(pagesPopulated, { name: draftSelectedPage.name });
          }
          pagesPopulated[pageIndex] = draftSelectedPage;
        }
      });
      return newState;
    }

    case `${ELEMENT_PROPERTY_CHANGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const selectedPage = state.selectedPage;
        if (selectedPage) {
          const { result } = action;
          let pageIndex;
          let selectedElement = result.element ? { ...result.element } : draft.selectedElement;
          if (selectedElement) {
            if (result.selectedElementProps) {
              const newRenderProps = { ...selectedElement.data.renderProps, ...result.selectedElementProps };
              const newData = { ...selectedElement.data, renderProps: newRenderProps };
              selectedElement.data = newData;
            }

            if (result.selectedElementDataProps) {
              Object.assign(selectedElement.data, result.selectedElementDataProps);
            }

            if (result.selectedElementRootProps) {
              Object.assign(selectedElement, result.selectedElementRootProps);
            }

            if (result.selectedElementDesignerProps !== undefined) {
              selectedElement.data.designerProps = result.selectedElementDesignerProps || '';
            }
            const pagesPopulated = draft.myProject.pagesPopulated;

            if (selectedPage._id) {
              pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
            } else {
              pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
            }
            let draftSelectedPage = pagesPopulated[pageIndex];
            const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);

            draftSelectedPage.screen[selectedScreenType] = updateElements(
              selectedPage.screen[selectedScreenType],
              selectedElement
            );

            if (draftSelectedPage.name === '_Theme') {
              const {
                selectedElement,
                myProject: { pagesPopulated = [], dialogsPopulated = [], reuse = [] }
              } = draft;
              const themedElementProps = result.selectedElementProps;

              for (const eachPage of pagesPopulated) {
                //eslint-disable-line no-unused-vars
                if (eachPage.name !== '_Theme') {
                  for (const screenType of Object.keys(eachPage.screen)) {
                    //eslint-disable-line no-unused-vars
                    const items = eachPage.screen[screenType]?.items || [];
                    if (items && items.length) {
                      for (let i = 0; i < items.length; i++) {
                        items[i] = updateElementProps(items[i], selectedElement, themedElementProps);
                      }
                    }
                  }
                }
              }

              for (const eachPage of dialogsPopulated) {
                //eslint-disable-line no-unused-vars
                const items = eachPage.screen.items || [];
                for (let i = 0; i < items.length; i++) {
                  items[i] = updateElementProps(items[i], selectedElement, themedElementProps);
                }
              }

              for (const item of reuse) {
                //eslint-disable-line no-unused-vars
                if (item?.id === selectedElement?.id) {
                  Object.assign(item.data.renderProps, themedElementProps);
                  break;
                }
              }
            }

            if (selectedElement?.referenceId) {
              draftSelectedPage = updateCustomisedData(selectedElement, draftSelectedPage, 'mobile');
            } else if (selectedScreenType === 'mobile' || selectedScreenType === 'mobileLandscape') {
              const liveReplicate = state.myProject?.settings.liveReplicate === true;
              if (liveReplicate) {
                handleMobileScreenType(selectedElement, draftSelectedPage);
              }
            }
            draft.selectedElement = selectedElement;
            draft.selectedPage = draftSelectedPage;
          }
        } else if (state.selectedDialog) {
          const selectedDialog = state.selectedDialog;
          const { result } = action;
          let selectedElement = result.element ? { ...result.element } : draft.selectedElement;
          if (selectedElement) {
            if (result.selectedElementProps) {
              const newRenderProps = { ...selectedElement.data.renderProps, ...result.selectedElementProps };
              const newData = { ...selectedElement.data, renderProps: newRenderProps };
              selectedElement.data = newData;
            }

            if (result.selectedElementDataProps) {
              Object.assign(selectedElement.data, result.selectedElementDataProps);
            }

            if (result.selectedElementRootProps) {
              Object.assign(selectedElement, result.selectedElementRootProps);
            }
            const dialogsPopulated = draft.myProject.dialogsPopulated;

            let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

            const draftSelectedDialog = dialogsPopulated[dialogIndex];
            draftSelectedDialog.screen = updateElements(selectedDialog.screen, selectedElement);
            draft.selectedElement = selectedElement;
            draft.selectedDialog = draftSelectedDialog;
          }
        }
      });
      return newState;
    }

    case `${DIALOG_PROPERTY_CHANGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const selectedDialog = state.selectedDialog;
        if (selectedDialog) {
          const selectedDialogProps = action.result.selectedDialogProps || {};

          const dialogsPopulated = draft.myProject.dialogsPopulated;

          let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

          const draftSelectedDialog = dialogsPopulated[dialogIndex];
          draftSelectedDialog.screen.data.renderProps = Object.assign(
            draftSelectedDialog.screen.data.renderProps,
            selectedDialogProps
          );

          draft.selectedDialogProps = action.result.selectedDialogProps;
          draft.selectedDialog = draftSelectedDialog;
        }
      });
      return newState;
    }

    case `${ACTIONS_TEST_URL}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.testResult = null;
        draft.error = null;
      });
      return newState;
    }

    case `${ACTIONS_TEST_URL}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.testResult = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${ACTIONS_TEST_URL}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.testResult = null;
        draft.error = action.error;
      });
      return newState;
    }

    case ADD_ELEMENT_TO_REUSE: {
      const [newState] = produceWithPatches(state, (draft) => {
        const { selectedScreenType = 'desktop', pagesPopulated, dialogsPopulated, reuse = [] } = draft.myProject;
        const newElement = { ...action.result, id: randstr(`${action.result.data.id}`) };
        if (newElement.items) delete newElement.items;
        reuse.push(newElement);

        const themePageIndex = findIndex(pagesPopulated, { name: '_Theme' });
        if (themePageIndex !== -1) {
          pagesPopulated[themePageIndex].screen['desktop'].items.push(newElement);
        }

        const target = draft.selectedPage ? pagesPopulated : dialogsPopulated;
        const key = draft.selectedPage ? 'selectedPage' : 'selectedDialog';
        const selected = draft[key];

        let pageIndex = findIndex(target, (item) => item._id === selected?._id || item.name === selected?.name);

        if (pageIndex !== -1) {
          Object.assign(draft.selectedElement.data, { reuseId: newElement.id });
          if (draft.selectedPage) {
            target[pageIndex].screen[selectedScreenType] = updateElements(
              selected.screen[selectedScreenType],
              draft.selectedElement
            );
          } else {
            target[pageIndex].screen = updateElements(selected.screen, draft.selectedElement);
          }
          draft[key] = target[pageIndex];
        }
      });
      return newState;
    }

    case UPDATE_INSTANCES: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.pagesPopulated = action.result;
      });
      return newState;
    }

    case UPDATE_DIALOG_INSTANCES: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.dialogsPopulated = action.result;
      });
      return newState;
    }

    case TOGGLE_PREVIEW_DIALOG: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.toggleDialog = action.result;
      });
      return newState;
    }

    case `${UPDATE_ASSETS}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        if (!draft.myProject.assets) {
          draft.myProject.assets = {};
        }
        if (action.result.assetType === 'fonts') {
          const fonts = draft.myProject.assets?.fonts || [];
          if (action.result.opType === 'delete') {
            fonts.splice(action.result.index, 1);
          } else {
            fonts.push(action.result.font);
          }
          draft.myProject.assets.fonts = fonts;
        } else if (action.result.assetType === 'icons') {
          const icons = draft.myProject.assets?.icons || [];
          if (action.result.opType === 'delete') {
            icons.splice(action.result.index, 1);
          }
          draft.myProject.assets.icons = icons;
        } else if (action.result.assetType === 'images') {
          const images = draft.myProject.assets?.images || [];
          if (action.result.opType === 'delete') {
            images.splice(action.result.index, 1);
          }
          draft.myProject.assets.images = images;
        } else if (action.result.assetType === 'favicons') {
          const favIcons = draft.myProject.assets?.favIcons || [];
          if (action.result.opType === 'delete') {
            favIcons.splice(action.result.index, 1);
          }
          draft.myProject.assets.favIcons = favIcons;
        } else if (action.result.assetType === 'videos') {
          const videos = draft.myProject.assets?.videos || [];
          if (action.result.opType === 'delete') {
            videos.splice(action.result.index, 1);
          } else {
            videos.push(action.result.video);
          }
          draft.myProject.assets.videos = videos;
        }
      });

      return newState;
    }

    case `${GET_PROJECT_VERSIONS}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectVersions = null;
        draft.error = null;
      });
      return newState;
    }

    case `${GET_PROJECT_VERSIONS}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectVersions = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${GET_PROJECT_VERSIONS}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.projectVersions = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${ADD_ELEMENT_DRAGGED_ITEM}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.addElementDraggedItem = action.result;
      });
      return newState;
    }

    case `${SET_OPEN_CONTAINER}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.addElementContainer = action.result;
      });
      return newState;
    }

    case `${SET_OPEN_INPUT}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.addElementInput = action.result;
      });
      return newState;
    }

    case `${SET_OPEN_OTHERS}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.addElementOther = action.result;
      });
      return newState;
    }

    case `${SET_OPEN_MUI}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.addElementMUI = action.result;
      });
      return newState;
    }

    case `${SET_OPEN_PEGA}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.addElementPega = action.result;
      });
      return newState;
    }

    case `${REUSE_ELEMENT_DRAGGED_ITEM}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.reuseElementDraggedItem = action.result;
      });
      return newState;
    }

    case `${DOUBLE_CLICK_ELEMENT}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.doubleClickElement = action.result;
      });
      return newState;
    }

    case `${DOUBLE_CLICK_PAGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.doubleClickPage = action.result;
      });
      return newState;
    }

    case `${ADD_TARGET}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const newTarget = action.result;
        if (!draft.myProject.targets) {
          draft.myProject.targets = [];
        }
        draft.myProject.targets.push(newTarget);
        // If there are existing targets and if the first target has variables,
        // copy its variables to the new target
        if (draft.myProject.targets.length > 1 && draft.myProject.targets[0].variables) {
          const firstTargetVariables = draft.myProject.targets[0].variables;
          newTarget.variables = firstTargetVariables.map((variable) => ({ name: variable.name, type: variable.type }));
        }
      });
      return newState;
    }

    case `${UPDATE_TARGET}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const updatedTarget = action.result;
        const index = draft.myProject.targets.findIndex((target) => target.name === draft.selectedTarget.name);
        if (index > -1) {
          draft.myProject.targets[index] = updatedTarget;
          draft.selectedTarget = updatedTarget;
        }
      });
      return newState;
    }

    case `${SELECTED_TARGET}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedTarget = action.result;
      });
      return newState;
    }

    case `${DELETE_TARGET}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const deletedTarget = action.result;
        const index = draft.myProject.targets.findIndex((target) => target.name === deletedTarget.name);
        if (index > -1) {
          draft.myProject.targets.splice(index, 1);

          // Update selectedTarget after deletion
          if (draft.myProject.targets.length > 0) {
            draft.selectedTarget = index === 0 ? draft.myProject.targets[0] : draft.myProject.targets[index - 1];
          } else {
            draft.selectedTarget = null;
          }
        }
      });
      return newState;
    }

    case `${ADD_DOMAIN}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const domains = draft.selectedTarget.domains || [];
        domains.push(action.result);

        const targetIndex = draft.myProject.targets.findIndex((target) => target.name === draft.selectedTarget.name);
        if (targetIndex > -1) {
          draft.myProject.targets[targetIndex].domains = domains;
        }
      });
      return newState;
    }

    case `${DELETE_DOMAIN}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const index = action.result;

        if (index >= 0 && index < draft.selectedTarget.domains.length) {
          draft.selectedTarget.domains.splice(index, 1);
        }

        const targetIndex = draft.myProject.targets.findIndex((target) => target.name === draft.selectedTarget.name);
        if (targetIndex > -1) {
          draft.myProject.targets[targetIndex].domains = draft.selectedTarget.domains;
        }
      });
      return newState;
    }

    case `${COPY_ELEMENTS_SCREENTYPE}`: {
      const [newState, patches] = produceWithPatches(state, (draft) => {
        const { screenType, items } = action.result;
        const selectedPage = draft.selectedPage;

        const myProject = draft.myProject;
        const selectedScreenType = getScreenType(selectedPage, myProject?.selectedScreenType);

        const selectedModule = selectedPage.screen[selectedScreenType];

        if (!selectedModule.items) selectedModule.items = []; // Ensure items array exists

        items.forEach((item) => {
          const clonedItem = cloneDeep(item);
          clonedItem.resync = {
            parentId: item.id,
            parentScreen: screenType
          };
          clonedItem.data.x = clonedItem.data.y = items.indexOf(item);
          clonedItem.id = randstr(`${clonedItem.data.id}_`);
          if (clonedItem?.source.type === 'CONTAINER') {
            clonedItem.data.renderProps.items = item.data.renderProps.items?.map((innerItem) => {
              const clonedInnerItem = cloneDeep(innerItem);
              clonedInnerItem.id = randstr(`${clonedInnerItem.data.id}_`);
              return clonedInnerItem;
            });
          }
          selectedModule.items.push(clonedItem);

          let pageIndex;
          const pagesPopulated = draft.myProject.pagesPopulated;

          if (selectedPage._id) {
            pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
          } else {
            pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
          }

          draft.myProject.pagesPopulated[pageIndex] = selectedPage;
        });
      });

      changes.push(...patches);

      return newState;
    }

    case `${RESYNC_ELEMENTS}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const items = action.result;
        const selectedPage = draft.selectedPage;
        const myProject = draft.myProject;
        const selectedScreenType = getScreenType(selectedPage, myProject?.selectedScreenType);

        for (const item of items) {
          //eslint-disable-line no-unused-vars
          if (item.resync.parentId && item.resync.parentScreen) {
            //eslint-disable-line no-unused-vars
            const parentScreenItems = selectedPage?.screen[item.resync.parentScreen]?.items;
            const presentItems = selectedPage?.screen[selectedScreenType]?.items;

            const parentItem = parentScreenItems?.find((parent) => parent.id === item.resync.parentId);
            const data = presentItems?.find((presentItem) => presentItem.id === item.id);
            const index = presentItems?.indexOf(data);
            if (parentItem && data && index !== undefined && index > -1) {
              //eslint-disable-line no-unused-vars
              if (parentItem && parentItem.source.type === 'CONTAINER') {
                const { items: innerItems, ...restRenderProps } = parentItem.data.renderProps; //eslint-disable-line no-unused-vars
                presentItems[index].data.renderProps = { ...restRenderProps, items: data.data.renderProps.items };
              } else {
                presentItems[index].data.renderProps = parentItem.data.renderProps;
              }
            }
          }
        }

        let pageIndex;
        const pagesPopulated = draft.myProject.pagesPopulated;

        if (selectedPage._id) {
          pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
        } else {
          pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
        }

        draft.myProject.pagesPopulated[pageIndex] = selectedPage;
      });

      return newState;
    }

    case `${COPY_TO_SELECTEDPAGES}`: {
      const { result } = action;
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedElement = draft.selectedElement;
        const selectedPage = draft.selectedPage;
        const updatedProject = draft.myProject;
        const screenType = getScreenType(selectedPage, updatedProject?.selectedScreenType);
        const otherPages = updatedProject.pagesPopulated;

        for (const each of otherPages) {
          //eslint-disable-line no-unused-vars
          if (each.id !== selectedPage.id && result.includes(each.id)) {
            const screen = each.screen[screenType];
            let copiedElement = null;
            for (const item of screen.items) {
              //eslint-disable-line no-unused-vars
              if (item.copiedFrom === selectedElement.id || item.id === selectedElement.copiedFrom) {
                copiedElement = item;
                copiedElement.data.renderProps = selectedElement.data.renderProps;
                break;
              }
            }
            if (!copiedElement) {
              const newElementId = randstr(`${selectedElement.data.id}_`);
              copiedElement = cloneDeep(selectedElement);
              copiedElement.id = newElementId;
              copiedElement.copiedFrom = selectedElement.id;
              screen.items.push(copiedElement);
            }
          }
        }
      });
      changes.push(...patches);
      inverseChanges.push(...inversePatches);
      return newState;
    }

    case `${EXPORT_PROJECT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.exportProject = null;
        draft.error = null;
      });

      return newState;
    }

    case `${EXPORT_PROJECT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.exportProject = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${EXPORT_PROJECT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.exportProject = null;
        draft.error = action.error;
      });

      return newState;
    }

    case UPDATE_PAGE_TITLE: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.pageTitle = action.result;
      });
      return newState;
    }

    case `${SET_PROJECT}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject = action.result;
      });
      return newState;
    }

    case `${SET_PAGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedPage = action.result;
      });
      return newState;
    }

    case `${SET_INITIAL_PAGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedPage = {
          ...action.result.selectedPage,
          resolvedPath: action.result.resolvedPath
        };
      });
      return newState;
    }

    case `${SET_DIALOG}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.selectedDialog = action.result;
      });
      return newState;
    }

    case `${EDIT_DIALOG}`: {
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const { dialog, editDetails } = action.result;
        const dialogsPopulated = draft.myProject.dialogsPopulated;
        let dialogIndex = findIndex(dialogsPopulated, { id: dialog.id });
        dialogsPopulated[dialogIndex] = Object.assign({}, dialogsPopulated[dialogIndex], editDetails);
      });

      changes.push(...patches);
      inverseChanges.push(...inversePatches);

      return newState;
    }

    case `${LOAD_SETTINGS}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.settings = null;
        draft.error = null;
      });
      return newState;
    }

    case `${LOAD_SETTINGS}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.settings = action.result;
        draft.myProject.settings = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${LOAD_SETTINGS}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = action.error;
      });
      return newState;
    }

    case `${SAVE_ACCESS_MGMT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = null;
      });
      return newState;
    }

    case `${SAVE_ACCESS_MGMT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.myProject.settings = action.result.settings;
        draft.error = null;
      });
      return newState;
    }

    case `${SAVE_ACCESS_MGMT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = action.error;
      });
      return newState;
    }

    case `${SAVE_SETTINGS}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = null;
      });
      return newState;
    }

    case `${SAVE_SETTINGS}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.settings = action.result.settings;
        draft.myProject.settings = action.result.settings;
        draft.error = null;
      });
      return newState;
    }

    case `${SAVE_SETTINGS}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = action.error;
      });
      return newState;
    }

    case `${LOAD_SITEMAP}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = null;
        draft.error = null;
      });
      return newState;
    }

    case `${LOAD_SITEMAP}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${LOAD_SITEMAP}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${SAVE_SITEMAP}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = null;
      });
      return newState;
    }

    case `${SAVE_SITEMAP}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = action.result;
        draft.error = null;
      });
      return newState;
    }

    case `${SAVE_SITEMAP}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = action.error;
      });
      return newState;
    }

    case `${MODIFY_SITEMAP_PAGES}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = state.sitemap || {};
        draft.sitemap.pages = action.result;
      });
      return newState;
    }

    case `${ADD_SITEMAP_CUSTOM_LINK}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = state.sitemap || {};
        draft.sitemap.pages = draft.sitemap.pages || [];
        draft.sitemap.pages.push(action.result);
      });
      return newState;
    }

    case `${ADD_SITEMAP_CMS_FILTER_CONDITIONS}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = state.sitemap || {};
        draft.sitemap.filters = draft.sitemap.filters || [];
        let cmsFilterExistIndex = findIndex(draft.sitemap.filters, { cmsTypeId: action.result.cmsTypeId });
        if (cmsFilterExistIndex > -1) {
          draft.sitemap.filters.splice(cmsFilterExistIndex, 1, action.result);
        } else {
          draft.sitemap.filters.push(action.result);
        }
      });
      return newState;
    }

    case `${MODIFY_ROOT_DOMAIN}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = state.sitemap || {};
        draft.sitemap.rootDomain = action.result;
      });
      return newState;
    }

    case `${UPDATE_PAGE_SETTINGS}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const { settings, selectedPage } = action.result;

        const pagesPopulated = draft.myProject.pagesPopulated;
        let pageIndex = findIndex(pagesPopulated, { id: selectedPage.id });

        if (pageIndex > -1) {
          draft.myProject.pagesPopulated[pageIndex].settings = settings;
          if (draft.selectedPage && draft.selectedPage.id === selectedPage.id) {
            draft.selectedPage.settings = settings;
          }
        }
      });
      return newState;
    }

    case `${VIEWPORT_POSITION}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.viewport = action.result;
      });
      return newState;
    }

    case `${ELEMENT_HEIGHT_CHANGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const selectedPage = draft.selectedPage;
        const selectedScreenType = action.result.screenType || 'desktop';
        const items = selectedPage.screen[selectedScreenType].items || [];
        const item = find(items, { id: action.result.id });
        if (item) {
          item.contentHeight = action.result.contentHeight;
        }
        draft.selectedPage = selectedPage;
      });
      return newState;
    }

    case `${LAST_ELEMENT_PAGE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.lastElementPage = action.result;
      });
      return newState;
    }

    case `${LAST_ELEMENT_DIALOG}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.lastElementDialog = action.result;
      });
      return newState;
    }

    case `${LAST_ELEMENT_SELECTED_ELEMENT}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.lastElementSelectedElement = action.result;
      });
      return newState;
    }

    case `${COLLAPSIBLE_NAV_CONTENT}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.collapsibleContent = action.result;
      });
      return newState;
    }

    case `${NAV_COLLAPSIBLE}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.navCollapsible = action.result;
      });
      return newState;
    }

    case `${GET_CMS_LINKS}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = null;
      });
      return newState;
    }

    case `${GET_CMS_LINKS}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        const cmsTypeId = action.result && action.result._id;
        draft.sitemap = draft.sitemap || {};
        draft.sitemap.pages = draft.sitemap.pages || [];
        if (draft.sitemap.pages.length) {
          for (let i = draft.sitemap.pages.length - 1; i >= 0; i--) {
            if (draft.sitemap.pages[i]._id === cmsTypeId) {
              draft.sitemap.pages.splice(i, 1);
            }
          }
        }
        draft.sitemap.pages.push(...action.result.constructedLinksArray);
        draft.error = null;
      });
      return newState;
    }

    case `${GET_CMS_LINKS}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = action.error;
      });
      return newState;
    }

    case `${RESYNC_CMS_LINKS}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = null;
      });
      return newState;
    }

    case `${RESYNC_CMS_LINKS}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.sitemap = draft.sitemap || {};
        draft.sitemap.pages = draft.sitemap.pages || [];

        if (action.result.resyncAll) {
          for (let i = draft.sitemap.pages.length - 1; i >= 0; i--) {
            if (draft.sitemap.pages[i].linkType === 'cms-link') {
              draft.sitemap.pages.splice(i, 1);
            }
          }
          draft.sitemap.pages.push(...action.result.constructedLinksArray);
        } else {
          const cmsTypeId = action.result && action.result._id;
          if (draft.sitemap.pages.length) {
            for (let i = draft.sitemap.pages.length - 1; i >= 0; i--) {
              if (draft.sitemap.pages[i]._id === cmsTypeId) {
                draft.sitemap.pages.splice(i, 1);
              }
            }
          }
          draft.sitemap.pages.push(...action.result.constructedLinksArray);
        }
        draft.error = null;
      });
      return newState;
    }

    case `${RESYNC_CMS_LINKS}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = action.error;
      });
      return newState;
    }

    case `${PUBLISH_PEGA_COMPONENT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        // draft.selectedPage = null;
        draft.error = null;
        draft.pegaPublishStatus = null;
      });
      return newState;
    }
    case `${PUBLISH_PEGA_COMPONENT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.pegaPublishStatus = action.result;
      });
      return newState;
    }
    case `${PUBLISH_PEGA_COMPONENT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.pegaPublishStatus = null;
        draft.error = action.error;
      });
      return newState;
    }

    case `${LIST_PEGA_COMPONENT}_REQUEST`: {
      const [newState] = produceWithPatches(state, (draft) => {
        // draft.selectedPage = null;
        draft.error = null;
        draft.pegaComponents = null;
      });
      return newState;
    }
    case `${LIST_PEGA_COMPONENT}_RESULT`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.pegaComponents = action.result;
      });
      return newState;
    }
    case `${LIST_PEGA_COMPONENT}_ERROR`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.error = action.error;
      });
      return newState;
    }

    case `${CONTAINER_VIEWPORT_POSITION}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        draft.containerViewport = action.result;
      });
      return newState;
    }

    case `${UPDATE_AMIMATIONS}`: {
      const [newState] = produceWithPatches(state, (draft) => {
        let selectedElement = draft.selectedElement;
        if (!isEmpty(selectedElement)) {
          const selectedAnimationProps = action.result;
          if (selectedAnimationProps) {
            let newAnimationProps = { ...selectedElement.data.animationProps };

            // Iterate through the selectedAnimationProps and update the animation properties
            Object.keys(selectedAnimationProps).forEach((event) => {
              if (!newAnimationProps[event]) {
                newAnimationProps[event] = {};
              }
              Object.assign(newAnimationProps[event], selectedAnimationProps[event]);
            });
            selectedElement.data.animationProps = newAnimationProps;
          }
        }
        if (state.selectedPage) {
          const selectedPage = state.selectedPage;
          let pageIndex;
          const pagesPopulated = draft.myProject.pagesPopulated;

          if (selectedPage._id) {
            pageIndex = findIndex(pagesPopulated, { _id: selectedPage._id });
          } else {
            pageIndex = findIndex(pagesPopulated, { name: selectedPage.name });
          }

          const draftSelectedPage = pagesPopulated[pageIndex];
          const selectedScreenType = getScreenType(selectedPage, state.myProject?.selectedScreenType);
          draftSelectedPage.screen[selectedScreenType] = updateElements(
            selectedPage.screen[selectedScreenType],
            selectedElement
          );

          draft.selectedPage = draftSelectedPage;
        } else if (state.selectedDialog) {
          const selectedDialog = state.selectedDialog;
          const dialogsPopulated = draft.myProject.dialogsPopulated;

          let dialogIndex = findIndex(dialogsPopulated, { id: selectedDialog.id });

          const draftSelectedDialog = dialogsPopulated[dialogIndex];
          draftSelectedDialog.screen = updateElements(selectedDialog.screen, selectedElement);
          draft.selectedDialog = draftSelectedDialog;
        }
      });

      return newState;
    }

    case `${COPY_TO_SELECTED_DIALOGS}`: {
      const { result } = action;
      const [newState, patches, inversePatches] = produceWithPatches(state, (draft) => {
        const selectedElement = draft.selectedElement;
        const selectedDialog = draft.selectedDialog;
        const updatedProject = draft.myProject;
        const otherDialogs = updatedProject.dialogsPopulated;

        for (const each of otherDialogs) {
          //eslint-disable-line no-unused-vars
          if (each.id !== selectedDialog.id && result.includes(each.id)) {
            const screen = each.screen;
            let copiedElement = null;
            for (const item of screen.items) {
              //eslint-disable-line no-unused-vars
              if (item.copiedFrom === selectedElement.id || item.id === selectedElement.copiedFrom) {
                copiedElement = item;
                copiedElement.data.renderProps = selectedElement.data.renderProps;
                break;
              }
            }
            if (!copiedElement) {
              const newElementId = randstr(`${selectedElement.data.id}_`);
              copiedElement = cloneDeep(selectedElement);
              copiedElement.id = newElementId;
              copiedElement.copiedFrom = selectedElement.id;
              screen.items.push(copiedElement);
            }
          }
        }
      });
      changes.push(...patches);
      inverseChanges.push(...inversePatches);
      return newState;
    }

    default:
      return state;
  }
};

/* eslint-enable no-param-reassign */

export default projectReducer;
