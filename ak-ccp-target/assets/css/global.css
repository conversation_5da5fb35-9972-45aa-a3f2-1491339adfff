* {
  box-sizing: border-box;
}

#nav-dropdown {
  color: white !important;
}

.MuiPopover-paper {
  box-shadow: none !important;
  margin-left: 3px;
}
.hover-pages {
  border: 2px solid #f1f1f1 !important;
  list-style: none !important;
  margin-bottom: 12px !important;
  padding: 6px 10px 6px 10px !important;
  border-radius: 6px !important;
  font-size: 15px !important;
  color: #525252 !important;
  cursor: pointer !important;
}
.hover-pages:hover {
  background-color: #ecf5fe;
}

#draggable-icon {
  cursor: move;
}

#MuiPopover-icons-box {
  border: 2px solid #f1f1f1 !important;
  padding: 3px 10px 3px 10px !important;
  border-radius: 6px !important;
  font-size: 15px !important;
  color: #525252 !important;
}

#border-radius-7px {
  border-radius: 7px !important;
}
#add-elements-font-weight {
  font-weight: 900 !important;
}

#scrolling-bar-add-elements {
  height: auto;
  max-height: calc(100vh - 100px);
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

#scrolling-bar-add-elements::-webkit-overflow-scrolling {
  -webkit-overflow-scrolling: scroll;
}

#scrolling-bar-add-elements::-webkit-scrollbar {
  width: 6px;
  -webkit-overflow-scrolling: touch;
}

#scrolling-bar-add-elements::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 100vh;
}

#scrolling-bar-add-elements::-webkit-scrollbar-thumb {
  background: #a2a2a2;
  border-radius: 100vh;
}

#scrolling-bar-add-elements-full {
  height: auto;
  max-height: calc(100vh - 60px);
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

#scrolling-bar-add-elements-full::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

#scrolling-bar-add-elements-full::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 100vh;
}

#scrolling-bar-add-elements-full::-webkit-scrollbar-thumb {
  background: #a2a2a2;
  border-radius: 100vh;
}

.button-group {
  margin: 8px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 10px 8px 10px;
  outline: none;
  background-color: transparent;
  border-radius: 8px;
  font-size: 17px;
  border: 0px solid;
  font-weight: 400;
  color: #000000;
  cursor: grab !important;
}

.button-group-primary {
  background-color: deepskyblue;
  color: #ffffff;
}
.button-group-normal {
  box-shadow: 1px 3px 4px 2px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
}
.button-group-outline {
  border: 2px solid deepskyblue;
}

.button-group-unactive {
  background-color: rgba(228, 228, 228, 255);
  color: rgba(199, 198, 198, 255);
}

.button-group-unactive-outline {
  border: 2px solid rgba(228, 228, 228, 255);
  color: rgba(199, 198, 198, 255);
}

#select-button-types {
  width: 100%;
  outline: none;
  border: 1px solid #dbdbdb;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px !important;
  font-weight: 400;
  padding: 7px 10px 7px 10px !important ;
}

.scrolling-bar-add-assets {
  height: auto;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.scrolling-bar-add-assets::-webkit-overflow-scrolling {
  -webkit-overflow-scrolling: scroll;
}

.scrolling-bar-add-assets::-webkit-scrollbar {
  width: 12px;
  -webkit-overflow-scrolling: touch;
}

.scrolling-bar-add-assets::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 100vh;
}

.scrolling-bar-add-assets::-webkit-scrollbar-thumb {
  background: #a2a2a2;
  border-radius: 100vh;
}

.custom-scroll-dropdown {
  height: auto;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll-dropdown::-webkit-overflow-scrolling {
  -webkit-overflow-scrolling: scroll;
}

.custom-scroll-dropdown::-webkit-scrollbar {
  width: 4px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll-dropdown::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 100vh;
}

.custom-scroll-dropdown::-webkit-scrollbar-thumb {
  background: #a2a2a2;
  border-radius: 100vh;
}

.mui-popover-more-options {
  box-shadow: 10px 30px 40px 20px rgba(0, 0, 0, 0.2) !important;
  border-radius: 7px;
}

.hover-options:hover {
  background-color: #f4f5ed;
  background-size: cover;
}

.hover-sidemenu-options:hover {
  color: #0074F1 !important;
  background-color: transparent !important;
  fill: #0074F1 !important;
}

.fillSelectedIcon svg {
  fill: #0074F1;
}

.hover-sidemenu-options:hover svg {
  fill: #0074F1;
}

.custom-collapse {
  flex: 1 0 auto !important;
}

.btn-hover-effect {
  transition: filter .6s ease-in-out;
}

.btn-hover-effect:hover {
  filter: brightness(85%);
}

.custom-button:hover {
  background-color: color-mix(in oklab, var(--fallback-background) 90%, black) !important;
}

.file-input-custom::file-selector-button:hover {
  background-color: color-mix(in oklab, #0074F1 90%, black) !important;
}

/* property.json */
.property-styling {
  display: flex !important;
  flex-direction: column !important;
  padding-left: 7px;
  padding-right: 7px;
  padding-top: 5px;
  margin-left: 10px;
  margin-right: 10px;
}

.data-property-styling {
  display: flex !important;
  background-color: #Dce1e1;
  padding: 8px 0 5px 10px;
  border-radius: 4px;
}

.property-input-label {
  width: 40% !important;
  font-weight: 600 !important;
  padding-top: 15px !important;
  padding-left: 5px !important;
}

.property-input-field {
  width: 210px !important;
  height: 50px;
  border-radius: 5px;
  border: 1px solid grey;
  padding: 5px;
}

.property-select-option {
  width: 162px !important;
  background-color: white !important;
}

.select-option {
  width: 250px !important;
  background-color: white !important;
}

.input-label {
  width: auto !important;
  font-weight: 600 !important;
}

.input-field {
  width: 100%;
  height: 50px;
  border-radius: 5px;
  border: 1px solid grey;
  padding: 5px;
}

.input-height {
  height: 32px;
  outline: none;
}

.delete-container .delete-button {
  visibility: hidden;
}

.delete-container:hover img {
  transition: filter 0.3s ease-in-out;
  filter: brightness(85%);
}

.delete-container:hover .delete-button {
  visibility: visible;
}

#example2:checked + .rounded-full {
  background-color: blue;
}

input:checked ~ .dot {
  transform: translateX(100%);
  background-color: #48bb78;
}

.app-container {
  width: 80% !important;
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin: 10px !important;
}
.button-group-border {
  border-right: 1px solid rgb(195, 195, 195, 0.5) !important;
}

/* calendar component */

.eachdate {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.available {
  background-color: rgb(197, 238, 196) !important;
  color: green;
}

.few-slots {
  background-color: rgb(252, 200, 170) !important;
  color: red;
}

.react-calendar__navigation {
  font-weight: 'bold';
  color: #000000;
}

.react-calendar__navigation button:hover {
  background-color: #f0f5ff !important;
  border-radius: 50% !important;
}

.react-calendar__tile:disabled {
  color: grey !important;
  background-color: white !important;
}

.react-calendar__month-view__days__day--weekend {
  color: black !important;
}

.react-calendar__tile--now {
  border: 2px solid !important;
  background-color: transparent !important ;
  border-color: rgb(150, 149, 149) !important ;
}

.react-calendar__tile:hover {
  background-color: #93b5d3 !important;
}

.react-calendar {
  border: none !important;
  width: 400px;
  line-height: 2em !important;
  color: #000000;
  margin: 2px !important;
}

.react-calendar__month-view__weekdays__weekday {
  color: grey;
}

.custom-calendar .react-calendar__tile {
  margin: 2px;
}

.react-calendar__navigation__label {
  font-weight: bold;
  color: #000000 !important;
}

.react-calendar__tile--active {
  background-color: rgba(37, 99, 235) !important;
  border-radius: 50% !important;
  font-weight: bold;
  color: white !important;
}

/* react editable list (property input fields) */

.app-container {
  width: 60% !important;
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  margin: 5px 5px 10px 5px !important;
}
.react-list-editable .input-field-container {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.react-list-editable .new-input-field {
  margin-right: 10px !important;
  outline: none;
  width: 100% !important;
}

.react-list-editable .input-field {
  padding: 10px;
  margin: 0 7px 0 0 !important;
  background-color: white !important;
  border: 1px solid rgb(170, 170, 170);
  border-radius: 3px;
  outline: none;
}

.react-list-editable .input-field-container .delete-btn {
  justify-content: center;
  padding: 0px;
  border: none;
  font: inherit;
  align-items: center;
  color: inherit;
  background-color: transparent;
  cursor: pointer;
  width: 34px;
  height: 34px;
  display: inline-flex;
  background: white;
  border: 1px solid gray;
  border-radius: 50% !important;
}
/* react editable list */

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.text-underline:hover {
  text-decoration: underline !important;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* For Webkit browsers */
.custom-scrollbar::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 100vh;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

#scrolling-bar-add-pages {
  height: auto;
  max-height: calc(100vh - 370px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

#scrolling-bar-add-pages::-webkit-overflow-scrolling {
  -webkit-overflow-scrolling: scroll;
}

#scrolling-bar-add-pages::-webkit-scrollbar {
  width: 6px;
  -webkit-overflow-scrolling: touch;
}

#scrolling-bar-add-pages::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 100vh;
}

#scrolling-bar-add-pages::-webkit-scrollbar-thumb {
  background: #a2a2a2;
  border-radius: 100vh;
}

@keyframes expandWidth {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

#modalButtons{
  background-color: #255eeff2;
  color: white
}

#scrolling-bar-container {
  -webkit-overflow-scrolling: touch;
}

#scrolling-bar-container::-webkit-overflow-scrolling {
  -webkit-overflow-scrolling: scroll;
}

#scrolling-bar-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  -webkit-overflow-scrolling: touch;
}

#scrolling-bar-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 100vh;
}

#scrolling-bar-container::-webkit-scrollbar-thumb {
  background: #a2a2a2;
  border-radius: 100vh;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.group-child .group-child-open {
  transition: all 0.3s;
  transform: rotate(0);
}
.group-child[open] .group-child-open {
  transition: all 0.3s;
  transform: rotate(180deg);
}

html {
  scroll-behavior: smooth;
}

/* canvas loading */
.canvas-loader {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.canvas-primary-loading:before,
.canvas-primary-loading:after {
  position: absolute;
  top: 0;
  content: '';
}

.canvas-primary-loading:before {
  left: -10px;
}

.canvas-primary-loading:after {
  left: 10px;
  -webkit-animation-delay: 0.32s !important;
  animation-delay: 0.32s !important;
}

.canvas-primary-loading:before,
.canvas-primary-loading:after,
.canvas-primary-loading {
  background: #0074f1;
  -webkit-animation: loading-keys-app-loading 0.8s infinite ease-in-out;
  animation: loading-keys-app-loading 0.8s infinite ease-in-out;
  width: 6px;
  height: 24px;
}

.canvas-primary-loading {
  text-indent: -9999em;
  margin: auto;
  position: absolute;
  right: calc(50% - 4px);
  top: calc(50% - 16px);
  -webkit-animation-delay: 0.16s !important;
  animation-delay: 0.16s !important;
}

@-webkit-keyframes loading-keys-app-loading {

  0%,
  80%,
  100% {
    opacity: .75;
    box-shadow: 0 0 #0074f1;
    height: 28px;
  }

  40% {
    opacity: 1;
    box-shadow: 0 -8px #0074f1;
    height: 36px;
  }
}

@keyframes loading-keys-app-loading {

  0%,
  80%,
  100% {
    opacity: .75;
    box-shadow: 0 0 #0074f1;
    height: 28px;
  }

  40% {
    opacity: 1;
    box-shadow: 0 -8px #0074f1;
    height: 36px;
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;
