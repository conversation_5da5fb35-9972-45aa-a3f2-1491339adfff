$widget-link-hover-color: #fff;
$widget-link-active-color: #fff;
$widget-link-color: rgba(255, 255, 255, 0.7);

.widget {
  ul,
  ol {
    margin: 0;
    padding: 0;
    list-style: none;

    > li {
      display: block;
      margin-bottom: 0.375rem;

      &:last-child {
        margin-bottom: 0;
      }

      ul,
      ol {
        padding: 0.25rem 0 0.25rem 1.375rem;
        > li {
          position: relative;
          margin-bottom: 0;
          border-left: $border-width * 2 solid $border-color;
          > a {
            padding-left: 1.125rem !important;
            &::after {
              position: absolute;
              left: 0;
              top: 50%;
              width: 0.75rem;
              height: $border-width;
              background-color: darken($border-color, 6%);
              content: "";
            }
          }
        }
      }
    }
  }
}

.widget-link {
  display: block;
  position: relative;
  padding: 0.25rem 0;
  transition: color 0.25s ease-in-out;
  color: $widget-link-color;
  font-weight: normal;
  text-decoration: none;

  &:hover {
    color: $widget-link-hover-color;
    text-decoration: none;
  }

  &.active,
  .active > & {
    color: $widget-link-active-color;
    pointer-events: none;
    cursor: default;
  }

  > small {
    display: inline-block;
    font: {
      size: 75%;
      weight: normal;
    }
  }

  & + ul > li > .widget-link {
    font-size: 87.5%;
  }
}

//**************Tiles****************

$primary-color: #063d72;

// .container {
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }

.radio-tile-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 20px;

  //set dimensions for invisible container
  .input-container {
    position: relative;
    // height: 7rem;
    // width: 7rem;
    margin: 0.5rem;
    border: 1px solid #979797;
    //make actual radio input invisible
    // + stretch to fill container
    &:hover {
      background-color: #d8ecff;
      border: none;
    }
    .radio-button {
      opacity: 0;
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      margin: 0;
      cursor: pointer;
    }

    //default tile styles
    .radio-tile {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      // border: 2px solid $primary-color;
      border-radius: 5px;
      padding: 1rem;
      transition: transform 300ms ease;
    }
    .icon svg {
      fill: $primary-color;
      width: 3rem;
      height: 3rem;
    }
    .icon {
      color: $primary-color;
    }
    .radio-tile-label {
      text-align: center;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      color: $primary-color;
    }

    //active tile styles
    .radio-button:checked + .radio-tile {
      background-color: #d8ecff;
      border: none;
      // border: 2px solid $primary-color;
      // color: white;
      transform: scale(1.1, 1.1);

      .icon svg {
        fill: white;
        background-color: $primary-color;
      }
      .radio-tile-label {
        color: white;
        background-color: $primary-color;
      }
    }
  }
}

.br-0 {
  border-radius: 0;
}
