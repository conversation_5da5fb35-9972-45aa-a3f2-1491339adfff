// TODO: Remove these importants and change inside the theme
//btn
.btn {
  padding: 0.5625rem 1.25rem;
  min-width: 10rem;
  font-weight: 500;
}

.btn2 {
  @extend .btn;
  min-width: 0;
}

button.btn-primary,
.btn-primary {
  background-color: #3f5c78;
  box-shadow: none;
  border: none;
  color: white;
  &:hover {
    background-color: #6d98c2;
    box-shadow: none;
  }
}
button.btn-secondry,
.btn-secondry {
  background-color: white;
  border: 1px solid #cfd2d2;
  color: #4a4b65;
  box-shadow: none;
  &:hover {
    background-color: white;
    border: 1px solid #0076d1;
    box-shadow: none;
    color: #000000;
  }
}

button.btn-form,
.btn-form {
  background-color: #0076d1;
  border: none;
  border-radius: 7px;
  color: white;
  box-shadow: none;
  &:hover {
    background-color: #0076d1;
    box-shadow: none;
  }
}

button.btn-disabled,
.btn-disabled {
  border: none;
  cursor: none;
}

button.btn-login,
.btn-login {
  font-size: 16px;
  font-weight: bold;
  background-color: $primary;
  box-shadow: none;
  &:hover {
    background-color: $secondary;
    box-shadow: none;
  }
}

button.btn-verify,
.btn-verify {
  font-size: 16px;
  font-weight: bold;
  background-color: rgb(203, 210, 217);
  box-shadow: none;
  color:white;
  &:hover {
    background-color: rgb(203, 210, 217);
    box-shadow: none;
    color:white;
  }

}
