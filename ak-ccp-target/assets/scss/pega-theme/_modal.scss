.create-platform-modal, .create-env-modal {
  > .<PERSON><PERSON><PERSON><PERSON>-root {
    overflow: scroll;
    > .<PERSON><PERSON><PERSON><PERSON>-root {
      height: 600px;
    }
  }
  .MuiStepper-horizontal {
    margin: 18px 100px;
  }
  .MuiStepLabel-label.Mui-active {
    @extend .text-bold-16;
    color: $primary;
  }
  .MuiStepLabel-root.MuiStepLabel-horizontal {
    display: flex;
    flex-direction: column;
  }
  .MuiStepIcon-root.Mui-active {
    color: $primary;
  }
}
.create-platform-modal--head, .create-env-modal--head {
  background-color: #d8ecff;
  padding: 24px 18px;
  h2 {
    margin: 0;
  }
}
.create-env-modal{
 > .MuiBox-root {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 900px;
  background-color: #fff;
  border-radius: 8px;
  //box-shadow: 0px 11px 15px -7px rgb(0 0 0 / 20%), 0px 24px 38px 3px rgb(0 0 0 / 14%), 0px 9px 46px 8px rgb(0 0 0 / 12%);
  overflow: scroll;
 }
}
// .ab .krishank{
//   position: relative;
//   &:hover{
//     +.kd{
//       display: block;
//       position: absolute;
//       top: -1;
//     }
//   }
// }
// .kd{
//   display: none;
// }