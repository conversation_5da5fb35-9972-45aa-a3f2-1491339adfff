table {
  thead {
    tr {
      th {
        font-weight: 700 !important;
      }
    }
  }
}

.collapsible-cards {
  margin-bottom: 12px;
  > div {
    border-radius: 0;
    .card {
      background-color: #e8f5eb;
      text-align: center;
      height: 100%;
    }
    div.errorStatus{
      .card {
        background-color: #BA4F65;
        color: white;
      }
    }
  }
}
.collapse-head {
  display: flex;
  justify-content: space-between;
}

.view-platform-container {
  .MuiPaper-root.MuiTableContainer-root.MuiPaper-elevation1.MuiPaper-rounded {
    height: 600px;
    > table > tbody > tr > td {
      border: none;
    }
  }
}
.pega-pagination {
  display: flex;
  margin: 18px 0;
  float: right;
  li {
    margin-right: 8px;
  }
  .MuiPaginationItem-sizeMedium {
    border-radius: 2px;
    border: 1px solid #808080;
    border-radius: 20%;
  }
  .MuiPaginationItem-sizeMedium.Mui-selected {
    background-color: #c5d2de;
    border-radius: 20% !important;
  }
  .MuiPaginationItem-ellipsis,
  .MuiPaginationItem-previousNext {
    border: none;
  }
}
// .MuiTableCell-body {
//   text-align: center;
// }
