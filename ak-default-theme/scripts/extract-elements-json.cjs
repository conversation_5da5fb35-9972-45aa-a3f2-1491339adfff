// Enable Babel to handle JSX/ESM
require('@babel/register')({
  extensions: ['.js', '.jsx'],
  ignore: [/node_modules/]
});

const fs = require('fs');
const path = require('path');

// Import all elements from index.js
const { elements } = require('../src/index.js');

// Prepare output array
const allComponents = [];

for (const element of elements) {
  try {
    // Push full element object as-is
    allComponents.push(element);
  } catch (err) {
    console.warn(`Skipped element due to error:`, err.message);
  }
}

// Ensure dist/ exists
const distDir = path.resolve(__dirname, '../dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir);
}

// Write single file with full data
const outputPath = path.join(distDir, 'components.json');
fs.writeFileSync(outputPath, JSON.stringify(allComponents, null, 2), 'utf8');

console.log(`Exported ${allComponents.length} components to dist/components.json`);
