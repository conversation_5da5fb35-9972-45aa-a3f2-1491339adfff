packages:
  yum:
    curl: []
    jq: []

files:
  "/etc/datadog-agent/datadog.yaml":
    mode: "000644"
    owner: root
    group: root
    content: |
      api_key: DATADOG_API_KEY
      site: datadoghq.com
      logs_enabled: true
      tags:
        - 'env:demo"
        - "service:pd-app-builder-api-demo"

  "/etc/datadog-agent/conf.d/logs.yaml":
    mode: "000644"
    owner: root
    group: root
    content: |
      logs:
        - type: file
          path: /var/log/nodejs/nodejs.log
          service: pd-app-builder-api-demo
          source: nodejs
          sourcecategory: sourcecode

commands:
  01_install_datadog_agent:
    command: |
      DD_API_KEY=DATADOG_API_KEY DD_SITE="datadoghq.com" bash -c "$(curl -L https://s3.amazonaws.com/dd-agent/scripts/install_script.sh)"

  02_restart_datadog_agent:
    command: service datadog-agent restart
