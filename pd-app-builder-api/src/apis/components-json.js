const AWS = require('aws-sdk');
const { respondUtil } = require('@limegroup/lime-utils');
const logger = require('../utils/logger');
const { parsePayload } = require('@limegroup/lime-utils/dist/payload-util');

const { DEFAULT_HEADERS, getErrorResponse } = respondUtil;

// Fetch a single theme's components.json from S3
async function fetchComponentsFromS3(bucket, key) {
  const s3 = new AWS.S3({
    accessKeyId: process.env.DEMO_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.DEMO_AWS_SECRET_ACCESS_KEY,
    region: 'eu-west-2'
  });

  try {
    const data = await s3
      .getObject({
        Bucket: bucket,
        Key: key
      })
      .promise();

    const parsed = JSON.parse(data.Body.toString('utf-8'));
    return Array.isArray(parsed) ? parsed : [];
  } catch (err) {
    logger.error(`S3 fetch failed for ${key}: ${err.message}`);
    return [];
  }
}

// Fetch all themes' components.json
async function fetchAllThemesFromS3() {
  const bucket = 'boffiny-component-json';
  const themeKeys = [
    { source: '@astrakraft/default-theme', key: 'ak-default-theme/components.json' },
    { source: '@astrakraft/ak-materialui-theme', key: 'ak-materialui-theme/components.json' },
    { source: '@astrakraft/ak-pega-theme', key: 'ak-pega-theme/components.json' }
  ];

  const results = await Promise.all(
    themeKeys.map(async ({ source, key }) => {
      const elements = await fetchComponentsFromS3(bucket, key);
      return { source, elements };
    })
  );

  return results;
}

// Main handler
async function getComponentItems(options, event, context, callback) { // eslint-disable-line consistent-return
  try {
    const payload = parsePayload(event.body);

    if (!Array.isArray(payload)) {
      throw getErrorResponse({ status: 400, message: 'Payload must be an array of objects with id field' });
    }

    const themeData = await fetchAllThemesFromS3();
    const searchIds = payload.map(({ id }) => id);
    const result = [];

    for (const searchId of searchIds) { //eslint-disable-line no-unused-vars
      let found = false;

      for (const { source, elements } of themeData) { //eslint-disable-line no-unused-vars
        for (const el of elements) { //eslint-disable-line no-unused-vars
          if (!Array.isArray(el.variants)) continue;

          const matchedVariant = el.variants.find(variant => variant?.id === searchId);
          if (matchedVariant) {
            result.push({
              data: JSON.parse(JSON.stringify(matchedVariant)),
              source: {
                label: el.label,
                type: el.type,
                theme: source
              }
            });
            found = true;
            break;
          }
        }
        if (found) break;
      }
    }

    event.response = {
      statusCode: 200,
      headers: DEFAULT_HEADERS,
      body: result
    };
    return callback();
  } catch (error) {
    logger.error(error);
    event.response = {
      statusCode: 500,
      headers: DEFAULT_HEADERS,
      body: getErrorResponse({ message: error.message || 'Internal Server Error' })
    };
    return callback(error);
  }
}

module.exports = { getComponentItems };
