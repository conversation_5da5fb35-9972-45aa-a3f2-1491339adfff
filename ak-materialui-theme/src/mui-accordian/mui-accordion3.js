import React from 'react';
import { Accordion, AccordionSummary, AccordionDetails, Typography, Box, Divider } from '@mui/material';

import { isNonEmptyArray } from '../utils/common-utils';

const ChevronIcon = ({ size = 24, color = '#FF0000' }) => (
  <svg width={size} height={size / 2} viewBox="0 0 24 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 2L12 10L22 2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const DeviceAccordion = ({
  options,
  currencies,
  submenubgColor,
  titleBackgroundColor,
  titleFontSize,
  titleFontWeight,
  titleColor,
  descriptionFontSize,
  descriptionColor,
  chevronIconSize,
  chevronIconColor,
  liveMode,
  designerProps,
  data,
  visibility
}) => {
  // Return null if visibility is hidden
  if (visibility === 'hidden') {
    return null;
  }

  // Improved options handling logic
  const getAccordionData = () => {
    if (liveMode && options) {
      return options;
    }
    if (isNonEmptyArray(designerProps)) {
      return designerProps;
    }
    if (isNonEmptyArray(options)) {
      return options;
    }
    return data || [];
  };

  const accordionData = getAccordionData();

  return (
    <Box
      sx={{
        width: '100%',
        p: 0,
        overflow: 'auto',
        maxHeight: '100%'
      }}
    >
      {isNonEmptyArray(accordionData) &&
        accordionData.map((item, index) => (
          <Accordion key={index}>
            <AccordionSummary
              expandIcon={
                <div
                  style={{
                    display: 'inline-block',
                    transition: 'transform 0.2s',
                    transform: 'rotate(0deg)',
                    marginRight: '8px', // Added right margin
                    marginLeft: '20px',
                    position: 'relative',
                    top: '-2px'
                  }}
                >
                  <ChevronIcon size={chevronIconSize || 20} color={chevronIconColor || '#FF0000'} />
                </div>
              }
              aria-controls={`device-content-${index}`}
              id={`device-header-${index}`}
              sx={{
                backgroundColor: titleBackgroundColor,
                '& .MuiAccordionSummary-content': {
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%'
                },
                '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
                  '& > div': {
                    transform: 'rotate(180deg)'
                  }
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <span
                  dangerouslySetInnerHTML={{
                    __html: decodeURIComponent(item.icon)
                  }}
                />
                <Box>
                  <Typography
                    variant="h6"
                    sx={{
                      fontSize: titleFontSize || '16px',
                      fontWeight: titleFontWeight || 'bold',
                      color: titleColor || 'black'
                    }}
                  >
                    {item.label}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: descriptionFontSize || '10px', color: descriptionColor || 'black' }}
                  >
                    {item.label2}
                  </Typography>
                </Box>
              </Box>
              <Typography
                variant="h6"
                sx={{
                  fontSize: titleFontSize || '16px',
                  fontWeight: titleFontWeight || 'bold',
                  color: titleColor || 'black'
                }}
              >
                {currencies === 'none' ? item.label3 : `${currencies}${item.label3} `}
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 0 }}>
              {isNonEmptyArray(item.submenu) &&
                item.submenu.map((submenuItem, submenuIndex) => (
                  <Accordion
                    key={submenuIndex}
                    sx={{
                      '&.MuiAccordion-root': {
                        boxShadow: 'none',
                        backgroundColor: submenubgColor || '#f8f8f8'
                      },
                      '&:before': {
                        display: 'none'
                      }
                    }}
                  >
                    <AccordionSummary
                      expandIcon={
                        <div
                          style={{
                            display: 'inline-block',
                            transition: 'transform 0.2s',
                            transform: 'rotate(0deg)',
                            marginLeft: '12px'
                          }}
                        >
                          <ChevronIcon size={chevronIconSize || 20} color={chevronIconColor || '#FF0000'} />
                        </div>
                      }
                      aria-controls={`plans-content-${index}-${submenuIndex}`}
                      id={`plans-header-${index}-${submenuIndex}`}
                      sx={{
                        '& .MuiAccordionSummary-content': {
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          width: '100%'
                        },
                        '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
                          '& > div': {
                            transform: 'rotate(180deg)'
                          }
                        }
                      }}
                    >
                      <Typography
                        sx={{
                          fontSize: titleFontSize || '16px',
                          fontWeight: titleFontWeight || 'bold',
                          color: titleColor || 'black'
                        }}
                        variant="h6"
                      >
                        {submenuItem.label}
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: titleFontSize || '16px',
                          fontWeight: titleFontWeight || 'bold',
                          color: titleColor || 'black'
                        }}
                        variant="h6"
                      >
                        {currencies === 'none' ? submenuItem.label2 : `${currencies}${submenuItem.label2}`}
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails sx={{ px: 2, pb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography
                          sx={{ fontSize: descriptionFontSize || '10px', color: descriptionColor || 'black' }}
                        >
                          {submenuItem.label3}
                        </Typography>
                        <Typography
                          sx={{ fontSize: descriptionFontSize || '10px', color: descriptionColor || 'black' }}
                        >
                          {currencies === 'none' ? submenuItem.label4 : `${currencies}${submenuItem.label4}`}
                        </Typography>
                      </Box>
                      <Divider sx={{ my: 2 }} />
                      {isNonEmptyArray(submenuItem.nestedSubMenu) &&
                        submenuItem.nestedSubMenu.map((nestedItem, nestedIndex) => (
                          <Box
                            key={nestedIndex}
                            sx={{ mb: nestedIndex !== submenuItem.nestedSubMenu.length - 1 ? 3 : 0 }}
                          >
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography
                                sx={{
                                  fontSize: titleFontSize || '16px',
                                  fontWeight: titleFontWeight || 'bold',
                                  color: titleColor || 'black'
                                }}
                                variant="h6"
                              >
                                {nestedItem.label}
                              </Typography>
                              <Typography
                                sx={{
                                  fontSize: titleFontSize || '16px',
                                  fontWeight: titleFontWeight || 'bold',
                                  color: titleColor || 'black'
                                }}
                                variant="h6"
                              >
                                {currencies === 'none' ? nestedItem.label2 : `${currencies}${nestedItem.label2}`}
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Typography
                                sx={{ fontSize: descriptionFontSize || '10px', color: descriptionColor || 'black' }}
                                color="text.secondary"
                              >
                                {nestedItem.label3}
                              </Typography>
                              <Typography
                                sx={{ fontSize: descriptionFontSize || '10px', color: descriptionColor || 'black' }}
                              >
                                {currencies === 'none' ? nestedItem.label4 : `${currencies}${nestedItem.label4}`}
                              </Typography>
                            </Box>
                          </Box>
                        ))}
                    </AccordionDetails>
                  </Accordion>
                ))}
            </AccordionDetails>
          </Accordion>
        ))}
    </Box>
  );
};

export default DeviceAccordion;
