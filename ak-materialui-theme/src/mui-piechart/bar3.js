import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { isNonEmptyArray } from '../utils/common-utils';

const CustomBar = ({
  value,
  maxValue,
  isHovered,
  isLast,
  activecolor = '#fff',
  barbgcolor = '#1976d2',
  latestbillcolor = 'red',
  barbordercolor = '#000',
  barSize = 16
}) => {
  const heightPercentage = (value / maxValue) * 100;

  return (
    <Box
      sx={{
        height: `${heightPercentage}%`,
        width: `${barSize}px`,
        backgroundColor: isHovered ? activecolor : isLast ? latestbillcolor : barbgcolor,
        borderRadius: 2,
        border: isHovered ? `2px solid ${barbordercolor}` : 'none',
        transition: 'all 0.2s ease',
        cursor: 'pointer',
        position: 'relative',
        minHeight: '4px'
      }}
    />
  );
};

const CustomTooltip = ({
  value,
  label,
  tooltipbgcolor = 'white',
  fontSize = 12,
  tooltipcolor = '#000',
  isLast = false,
  tooltipText = 'Monthly Bill',
  fontWeight = 500
}) => (
  <Box
    sx={{
      position: 'absolute',
      top: -60,
      left: '50%',
      transform: 'translateX(-50%)',
      backgroundColor: tooltipbgcolor,
      color: tooltipcolor,
      padding: '10px 15px',
      borderRadius: 1,
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      whiteSpace: 'nowrap',
      zIndex: isLast ? 0 : 1,
      textAlign: 'center',
      '&:after': {
        content: '""',
        position: 'absolute',
        bottom: -6,
        left: '50%',
        transform: 'translateX(-50%) rotate(45deg)',
        width: 12,
        height: 12,
        backgroundColor: tooltipbgcolor,
        boxShadow: '2px 2px 2px rgba(0,0,0,0.1)',
        zIndex: -1
      }
    }}
  >
    <Typography variant="body2" fontWeight={500} align="center">
      €{value}
    </Typography>
    <Typography
      variant="caption"
      sx={{
        fontWeight: fontWeight || 500,
        fontSize: fontSize,
        pcolor: tooltipcolor,
        display: 'block',
        textAlign: 'center',
        mt: 0.5
      }}
    >
      {label}
    </Typography>
    <Typography
      variant="caption"
      sx={{ fontSize: fontSize, color: tooltipcolor, display: 'block', textAlign: 'center', mt: 0.5 }}
    >
      {tooltipText}
    </Typography>
  </Box>
);

const BarChartComponent = (props) => {
  const [hoveredIndex, setHoveredIndex] = useState(null);

  if (props.visibility === 'hidden') {
    return null;
  }

  // Improved options handling logic
  const getChartData = () => {
    if (props.liveMode && props.options) {
      return props.options;
    }
    if (isNonEmptyArray(props.designerProps)) {
      return props.designerProps;
    }
    if (isNonEmptyArray(props.options)) {
      return props.options;
    }
    return props.data || [];
  };

  const chartData = getChartData();
  const maxValue = isNonEmptyArray(chartData) ? Math.max(...chartData.map((item) => parseFloat(item.value || 0))) : 0;

  // Default colors
  const defaultColors = {
    prevMonthBgColor: '#000',
    lastMonthBgColor: 'red',
    hoverBgColor: '#666',
    labelColor: '#fff'
  };

  return (
    <Box height={props.height || 400} width="100%" sx={{ overflowX: 'auto', boxShadow: props.boxShadow }}>
      <Box
        sx={{
          minWidth: '800px',
          width: 'max-content',
          height: '100%',
          p: 4,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Box sx={{ flex: 1, minHeight: 0 }}>
          <Stack
            direction="row"
            spacing={Number(props.gap) || 0}
            alignItems="flex-end"
            sx={{
              height: '100%',
              '& > *': {
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center'
              }
            }}
          >
            {isNonEmptyArray(chartData) &&
              chartData.map((item, index) => (
                <Box
                  key={index}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    position: 'relative'
                  }}
                >
                  {hoveredIndex === index && (
                    <CustomTooltip
                      fontWeight={props.fontWeight || 500}
                      label={`${props.currencies}${item.value}`}
                      tooltipbgcolor={props.tooltipbgcolor}
                      tooltipcolor={props.tooltipcolor}
                      isLast={index === chartData.length - 1}
                      tooltipText={
                        index === chartData.length - 1
                          ? props.lastbartext || 'Latest Bill'
                          : props.tooltiptext || 'Monthly Bill'
                      }
                    />
                  )}
                  <Box sx={{ height: '85%', display: 'flex', alignItems: 'flex-end' }}>
                    <CustomBar
                      value={parseFloat(item.value)}
                      maxValue={maxValue}
                      isHovered={hoveredIndex === index}
                      isLast={index === chartData.length - 1}
                      activecolor={props.activecolor}
                      barbgcolor={props.barbgcolor}
                      latestbillcolor={props.latestbillcolor}
                      barbordercolor={props.barbordercolor}
                      barSize={Number(props.barSize) || 16}
                    />
                  </Box>
                  <Box sx={{ mt: 2, textAlign: 'center' }}>
                    <Typography
                      variant="caption"
                      sx={{
                        fontWeight: props.fontWeight || 500,
                        display: 'inline-block',
                        fontSize: Number(props.labelfontsize) || 12,
                        color: props.labelcolor || defaultColors.labelColor,
                        padding: '4px 12px',
                        backgroundColor:
                          index === chartData.length - 1
                            ? props.lastmonthbgcolor || defaultColors.lastMonthBgColor
                            : props.prevmonthbgcolor || defaultColors.prevMonthBgColor,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: props.hoverbgcolor || defaultColors.hoverBgColor
                        },
                        borderRadius:
                          index === 0 ? '4px 0 0 4px' : index === chartData.length - 1 ? '0 4px 4px 0' : '0',
                        marginLeft: index === 0 ? '0' : '-1px',
                        position: 'relative',
                        zIndex: hoveredIndex === index ? 2 : 1
                      }}
                    >
                      {item.label}
                    </Typography>
                    {hoveredIndex === index && (
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: -8,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          width: 30,
                          height: 3,
                          backgroundColor: 'red'
                        }}
                      />
                    )}
                  </Box>
                </Box>
              ))}
          </Stack>
        </Box>
      </Box>
    </Box>
  );
};

export default BarChartComponent;
