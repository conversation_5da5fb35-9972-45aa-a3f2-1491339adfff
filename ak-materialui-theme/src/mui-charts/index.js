import React from 'react';

import { commonProperties } from '@astrakraft/core-lib';
const { commonPegaPropDefinitions, commonPegaProps, commonProps, commonRenderProps } = commonProperties;
import BarChartJS from './bar-chartjs';
import LineChartJS from './line-chartjs';
import PolarAreaChartJS from './polar-chartjs';
import DonutChartJS from './donut-chartjs';
import PieChartJS from './pie-chartjs';
import RadarChartJS from './radar-chartjs';
import ScatterChartJS from './scatter-chartjs';
import { utils } from '@astrakraft/core-lib';
const { themeUtil } = utils;
const { getLink } = themeUtil;
import { updateLabelsWithProps } from '../utils/common-utils';

export default {
  label: 'Charts',
  icon: (
    <svg
      fill="#000000"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      enableBackground="new 0 0 24 24"
    >
      <path d="M5.12 0.128v1.313C2.912 2.01 1.28 4.014 1.28 6.4 1.28 9.228 3.572 11.52 6.4 11.52c2.386 0 4.39 -1.632 4.959 -3.84h1.313c-0.593 2.921 -3.176 5.12 -6.272 5.12C2.865 12.8 0 9.935 0 6.4 0 3.304 2.199 0.721 5.12 0.128M6.4 0c3.499 0 6.343 2.808 6.399 6.294l0.001 0.106H6.4z" />{' '}
    </svg>
  ),
  type: 'VISUALIZATION',
  events: [],
  properties: commonProps.concat(commonPegaPropDefinitions, [
    {
      name: 'data'
    },

    {
      name: 'optionType',
      options: ['static', 'dynamic']
    },
    {
      name: 'color1'
    },
    {
      name: 'color2'
    },
    {
      name: 'color3'
    },
    {
      name: 'options',
      type: 'menuOptions',
      i18n: true
    },
    { name: 'optionType', options: ['static', 'dynamic'] }
  ]),
  variants: [
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      properties: commonProps.concat(commonPegaPropDefinitions, [
        { name: 'activecolor' },
        { name: 'latestbillcolor' },
        { name: 'tooltipbgcolor' },
        { name: 'tooltipcolor' },
        { name: 'tooltipfontsize' },
        { name: 'tooltipfontweight' },
        { name: 'barbordercolor' },
        { name: 'barbgcolor' },
        { name: 'barSize' },
        { name: 'labelfontsize' },
        { name: 'labelcolor' },
        { name: 'tooltipText' },
        { name: 'lastbartext' },
        { name: 'tooltipfontsize' },
        { name: 'tooltipfontweight' },
        { name: 'lastmonthbgcolor' },
        { name: 'barStyle', options: ['circular', 'rectangle'] },
        { name: 'optionType', options: ['static', 'dynamic'] },
        { name: 'currencies', options: ['none', '$', '€', '£', '¥', '₹'] },

        {
          name: 'options',
          type: 'menuOptions',
          i18n: true
        }
      ]),
      id: 'chartjs',
      designerProps: {},
      renderProps: {
        options: [
          { label: 'Jan', value: 80 },
          { label: 'Feb', value: 160 },
          { label: 'Mar', value: 100 },
          { label: 'Apr', value: 105 },
          { label: 'May', value: 110 },
          { label: 'Jun', value: 120 },
          { label: 'Jul', value: 70 },
          { label: 'Aug', value: 100 },
          { label: 'Sep', value: 166.52 },
          { label: 'Oct', value: 90 },
          { label: 'Nov', value: 130 },
          { label: 'Dec', value: 60 },
          { label: 'Jan', value: 80 },
          { label: 'Feb', value: 160 },
          { label: 'Mar', value: 100 },
          { label: 'Apr', value: 105 },
          { label: 'May', value: 110 },
          { label: 'Jun', value: 65 },
          { label: 'Apr', value: 105 }
        ],
        optionType: 'static',
        ...commonRenderProps,
        ...commonPegaProps,
        data: '75',
        width: '400px',
        barStyle: 'rectangle',
        barSize: 20,
        activecolor: '#fff',
        barbgcolor: 'skyblue',
        labelfontsize: 12,
        currencies: '$',
        visibility: 'visible',
        lastmonthbgcolor: 'red',
        tooltipText: 'previous Bill',
        lastbartext: 'Latest Bill',
        labelcolor: '#000',
        latestbillcolor: 'red',
        tooltipbgcolor: 'white',
        tooltipcolor: '#000',
        barbordercolor: '#000',
        height: '400px',
        backgroundColor: 'white',
        color1: '#A133FF',
        color2: '#FF33A1',
        color3: '#3357FF',
        pegaConfig: [
          {
            name: 'label',
            label: 'Label',
            format: 'TEXT'
          },
          {
            name: 'cardItemLabel',
            label: 'Label on each Card',
            format: 'TEXT'
          },
          {
            name: 'cardItemIcon',
            label: 'Icon on each Card',
            format: 'TEXT'
          },
          {
            name: 'dataPage',
            label: 'List Data Page name to get all cases',
            format: 'TEXT',
            required: true
          },
          {
            name: 'createClassname',
            label: 'Create case className (empty to disable create)',
            format: 'TEXT'
          }
        ]
      },
      renderComponent: function (props) {
        const updatedConfig = updateLabelsWithProps(props.pegaConfig, props);
        props = { ...props, pegaConfig: updatedConfig };
        const linkRef = getLink(props);
        let data = props.data;
        let options = props.options;

        if (!props.liveMode && typeof data === 'string' && data.indexOf('{{') >= 0) {
          data = props._designerProps?.data || [];
        }

        if (!props.liveMode && typeof options === 'string' && options.indexOf('{{') >= 0) {
          options = props._designerProps?.options || [];
        }

        const finalData = options || data;

        return props.liveMode && linkRef ? (
          <a href={linkRef}>
            <BarChartJS {...props} data={finalData} />
          </a>
        ) : (
          <BarChartJS {...props} data={finalData} />
        );
      }
    },
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      properties: commonProps.concat(commonPegaPropDefinitions, [
        { name: 'lineColor' },
        { name: 'pointColor' },
        { name: 'pointBorderColor' },
        { name: 'fillColor' },
        { name: 'fillArea', type: 'boolean' },
        { name: 'lineWidth' },
        { name: 'pointSize' },
        { name: 'pointHoverSize' },
        { name: 'lineTension' },
        { name: 'showGrid', type: 'boolean' },
        { name: 'gridColor' },
        { name: 'startFromZero', type: 'boolean' },
        { name: 'showLegend', type: 'boolean' },
        { name: 'legendPosition', options: ['top', 'bottom', 'left', 'right'] },
        { name: 'legendColor' },
        { name: 'legendFontSize' },
        { name: 'xAxisLabelColor' },
        { name: 'xAxisFontSize' },
        { name: 'yAxisLabelColor' },
        { name: 'yAxisFontSize' },
        { name: 'labelFontSize' },
        { name: 'labelColor' },
        { name: 'tooltipText' },
        { name: 'tooltipBgColor' },
        { name: 'tooltipColor' },
        { name: 'tooltipFontSize' },
        { name: 'tooltipFontWeight' },
        { name: 'datasetLabel' },
        { name: 'currencies', options: ['none', '$', '€', '£', '¥', '₹'] },
        { name: 'optionType', options: ['static', 'dynamic'] },
        {
          name: 'options',
          type: 'menuOptions',
          i18n: true
        }
      ]),
      id: 'linechartjs',
      designerProps: {},
      renderProps: {
        options: [
          { label: 'Jan', value: 65 },
          { label: 'Feb', value: 59 },
          { label: 'Mar', value: 80 },
          { label: 'Apr', value: 81 },
          { label: 'May', value: 56 },
          { label: 'Jun', value: 55 },
          { label: 'Jul', value: 40 }
        ],
        optionType: 'static',
        ...commonRenderProps,
        ...commonPegaProps,
        data: '75',
        width: '400px',
        height: '400px',
        lineColor: '#4BC0C0',
        pointColor: '#4BC0C0',
        fillColor: 'rgba(75, 192, 192, 0.2)',
        fillArea: false,
        lineWidth: 2,
        pointSize: 4,
        pointHoverSize: 6,
        lineTension: 0.4,
        showGrid: false,
        gridColor: '#ddd',
        startFromZero: false,
        showLegend: false,
        legendPosition: 'top',
        legendColor: '#000',
        legendFontSize: 12,
        labelFontSize: 12,
        labelColor: '#000',
        tooltipText: 'Value',
        tooltipBgColor: 'white',
        tooltipColor: '#000',
        tooltipFontSize: 12,
        tooltipFontWeight: 500,
        datasetLabel: 'My First Dataset',
        currencies: '€',
        visibility: 'visible',
        backgroundColor: 'white',
        color1: '#4BC0C0',
        color2: '#FF33A1',
        color3: '#3357FF',
        pegaConfig: [
          {
            name: 'label',
            label: 'Label',
            format: 'TEXT'
          },
          {
            name: 'cardItemLabel',
            label: 'Label on each Card',
            format: 'TEXT'
          },
          {
            name: 'cardItemIcon',
            label: 'Icon on each Card',
            format: 'TEXT'
          },
          {
            name: 'dataPage',
            label: 'List Data Page name to get all cases',
            format: 'TEXT',
            required: true
          },
          {
            name: 'createClassname',
            label: 'Create case className (empty to disable create)',
            format: 'TEXT'
          }
        ]
      },
      renderComponent: function (props) {
        const updatedConfig = updateLabelsWithProps(props.pegaConfig, props);
        props = { ...props, pegaConfig: updatedConfig };
        const linkRef = getLink(props);
        let data = props.data;
        let options = props.options;

        if (!props.liveMode && typeof data === 'string' && data.indexOf('{{') >= 0) {
          data = props._designerProps?.data || [];
        }

        if (!props.liveMode && typeof options === 'string' && options.indexOf('{{') >= 0) {
          options = props._designerProps?.options || [];
        }

        const finalData = options || data;

        return props.liveMode && linkRef ? (
          <a href={linkRef}>
            <LineChartJS {...props} data={finalData} />
          </a>
        ) : (
          <LineChartJS {...props} data={finalData} />
        );
      }
    },
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      properties: commonProps.concat(commonPegaPropDefinitions, [
        { name: 'backgroundColor' },
        { name: 'borderColor' },
        { name: 'borderWidth' },
        { name: 'borderAlign', options: ['center', 'inner', 'outer'] },
        { name: 'showGrid', type: 'boolean' },
        { name: 'gridColor' },
        { name: 'startFromZero', type: 'boolean' },
        { name: 'legendPosition', options: ['top', 'bottom', 'left', 'right'] },
        { name: 'legendColor' },
        { name: 'legendFontSize' },
        { name: 'labelFontSize' },
        { name: 'labelColor' },
        { name: 'showAngleLines', type: 'boolean' },
        { name: 'angleLineColor' },
        { name: 'showTickBackdrop', type: 'boolean' },
        { name: 'tickBackdropColor' },
        { name: 'tooltipText' },
        { name: 'tooltipBgColor' },
        { name: 'tooltipColor' },
        { name: 'tooltipFontSize' },
        { name: 'tooltipFontWeight' },
        { name: 'datasetLabel' },
        { name: 'currencies', options: ['none', '$', '€', '£', '¥', '₹'] },
        { name: 'optionType', options: ['static', 'dynamic'] },
        {
          name: 'options',
          type: 'menuOptions',
          i18n: true
        }
      ]),
      id: 'polarareachartjs',
      designerProps: {},
      renderProps: {
        options: [
          { label: 'Red', value: 11 },
          { label: 'Green', value: 16 },
          { label: 'Yellow', value: 7 },
          { label: 'Grey', value: 3 },
          { label: 'Blue', value: 14 }
        ],
        optionType: 'static',
        ...commonRenderProps,
        ...commonPegaProps,
        data: '75',
        width: '400px',
        height: '400px',
        backgroundColor: undefined,
        borderColor: undefined,
        borderWidth: 1,
        borderAlign: 'center',
        showGrid: 'true',
        gridColor: '#ddd',
        startFromZero: 'true',
        showLegend: true,
        legendPosition: 'top',
        legendColor: '#000',
        legendFontSize: 12,
        labelFontSize: 12,
        labelColor: '#000',
        showAngleLines: 'true',
        angleLineColor: '#ddd',
        showTickBackdrop: 'true',
        tickBackdropColor: 'rgba(255, 255, 255, 0.75)',
        tooltipText: 'Value',
        tooltipBgColor: 'white',
        tooltipColor: '#000',
        tooltipFontSize: 12,
        tooltipFontWeight: 500,
        datasetLabel: 'Dataset',
        currencies: '€',
        visibility: 'visible',
        pegaConfig: [
          {
            name: 'label',
            label: 'Label',
            format: 'TEXT'
          },
          {
            name: 'cardItemLabel',
            label: 'Label on each Card',
            format: 'TEXT'
          },
          {
            name: 'cardItemIcon',
            label: 'Icon on each Card',
            format: 'TEXT'
          },
          {
            name: 'dataPage',
            label: 'List Data Page name to get all cases',
            format: 'TEXT',
            required: true
          },
          {
            name: 'createClassname',
            label: 'Create case className (empty to disable create)',
            format: 'TEXT'
          }
        ]
      },
      renderComponent: function (props) {
        const updatedConfig = updateLabelsWithProps(props.pegaConfig, props);
        props = { ...props, pegaConfig: updatedConfig };
        const linkRef = getLink(props);
        let data = props.data;
        let options = props.options;

        if (!props.liveMode && typeof data === 'string' && data.indexOf('{{') >= 0) {
          data = props._designerProps?.data || [];
        }

        if (!props.liveMode && typeof options === 'string' && options.indexOf('{{') >= 0) {
          options = props._designerProps?.options || [];
        }

        const finalData = options || data;

        return props.liveMode && linkRef ? (
          <a href={linkRef}>
            <PolarAreaChartJS {...props} data={finalData} />
          </a>
        ) : (
          <PolarAreaChartJS {...props} data={finalData} />
        );
      }
    },
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      properties: commonProps.concat(commonPegaPropDefinitions, [
        { name: 'backgroundColor' },
        { name: 'borderColor' },
        { name: 'borderWidth' },
        { name: 'hoverOffset' },
        { name: 'cutoutPercentage' },
        { name: 'showLegend', type: 'boolean' },
        { name: 'legendPosition', options: ['top', 'bottom', 'left', 'right'] },
        { name: 'legendColor' },
        { name: 'legendFontSize' },
        { name: 'tooltipText' },
        { name: 'tooltipBgColor' },
        { name: 'tooltipColor' },
        { name: 'tooltipFontSize' },
        { name: 'tooltipFontWeight' },
        { name: 'datasetLabel' },
        { name: 'labelcolor' },
        { name: 'currencies', options: ['none', '$', '€', '£', '¥', '₹'] },
        { name: 'optionType', options: ['static', 'dynamic'] },
        {
          name: 'options',
          type: 'menuOptions',
          i18n: true
        }
      ]),
      id: 'donutchartjs',
      designerProps: {},
      renderProps: {
        options: [
          { label: 'Red', value: 300 },
          { label: 'Blue', value: 50 },
          { label: 'Yellow', value: 100 }
        ],
        optionType: 'static',
        ...commonRenderProps,
        ...commonPegaProps,
        data: '75',
        width: '400px',
        height: '400px',
        cutoutPercentage: '50',
        backgroundColor: undefined,
        borderColor: undefined,
        borderWidth: 1,
        hoverOffset: 4,
        showLegend: true,
        legendPosition: 'top',
        legendColor: '#000',
        legendFontSize: 12,
        tooltipText: 'Value',
        tooltipBgColor: 'white',
        tooltipColor: '#000',
        tooltipFontSize: 12,
        tooltipFontWeight: 500,
        datasetLabel: 'Dataset',
        currencies: '€',
        visibility: 'visible',
        pegaConfig: [
          {
            name: 'label',
            label: 'Label',
            format: 'TEXT'
          },
          {
            name: 'cardItemLabel',
            label: 'Label on each Card',
            format: 'TEXT'
          },
          {
            name: 'cardItemIcon',
            label: 'Icon on each Card',
            format: 'TEXT'
          },
          {
            name: 'dataPage',
            label: 'List Data Page name to get all cases',
            format: 'TEXT',
            required: true
          },
          {
            name: 'createClassname',
            label: 'Create case className (empty to disable create)',
            format: 'TEXT'
          }
        ]
      },
      renderComponent: function (props) {
        const updatedConfig = updateLabelsWithProps(props.pegaConfig, props);
        props = { ...props, pegaConfig: updatedConfig };
        const linkRef = getLink(props);
        let data = props.data;
        let options = props.options;

        if (!props.liveMode && typeof data === 'string' && data.indexOf('{{') >= 0) {
          data = props._designerProps?.data || [];
        }

        if (!props.liveMode && typeof options === 'string' && options.indexOf('{{') >= 0) {
          options = props._designerProps?.options || [];
        }

        const finalData = options || data;

        return props.liveMode && linkRef ? (
          <a href={linkRef}>
            <DonutChartJS {...props} data={finalData} />
          </a>
        ) : (
          <DonutChartJS {...props} data={finalData} />
        );
      }
    },
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      properties: commonProps.concat(commonPegaPropDefinitions, [
        { name: 'backgroundColor' },
        { name: 'borderColor' },
        { name: 'borderWidth' },
        { name: 'hoverOffset' },
        { name: 'showLegend', type: 'boolean' },
        { name: 'legendPosition', options: ['top', 'bottom', 'left', 'right'] },
        { name: 'legendColor' },
        { name: 'legendFontSize' },
        { name: 'tooltipText' },
        { name: 'tooltipBgColor' },
        { name: 'tooltipColor' },
        { name: 'tooltipFontSize' },
        { name: 'tooltipFontWeight' },
        { name: 'datasetLabel' },
        { name: 'labelcolor' },
        { name: 'currencies', options: ['none', '$', '€', '£', '¥', '₹'] },
        { name: 'optionType', options: ['static', 'dynamic'] },
        {
          name: 'options',
          type: 'menuOptions',
          i18n: true
        }
      ]),
      id: 'piechartjs',
      designerProps: {},
      renderProps: {
        options: [
          { label: 'Red', value: 300 },
          { label: 'Blue', value: 50 },
          { label: 'Yellow', value: 100 }
        ],
        optionType: 'static',
        ...commonRenderProps,
        ...commonPegaProps,
        data: '75',
        width: '400px',
        height: '400px',
        backgroundColor: undefined,
        borderColor: undefined,
        borderWidth: 1,
        hoverOffset: 4,
        showLegend: true,
        legendPosition: 'top',
        legendColor: '#000',
        legendFontSize: 12,
        tooltipText: 'Value',
        tooltipBgColor: 'white',
        tooltipColor: '#000',
        tooltipFontSize: 12,
        tooltipFontWeight: 500,
        datasetLabel: 'Dataset',
        currencies: '€',
        visibility: 'visible',
        pegaConfig: [
          {
            name: 'label',
            label: 'Label',
            format: 'TEXT'
          },
          {
            name: 'cardItemLabel',
            label: 'Label on each Card',
            format: 'TEXT'
          },
          {
            name: 'cardItemIcon',
            label: 'Icon on each Card',
            format: 'TEXT'
          },
          {
            name: 'dataPage',
            label: 'List Data Page name to get all cases',
            format: 'TEXT',
            required: true
          },
          {
            name: 'createClassname',
            label: 'Create case className (empty to disable create)',
            format: 'TEXT'
          }
        ]
      },
      renderComponent: function (props) {
        const updatedConfig = updateLabelsWithProps(props.pegaConfig, props);
        props = { ...props, pegaConfig: updatedConfig };
        const linkRef = getLink(props);
        let data = props.data;
        let options = props.options;

        if (!props.liveMode && typeof data === 'string' && data.indexOf('{{') >= 0) {
          data = props._designerProps?.data || [];
        }

        if (!props.liveMode && typeof options === 'string' && options.indexOf('{{') >= 0) {
          options = props._designerProps?.options || [];
        }

        const finalData = options || data;

        return props.liveMode && linkRef ? (
          <a href={linkRef}>
            <PieChartJS {...props} data={finalData} />
          </a>
        ) : (
          <PieChartJS {...props} data={finalData} />
        );
      }
    },
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      properties: commonProps.concat(commonPegaPropDefinitions, [
        { name: 'backgroundColor' },
        { name: 'borderColor' },
        { name: 'borderWidth' },
        { name: 'pointSize' },
        { name: 'pointHoverSize' },
        { name: 'showGrid', type: 'boolean' },
        { name: 'gridColor' },
        { name: 'showAngleLines', type: 'boolean' },
        { name: 'angleLineColor' },
        { name: 'startFromZero', type: 'boolean' },
        { name: 'showTicks', type: 'boolean' },
        { name: 'tickColor' },
        { name: 'tickBackdropColor' },
        { name: 'showLegend', type: 'boolean' },
        { name: 'legendPosition', options: ['top', 'bottom', 'left', 'right'] },
        { name: 'legendColor' },
        { name: 'legendFontSize' },
        { name: 'labelColor' },
        { name: 'labelFontSize' },
        { name: 'tooltipText' },
        { name: 'tooltipBgColor' },
        { name: 'tooltipColor' },
        { name: 'tooltipFontSize' },
        { name: 'tooltipFontWeight' },
        { name: 'datasetLabel' },
        { name: 'fontSize' },
        { name: 'fontWeight' },
        { name: 'labelcolor' },
        { name: 'optionType', options: ['static', 'dynamic'] },
        {
          name: 'options',
          type: 'menuOptions',
          i18n: true
        }
      ]),
      id: 'radarchartjs',
      designerProps: {},
      renderProps: {
        options: [
          { icon: '', label: 'Eating(Dataset 1)', value: '65' },
          { icon: '', label: 'Drinking(Dataset 1)', value: '59' },
          { icon: '', label: 'Sleeping(Dataset 1)', value: '80' },
          { icon: '', label: 'Cycling(Dataset 1)', value: '81' },
          { icon: '', label: 'Running(Dataset 1)', value: '56' },
          { icon: '', label: 'Eating(Dataset 2)', value: '28' },
          { icon: '', label: 'Drinking(Dataset 2)', value: '48' },
          { icon: '', label: 'Sleeping(Dataset 2)', value: '40' },
          { icon: '', label: 'Cycling(Dataset 2)', value: '19' },
          { icon: '', label: 'Running(Dataset 2)', value: '96' },
          { icon: '', label: 'Eating(Dataset 3)', value: '45' },
          { icon: '', label: 'Drinking(Dataset 3)', value: '67' },
          { icon: '', label: 'Sleeping(Dataset 3)', value: '72' },
          { icon: '', label: 'Cycling(Dataset 3)', value: '83' },
          { icon: '', label: 'Running(Dataset 3)', value: '90' }
        ],
        optionType: 'static',
        ...commonRenderProps,
        ...commonPegaProps,
        width: '400px',
        height: '400px',
        showGrid: true,
        showAngleLines: true,
        startFromZero: true,
        showTicks: true,
        showLegend: true,
        legendPosition: 'top',
        borderWidth: 1,
        pointSize: 3,
        pointHoverSize: 5,
        gridColor: 'rgba(0, 0, 0, 0.1)',
        angleLineColor: 'rgba(0, 0, 0, 0.1)',
        legendColor: '#000',
        legendFontSize: 12,
        labelColor: '#000',
        labelFontSize: 12,
        tooltipBgColor: 'white',
        tooltipColor: '#000',
        tooltipFontSize: 12,
        tooltipFontWeight: 500,
        datasetLabel: 'Radar Chart',
        fontSize: 16,
        fontWeight: 'bold',
        labelcolor: '#000',
        visibility: 'visible',
        pegaConfig: [
          {
            name: 'label',
            label: 'Label',
            format: 'TEXT'
          },
          {
            name: 'cardItemLabel',
            label: 'Label on each Card',
            format: 'TEXT'
          },
          {
            name: 'cardItemIcon',
            label: 'Icon on each Card',
            format: 'TEXT'
          },
          {
            name: 'dataPage',
            label: 'List Data Page name to get all cases',
            format: 'TEXT',
            required: true
          },
          {
            name: 'createClassname',
            label: 'Create case className (empty to disable create)',
            format: 'TEXT'
          }
        ]
      },
      renderComponent: function (props) {
        const updatedConfig = updateLabelsWithProps(props.pegaConfig, props);
        props = { ...props, pegaConfig: updatedConfig };
        const linkRef = getLink(props);
        let data = props.data;
        let options = props.options;

        if (!props.liveMode && typeof data === 'string' && data.indexOf('{{') >= 0) {
          data = props._designerProps?.data || [];
        }

        if (!props.liveMode && typeof options === 'string' && options.indexOf('{{') >= 0) {
          options = props._designerProps?.options || [];
        }

        const finalData = options || data;

        return props.liveMode && linkRef ? (
          <a href={linkRef}>
            <RadarChartJS {...props} data={finalData} />
          </a>
        ) : (
          <RadarChartJS {...props} data={finalData} />
        );
      }
    },
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      properties: commonProps.concat(commonPegaPropDefinitions, [
        { name: 'xAxisLabel' },
        { name: 'yAxisLabel' },
        { name: 'showGrid', type: 'boolean' },
        { name: 'gridColor' },
        { name: 'pointSize' },
        { name: 'pointHoverSize' },
        { name: 'borderWidth' },
        { name: 'borderColor' },
        { name: 'showLegend', type: 'boolean' },
        { name: 'legendPosition', options: ['top', 'bottom', 'left', 'right'] },
        { name: 'legendColor' },
        { name: 'legendFontSize' },
        { name: 'labelColor' },
        { name: 'labelFontSize' },
        { name: 'tickColor' },
        { name: 'tickFontSize' },
        { name: 'tooltipBgColor' },
        { name: 'tooltipColor' },
        { name: 'tooltipFontSize' },
        { name: 'tooltipFontWeight' },
        { name: 'datasetLabel' },
        { name: 'titleFontSize' },
        { name: 'titleFontWeight' },
        { name: 'optionType', options: ['static', 'dynamic'] },
        {
          name: 'options',
          type: 'menuOptions',
          i18n: true
        }
      ]),
      id: 'scatterchartjs',
      designerProps: {},
      renderProps: {
        options: [
          { icon: '', label: 'Point 1(Dataset 1)', value: 'x: -10, y: 0' },
          { icon: '', label: 'Point 2(Dataset 1)', value: 'x: 0, y: 10' },
          { icon: '', label: 'Point 3(Dataset 1)', value: 'x: 1, y: 5.5' },
          { icon: '', label: 'Point 4(Dataset 1)', value: 'x: 10, y: 5' },
          { icon: '', label: 'Point 1(Dataset 2)', value: 'x: -5, y: 5' },
          { icon: '', label: 'Point 2(Dataset 2)', value: 'x: 0, y: 7' },
          { icon: '', label: 'Point 3(Dataset 2)', value: 'x: 5, y: 9' },
          { icon: '', label: 'Point 4(Dataset 2)', value: 'x: 8, y: 2' }
        ],
        optionType: 'static',
        ...commonRenderProps,
        ...commonPegaProps,
        width: '400px',
        height: '400px',
        showGrid: true,
        pointSize: 4,
        pointHoverSize: 6,
        borderWidth: 1,
        showLegend: true,
        legendPosition: 'top',
        legendColor: '#000',
        legendFontSize: 12,
        labelColor: '#000',
        labelFontSize: 12,
        tickColor: '#000',
        tickFontSize: 12,
        tooltipBgColor: 'white',
        tooltipColor: '#000',
        tooltipFontSize: 12,
        tooltipFontWeight: 500,
        datasetLabel: 'Scatter Chart',
        titleFontSize: 16,
        titleFontWeight: 'bold',
        xAxisLabel: 'X Axis',
        yAxisLabel: 'Y Axis',
        visibility: 'visible',
        pegaConfig: [
          {
            name: 'label',
            label: 'Label',
            format: 'TEXT'
          },
          {
            name: 'cardItemLabel',
            label: 'Label on each Card',
            format: 'TEXT'
          },
          {
            name: 'cardItemIcon',
            label: 'Icon on each Card',
            format: 'TEXT'
          },
          {
            name: 'dataPage',
            label: 'List Data Page name to get all cases',
            format: 'TEXT',
            required: true
          },
          {
            name: 'createClassname',
            label: 'Create case className (empty to disable create)',
            format: 'TEXT'
          }
        ]
      },
      renderComponent: function (props) {
        const updatedConfig = updateLabelsWithProps(props.pegaConfig, props);
        props = { ...props, pegaConfig: updatedConfig };
        const linkRef = getLink(props);
        let data = props.data;
        let options = props.options;

        if (!props.liveMode && typeof data === 'string' && data.indexOf('{{') >= 0) {
          data = props._designerProps?.data || [];
        }

        if (!props.liveMode && typeof options === 'string' && options.indexOf('{{') >= 0) {
          options = props._designerProps?.options || [];
        }

        const finalData = options || data;

        return props.liveMode && linkRef ? (
          <a href={linkRef}>
            <ScatterChartJS {...props} data={finalData} />
          </a>
        ) : (
          <ScatterChartJS {...props} data={finalData} />
        );
      }
    }
  ]
};
