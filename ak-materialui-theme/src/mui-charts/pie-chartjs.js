import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import { isNonEmptyArray } from '../utils/common-utils';

export const PieChartJS = (props) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  const getChartData = () => {
    if (props.liveMode && props.options) {
      return props.options;
    }
    if (isNonEmptyArray(props.designerProps)) {
      return props.designerProps;
    }
    if (isNonEmptyArray(props.options)) {
      return props.options;
    }
    return props.data || [];
  };

  useEffect(() => {
    if (props.visibility === 'hidden') {
      return;
    }

    const chartData = getChartData();

    // Default colors
    const defaultColors = [
      'rgba(255, 99, 132, 0.5)',
      'rgba(75, 192, 192, 0.5)',
      'rgba(255, 205, 86, 0.5)',
      'rgba(201, 203, 207, 0.5)',
      'rgba(54, 162, 235, 0.5)'
    ];

    const defaultBorderColors = [
      'rgb(255, 99, 132)',
      'rgb(75, 192, 192)',
      'rgb(255, 205, 86)',
      'rgb(201, 203, 207)',
      'rgb(54, 162, 235)'
    ];

    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');

    const data = {
      labels: isNonEmptyArray(chartData) ? chartData.map((item) => item.label) : [],
      datasets: [
        {
          label: props.datasetLabel || 'Dataset',
          data: isNonEmptyArray(chartData) ? chartData.map((item) => parseFloat(item.value)) : [],
          backgroundColor: isNonEmptyArray(chartData)
            ? chartData.map((item, index) => item.bgColor || defaultColors[index % defaultColors.length])
            : defaultColors,
          borderColor: props.borderColor ? Array(chartData.length).fill(props.borderColor) : defaultBorderColors,
          borderWidth: Number(props.borderWidth) || 1,
          hoverOffset: Number(props.hoverOffset) || 4
        }
      ]
    };

    chartInstance.current = new Chart(ctx, {
      type: 'pie',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: data.datasets[0].label || 'Dataset',
            font: {
              size: props.fontSize || 16,
              weight: props.fontWeight || 'bold'
            },
            color: props.labelcolor || '#000',
            padding: {
              top: 5,
              bottom: 5
            }
          },
          legend: {
            display: Boolean(props.showLegend),
            position: props.legendPosition || 'top',
            labels: {
              color: props.legendColor || '#000',
              font: {
                size: Number(props.legendFontSize) || 12
              }
            }
          },
          tooltip: {
            backgroundColor: props.tooltipBgColor || 'white',
            titleColor: props.tooltipColor || '#000',
            bodyColor: props.tooltipColor || '#000',
            titleFont: {
              size: Number(props.tooltipFontSize) || 12,
              weight: Number(props.tooltipFontWeight) || 500
            },
            bodyFont: {
              size: Number(props.tooltipFontSize) || 12,
              weight: Number(props.tooltipFontWeight) || 500
            },
            padding: 12,
            displayColors: false,
            callbacks: {
              title: (tooltipItems) => {
                const index = tooltipItems[0].dataIndex;
                return chartData[index].label;
              },
              label: (context) => {
                const value = context.parsed;
                return [`${props.currencies === 'none' ? '' : props.currencies || '€'}${value}`, props.tooltipText];
              }
            }
          }
        }
      }
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [props]);

  if (props.visibility === 'hidden') {
    return null;
  }

  return (
    <div style={{ height: props.height || 400, width: '100%' }}>
      <div style={{ width: '100%', height: '100%', padding: '16px' }}>
        <canvas ref={chartRef} />
      </div>
    </div>
  );
};

export default PieChartJS;
