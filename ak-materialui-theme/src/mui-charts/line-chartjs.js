import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import { isNonEmptyArray } from '../utils/common-utils';

const LineChartComponent = (props) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  const getChartData = () => {
    if (props.liveMode && props.options) {
      return props.options;
    }
    if (isNonEmptyArray(props.designerProps)) {
      return props.designerProps;
    }
    if (isNonEmptyArray(props.options)) {
      return props.options;
    }
    return props.data || [];
  };

  useEffect(() => {
    if (props.visibility === 'hidden') {
      return;
    }

    const chartData = getChartData();

    // Default colors
    const defaultColors = {
      lineColor: '#4BC0C0',
      pointColor: '#4BC0C0',
      fillColor: 'rgba(75, 192, 192, 0.2)'
    };

    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');

    const data = {
      labels: isNonEmptyArray(chartData) ? chartData.map((item) => item.label) : [],
      datasets: [
        {
          label: props.datasetLabel || 'My First Dataset',
          data: isNonEmptyArray(chartData) ? chartData.map((item) => parseFloat(item.value)) : [],
          borderColor: props.lineColor || defaultColors.lineColor,
          backgroundColor: props.fillColor || defaultColors.fillColor,
          pointBackgroundColor: props.pointColor || defaultColors.pointColor,
          pointBorderColor: props.pointBorderColor || defaultColors.pointColor,
          pointRadius: Number(props.pointSize) || 4,
          pointHoverRadius: Number(props.pointHoverSize) || 6,
          borderWidth: Number(props.lineWidth) || 2,
          fill: Boolean(props.fillArea),
          tension: Number(props.lineTension) || 0.4
        }
      ]
    };

    chartInstance.current = new Chart(ctx, {
      type: 'line',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: Boolean(props.startFromZero),
            grid: {
              display: Boolean(props.showGrid),
              color: props.gridColor || '#ddd'
            },
            ticks: {
              color: props.yAxisLabelColor || '#000',
              font: {
                size: Number(props.yAxisFontSize) || 12
              }
            }
          },
          x: {
            grid: {
              display: Boolean(props.showGrid),
              color: props.gridColor || '#ddd'
            },
            ticks: {
              color: props.xAxisLabelColor || '#000',
              font: {
                size: Number(props.xAxisFontSize) || 12
              }
            }
          }
        },
        plugins: {
          legend: {
            display: Boolean(props.showLegend),
            position: props.legendPosition || 'top',
            labels: {
              color: props.legendColor || '#000',
              font: {
                size: Number(props.legendFontSize) || 12
              }
            }
          },
          tooltip: {
            backgroundColor: props.tooltipBgColor || 'white',
            titleColor: props.tooltipColor || '#000',
            bodyColor: props.tooltipColor || '#000',
            titleFont: {
              size: Number(props.tooltipFontSize) || 12,
              weight: Number(props.tooltipFontWeight) || 500
            },
            bodyFont: {
              size: Number(props.tooltipFontSize) || 12,
              weight: Number(props.tooltipFontWeight) || 500
            },
            padding: 12,
            displayColors: false,
            callbacks: {
              title: (tooltipItems) => {
                const index = tooltipItems[0].dataIndex;
                return chartData[index].label;
              },
              label: (context) => {
                const value = context.parsed.y;
                return [`${props.currencies === 'none' ? '' : props.currencies || '€'}${value}`, props.tooltipText];
              }
            }
          }
        }
      }
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [props]);

  if (props.visibility === 'hidden') {
    return null;
  }

  return (
    <div style={{ height: props.height || 400, width: '100%' }}>
      <div style={{ width: '100%', height: '100%', padding: '16px' }}>
        <canvas ref={chartRef} />
      </div>
    </div>
  );
};

export default LineChartComponent;
