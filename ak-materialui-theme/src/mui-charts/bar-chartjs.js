import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import { isNonEmptyArray } from '../utils/common-utils';

const BarChartComponent = (props) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  const getChartData = () => {
    if (props.liveMode && props.options) {
      return props.options;
    }
    if (isNonEmptyArray(props.designerProps)) {
      return props.designerProps;
    }
    if (isNonEmptyArray(props.options)) {
      return props.options;
    }
    return props.data || [];
  };

  useEffect(() => {
    if (props.visibility === 'hidden') {
      return;
    }

    const chartData = getChartData();

    // Default colors
    const defaultColors = {
      lastMonthBgColor: 'red',
      hoverBgColor: '#666',
      labelColor: '#fff'
    };

    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');

    const data = {
      labels: isNonEmptyArray(chartData) ? chartData.map((item) => item.label) : [],
      datasets: [
        {
          data: isNonEmptyArray(chartData) ? chartData.map((item) => parseFloat(item.value)) : [],
          backgroundColor: isNonEmptyArray(chartData)
            ? chartData.map((_, index) =>
                index === chartData.length - 1
                  ? props.latestbillcolor || defaultColors.lastMonthBgColor
                  : props.barbgcolor
              )
            : [],
          borderColor: props.barbordercolor || '#000',
          borderWidth: 0,
          borderRadius: props.barStyle === 'rectangle' ? 0 : 8,
          barThickness: Number(props.barSize) || 16,
          hoverBackgroundColor: props.activecolor || '#fff',
          hoverBorderWidth: 2,
          hoverBorderColor: props.barbordercolor || '#000'
        }
      ]
    };

    chartInstance.current = new Chart(ctx, {
      type: 'bar',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              display: false
            },
            ticks: {
              color: props.labelcolor || '#000',
              font: {
                size: Number(props.labelfontsize) || 12
              }
            }
          },
          x: {
            grid: {
              display: false
            },
            ticks: {
              color: props.labelcolor || '#000',
              font: {
                size: Number(props.labelfontsize) || 12
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: props.tooltipbgcolor || 'white',
            titleColor: props.tooltipcolor || '#000',
            bodyColor: props.tooltipcolor || '#000',
            titleFont: {
              size: Number(props.tooltipfontsize) || Number(props.labelfontsize) || 12,
              weight: Number(props.tooltipfontweight) || Number(props.fontWeight) || 500
            },
            bodyFont: {
              size: Number(props.tooltipfontsize) || Number(props.labelfontsize) || 12,
              weight: Number(props.tooltipfontweight) || Number(props.fontWeight) || 500
            },
            padding: 12,
            displayColors: false,
            callbacks: {
              title: (tooltipItems) => {
                const index = tooltipItems[0].dataIndex;
                return chartData[index].label;
              },
              label: (context) => {
                const index = context.dataIndex;
                const value = chartData[index].value;
                const isLast = index === chartData.length - 1;
                const tooltipText = isLast ? props.lastbartext || 'Latest Bill' : props.tooltiptext || 'Monthly Bill';
                return [`${props.currencies === 'none' ? '' : props.currencies || '€'}${value}`, tooltipText];
              }
            }
          }
        }
      }
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [props]);

  if (props.visibility === 'hidden') {
    return null;
  }

  return (
    <div style={{ height: props.height || 400, width: '100%', overflowX: 'auto' }}>
      <div style={{ minWidth: '800px', width: 'max-content', height: '100%', padding: '32px' }}>
        <canvas ref={chartRef} />
      </div>
    </div>
  );
};

export default BarChartComponent;
