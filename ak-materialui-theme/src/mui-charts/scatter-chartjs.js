import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import { isNonEmptyArray } from '../utils/common-utils';

const ScatterChartComponent = (props) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  const getChartData = () => {
    if (props.liveMode && isNonEmptyArray(props.options)) {
      return props.options;
    }
    if (isNonEmptyArray(props.designerProps)) {
      return props.designerProps;
    }
    if (isNonEmptyArray(props.options)) {
      return props.options;
    }
    return props.data || [];
  };

  const parseLabel = (labelText) => {
    const match = labelText.match(/^(.*?)\((.*?)\)$/);
    if (match) {
      return {
        label: match[1].trim(),
        dataset: match[2].trim()
      };
    }
    return {
      label: labelText,
      dataset: 'Dataset 1'
    };
  };

  const processColor = (color) => {
    if (!color) return null;

    // Handle named colors
    if (typeof color === 'string' && !color.startsWith('rgb') && !color.startsWith('#')) {
      return color;
    }

    // Handle hex colors
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return {
        solid: `rgb(${r}, ${g}, ${b})`,
        transparent: `rgba(${r}, ${g}, ${b}, 0.2)`
      };
    }

    // Handle rgb/rgba colors
    if (color.startsWith('rgb')) {
      const isRgba = color.startsWith('rgba');
      if (isRgba) {
        return {
          solid: color.replace(/,[^,]*\)/, ',1)'),
          transparent: color.replace(/,[^,]*\)/, ',0.2)')
        };
      } else {
        return {
          solid: color,
          transparent: color.replace('rgb', 'rgba').replace(')', ',0.2)')
        };
      }
    }

    return color;
  };

  useEffect(() => {
    if (props.visibility === 'hidden') {
      return;
    }

    const chartData = getChartData();

    // Default colors for datasets
    const defaultDatasetColors = [
      {
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgb(255, 99, 132)',
        pointBackgroundColor: 'rgb(255, 99, 132)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(255, 99, 132)'
      },
      {
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgb(54, 162, 235)',
        pointBackgroundColor: 'rgb(54, 162, 235)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(54, 162, 235)'
      },
      {
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        borderColor: 'rgb(75, 192, 192)',
        pointBackgroundColor: 'rgb(75, 192, 192)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(75, 192, 192)'
      },
      {
        backgroundColor: 'rgba(255, 159, 64, 0.2)',
        borderColor: 'rgb(255, 159, 64)',
        pointBackgroundColor: 'rgb(255, 159, 64)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(255, 159, 64)'
      },
      {
        backgroundColor: 'rgba(153, 102, 255, 0.2)',
        borderColor: 'rgb(153, 102, 255)',
        pointBackgroundColor: 'rgb(153, 102, 255)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(153, 102, 255)'
      }
    ];

    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');

    // Process data and extract dataset information from labels
    const processedData = isNonEmptyArray(chartData)
      ? chartData.map((item) => {
          const coordinates = item.value.match(/x:\s*(-?\d+\.?\d*)\s*,\s*y:\s*(-?\d+\.?\d*)/);
          return {
            ...item,
            ...parseLabel(item.label),
            processedColor: processColor(item.bgColor),
            coordinates: coordinates
              ? {
                  x: parseFloat(coordinates[1]),
                  y: parseFloat(coordinates[2])
                }
              : null
          };
        })
      : [];

    // Get unique datasets
    const uniqueDatasets = isNonEmptyArray(processedData)
      ? [...new Set(processedData.map((item) => item.dataset))]
      : [];

    // Create a map of dataset colors
    const datasetColors = {};
    processedData.forEach((item) => {
      if (item.processedColor) {
        datasetColors[item.dataset] = item.processedColor;
      }
    });

    // Group data by dataset
    const datasets = [];

    if (isNonEmptyArray(processedData)) {
      uniqueDatasets.forEach((datasetName, index) => {
        const datasetPoints = processedData.filter((item) => item.dataset === datasetName);
        const defaultColor = defaultDatasetColors[index % defaultDatasetColors.length];

        // Use the color from the datasetColors map
        const color = datasetColors[datasetName];

        const colorSet = color
          ? {
              backgroundColor: typeof color === 'string' ? color : color.transparent,
              borderColor: typeof color === 'string' ? color : color.solid,
              pointBackgroundColor: typeof color === 'string' ? color : color.solid,
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: typeof color === 'string' ? color : color.solid
            }
          : defaultColor;

        datasets.push({
          label: datasetName,
          data: datasetPoints.filter((point) => point.coordinates).map((point) => point.coordinates),
          ...colorSet,
          borderWidth: Number(props.borderWidth) || 1,
          pointRadius: Number(props.pointSize) || 4,
          pointHoverRadius: Number(props.pointHoverSize) || 6
        });
      });
    }

    const data = { datasets };

    chartInstance.current = new Chart(ctx, {
      type: 'scatter',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            type: 'linear',
            position: 'bottom',
            title: {
              display: true,
              text: props.xAxisLabel || 'X Axis',
              color: props.labelColor || '#000',
              font: {
                size: Number(props.labelFontSize) || 12
              }
            },
            grid: {
              display: Boolean(props.showGrid),
              color: props.gridColor || '#ddd'
            },
            ticks: {
              color: props.tickColor || '#000',
              font: {
                size: Number(props.tickFontSize) || 12
              }
            }
          },
          y: {
            title: {
              display: true,
              text: props.yAxisLabel || 'Y Axis',
              color: props.labelColor || '#000',
              font: {
                size: Number(props.labelFontSize) || 12
              }
            },
            grid: {
              display: Boolean(props.showGrid),
              color: props.gridColor || '#ddd'
            },
            ticks: {
              color: props.tickColor || '#000',
              font: {
                size: Number(props.tickFontSize) || 12
              }
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: props.datasetLabel || 'Scatter Chart',
            color: props.labelColor || '#000',
            font: {
              size: Number(props.titleFontSize) || 16,
              weight: props.titleFontWeight || 'bold'
            },
            padding: {
              top: 10,
              bottom: 10
            }
          },
          legend: {
            display: Boolean(props.showLegend),
            position: props.legendPosition || 'top',
            labels: {
              color: props.legendColor || '#000',
              font: {
                size: Number(props.legendFontSize) || 12
              }
            }
          },
          tooltip: {
            backgroundColor: props.tooltipBgColor || 'white',
            titleColor: props.tooltipColor || '#000',
            bodyColor: props.tooltipColor || '#000',
            titleFont: {
              size: Number(props.tooltipFontSize) || 12,
              weight: Number(props.tooltipFontWeight) || 500
            },
            bodyFont: {
              size: Number(props.tooltipFontSize) || 12,
              weight: Number(props.tooltipFontWeight) || 500
            },
            padding: 12,
            displayColors: true,
            callbacks: {
              label: (context) => {
                const point = context.raw;
                return `${context.dataset.label}: x: ${point.x}, y: ${point.y}`;
              }
            }
          }
        }
      }
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [props]);

  if (props.visibility === 'hidden') {
    return null;
  }

  return (
    <div style={{ height: props.height || 400, width: '100%' }}>
      <div style={{ width: '100%', height: '100%', padding: '16px' }}>
        <canvas ref={chartRef} />
      </div>
    </div>
  );
};

export default ScatterChartComponent;
