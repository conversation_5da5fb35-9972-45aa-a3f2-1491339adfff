import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import Typography from '@mui/material/Typography';
import Collapse from '@mui/material/Collapse';
import useMediaQuery from '@mui/material/useMediaQuery';
import IconButton from '@mui/material/IconButton';
import InputBase from '@mui/material/InputBase';
import { IconComponent } from '../utils/icon-migration';
import { isNonEmptyArray } from '../utils/common-utils';

const MOBILE_BREAKPOINT = 789;

// Background colors
const BG_COLORS = {
  ACTIVE: '#f8f8f8',
  HOVER: 'rgba(0, 0, 0, 0.02)',
  TRANSPARENT: 'transparent'
};

const SearchBar = ({
  onSearch,
  placeholder,
  bgColor,
  placeholderColor,
  placeholderFontSize,
  placeholderFontWeight
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: bgColor || BG_COLORS.TRANSPARENT,
        borderRadius: '4px',
        py: '6px',
        px: '12px',
        width: '100%'
      }}
    >
      <IconComponent iconName="Search" sx={{ color: 'text.secondary', mr: 2 }} />
      <InputBase
        fullWidth
        placeholder={placeholder}
        inputProps={{
          'aria-label': 'search',
          style: {
            fontSize: placeholderFontSize || '16px',
            fontWeight: placeholderFontWeight || '400'
          }
        }}
        onChange={(e) => onSearch(e.target.value)}
        sx={{
          '& .MuiInputBase-input::placeholder': {
            color: placeholderColor || '#666',
            opacity: 1
          }
        }}
      />
    </Box>
  );
};

const TopSearchBar = ({ onSearch, placeholder, bgColor, placeholderColor, placeholderFontSize }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: bgColor || BG_COLORS.TRANSPARENT,
        borderRadius: '4px',
        py: '6px',
        px: '12px',
        width: '100%',
        position: 'relative'
      }}
    >
      <InputBase
        fullWidth
        placeholder={placeholder}
        inputProps={{
          'aria-label': 'search',
          style: {
            fontSize: placeholderFontSize || '16px'
          }
        }}
        onChange={(e) => onSearch(e.target.value)}
        sx={{
          '& .MuiInputBase-input::placeholder': {
            color: placeholderColor || '#666',
            opacity: 1
          }
        }}
      />
      <IconComponent
        iconName="Search"
        sx={{
          color: 'text.secondary',
          position: 'absolute',
          right: '12px'
        }}
      />
    </Box>
  );
};

const PegaDrawer2 = ({
  options,
  width = '300px',
  maintextcolor,
  subtextcolor,
  nestedtextcolor,
  mainfontsize,
  subfontsize,
  nestedfontsize,
  mainfontweight,
  subfontweight,
  nestedfontweight,
  topsearchbarbgcolor,
  topsearchbarplaceholdercolor,
  topsearchbarplaceholderfontsize,
  topsearchbarplaceholdertext,
  bottomsearchbarbgcolor,
  bottomsearchbarplaceholdercolor,
  bottomsearchbarplaceholderfontsize,
  bottomsearchbarplaceholdertext,
  bottomsearchbarplaceholderfontweight,
  activebgcolor,
  visibility,
  height,
  icon,
  liveMode,
  designerProps,
  data
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [openItems, setOpenItems] = useState({});
  const [searchQueries, setSearchQueries] = useState({});
  const [mainSearch, setMainSearch] = useState('');
  const isMobile = useMediaQuery(`(max-width:${MOBILE_BREAKPOINT}px)`);

  // Improved options handling logic
  const getDrawerData = () => {
    console.log('Live Mode:', liveMode);
    console.log('Options:', options);
    console.log('Designer Props:', designerProps);
    console.log('Data:', data);

    let finalData;
    if (liveMode && options) {
      finalData = options;
    } else if (isNonEmptyArray(designerProps)) {
      finalData = designerProps;
    } else if (isNonEmptyArray(options)) {
      finalData = options;
    } else {
      finalData = data || [];
    }

    console.log('Final Data being used:', finalData);
    return finalData;
  };

  // Background colors
  const BG_COLORS = {
    ACTIVE: activebgcolor || '#f8f8f8',
    HOVER: 'rgba(0, 0, 0, 0.02)',
    TRANSPARENT: 'transparent'
  };

  if (visibility === 'hidden') {
    return null;
  }

  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleItemClick = (itemId, hasChildren) => {
    if (hasChildren) {
      setOpenItems((prev) => ({
        ...prev,
        [itemId]: !prev[itemId]
      }));
      // Clear search when closing a submenu
      if (!openItems[itemId]) {
        setSearchQueries((prev) => ({
          ...prev,
          [itemId]: ''
        }));
      }
    }
  };

  const hasNestedItems = (item) => {
    return isNonEmptyArray(item.submenu) || isNonEmptyArray(item.nestedSubMenu);
  };

  const getMainItemBgColor = (item) => {
    if (openItems[item.label]) {
      return BG_COLORS.ACTIVE;
    }
    return BG_COLORS.TRANSPARENT;
  };

  const handleSearch = (query, parentId) => {
    setSearchQueries((prev) => ({
      ...prev,
      [parentId]: query.toLowerCase()
    }));
  };

  const filterSubmenuItems = (items, parentId) => {
    const searchQuery = searchQueries[parentId] || '';
    if (!searchQuery) return items;

    return items.filter((item) => item.label.toLowerCase().includes(searchQuery));
  };

  // Different styles for each menu level
  const mainMenuTextStyles = {
    fontSize: mainfontsize || '16px',
    fontWeight: mainfontweight || '500',
    color: maintextcolor || '#2c3e50',
    paddingLeft: 0,
    textTransform: 'none'
  };

  const subMenuTextStyles = {
    fontSize: subfontsize || '16px',
    fontWeight: subfontweight || '400',
    color: subtextcolor || '#2c3e50',
    paddingLeft: 0,
    opacity: 0.87
  };

  const nestedMenuTextStyles = {
    fontSize: nestedfontsize || '14px',
    fontWeight: nestedfontweight || '400',
    color: nestedtextcolor || '#2c3e50',
    paddingLeft: 0,
    opacity: 0.75
  };

  // Get and filter the data
  const drawerData = getDrawerData();
  console.log('Drawer Data before filtering:', drawerData);

  const filteredOptions = isNonEmptyArray(drawerData)
    ? drawerData.filter((item) => {
        console.log('Filtering item:', item);
        return item.label.toLowerCase().includes((mainSearch || '').toLowerCase());
      })
    : [];

  console.log('Filtered Options:', filteredOptions);

  return (
    <div>
      {visibility && (
        <IconButton
          onClick={handleMenuOpen}
          edge="start"
          aria-label="menu"
          aria-haspopup="true"
          sx={{
            width: width || '120px',
            height: height || '120px',
            padding: '12px'
          }}
        >
          <span
            style={{
              width: width ? `${parseInt(width) * 0.5}px` : '60px',
              height: height ? `${parseInt(height) * 0.5}px` : '60px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            dangerouslySetInnerHTML={{
              __html: menuAnchorEl
                ? `<svg width="24" height="24" viewBox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>`
                : decodeURIComponent(icon) ||
                  `<svg width="24" height="24" viewBox="0 0 24 24"><path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" /></svg>`
            }}
          />
        </IconButton>
      )}
      <Menu
        id="menu-list"
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        MenuListProps={{
          'aria-labelledby': 'menu-button'
        }}
        PaperProps={{
          sx: {
            width: isMobile ? '100%' : width,
            maxHeight: '90vh',
            overflowY: 'auto',
            mt: 1,
            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
            borderRadius: '4px',
            p: 0
          }
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left'
        }}
      >
        {/* Top search bar for main options */}
        <ListItemButton sx={{ bottom: 8, px: 0, py: 0 }}>
          <TopSearchBar
            onSearch={setMainSearch}
            placeholder={topsearchbarplaceholdertext || 'Search main options'}
            bgColor={topsearchbarbgcolor || '#fff'}
            placeholderColor={topsearchbarplaceholdercolor || '#666'}
            placeholderFontSize={topsearchbarplaceholderfontsize || '16px'}
          />
        </ListItemButton>
        <List sx={{ pt: 0, pb: 0 }}>
          {console.log('About to render options:', filteredOptions)}
          {Array.isArray(filteredOptions) && filteredOptions.length > 0 ? (
            filteredOptions.map((item, index) => {
              console.log('Rendering item:', item, 'at index:', index);
              const filteredSubmenu = item.submenu ? filterSubmenuItems(item.submenu, item.label) : [];

              return (
                <React.Fragment key={index}>
                  <ListItem
                    disablePadding
                    sx={{
                      backgroundColor: getMainItemBgColor(item),
                      mb: 0
                    }}
                  >
                    <ListItemButton
                      onClick={() => handleItemClick(item.label, hasNestedItems(item))}
                      sx={{
                        py: 2,
                        px: 2,
                        '&:hover': {
                          backgroundColor: BG_COLORS.HOVER
                        },
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {item.icon && (
                          <span
                            style={{ position: 'relative', left: 0, marginRight: 15 }}
                            dangerouslySetInnerHTML={{
                              __html: decodeURIComponent(item.icon)
                            }}
                          />
                        )}
                        <Typography sx={mainMenuTextStyles}>{item.label}</Typography>
                      </Box>
                      {hasNestedItems(item) && (
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            transition: 'transform 0.3s',
                            transform: openItems[item.label] ? 'rotate(180deg)' : 'rotate(0deg)'
                          }}
                        >
                          <IconComponent iconName="ExpandMore" />
                        </Box>
                      )}
                    </ListItemButton>
                  </ListItem>

                  {isNonEmptyArray(item.submenu) && openItems[item.label] && (
                    <>
                      {index === 0 && (
                        <ListItem disablePadding sx={{ pt: 2 }}>
                          <ListItemButton sx={{ py: 0, px: 0, bottom: 4 }}>
                            <SearchBar
                              onSearch={(query) => handleSearch(query, item.label)}
                              placeholder={bottomsearchbarplaceholdertext || 'Search options'}
                              bgColor={bottomsearchbarbgcolor || '#f8f8f8'}
                              placeholderColor={bottomsearchbarplaceholdercolor || '#666'}
                              placeholderFontSize={bottomsearchbarplaceholderfontsize || '16px'}
                              placeholderFontWeight={bottomsearchbarplaceholderfontweight || '400'}
                            />
                          </ListItemButton>
                        </ListItem>
                      )}
                      <Collapse in={openItems[item.label]} timeout="auto" unmountOnExit>
                        <List component="div" disablePadding sx={{ pl: 2 }}>
                          {filteredSubmenu.map((submenuItem, submenuIndex) => {
                            const submenuItemId = `${item.label}-${submenuItem.label}`;
                            return (
                              <React.Fragment key={`${index}-sub-${submenuIndex}`}>
                                <ListItem
                                  disablePadding
                                  sx={{
                                    borderRadius: '4px',
                                    mb: 0.5
                                  }}
                                >
                                  <ListItemButton
                                    onClick={() => handleItemClick(submenuItemId, hasNestedItems(submenuItem))}
                                    sx={{
                                      py: 1.5,
                                      pl: 2,
                                      pr: 2,
                                      '&:hover': {
                                        backgroundColor: BG_COLORS.HOVER
                                      },
                                      display: 'flex',
                                      justifyContent: 'space-between',
                                      alignItems: 'center'
                                    }}
                                  >
                                    <Typography sx={subMenuTextStyles}>{submenuItem.label}</Typography>
                                    {hasNestedItems(submenuItem) && (
                                      <Box
                                        sx={{
                                          display: 'flex',
                                          alignItems: 'center',
                                          transition: 'transform 0.3s',
                                          transform: openItems[submenuItemId] ? 'rotate(180deg)' : 'rotate(0deg)'
                                        }}
                                      >
                                        <IconComponent iconName="ExpandMore" />
                                      </Box>
                                    )}
                                  </ListItemButton>
                                </ListItem>

                                {isNonEmptyArray(submenuItem.nestedSubMenu) && (
                                  <Collapse in={openItems[submenuItemId]} timeout="auto" unmountOnExit>
                                    <List component="div" disablePadding sx={{ pl: 2 }}>
                                      {submenuItem.nestedSubMenu.map((nestedItem, nestedIndex) => {
                                        const nestedItemId = `${submenuItemId}-${nestedItem.label}`;
                                        return (
                                          <ListItem
                                            key={`${index}-sub-${submenuIndex}-nested-${nestedIndex}`}
                                            disablePadding
                                            sx={{
                                              borderRadius: '4px',
                                              mb: 0.5
                                            }}
                                          >
                                            <ListItemButton
                                              onClick={() => handleItemClick(nestedItemId, false)}
                                              sx={{
                                                py: 1.5,
                                                pl: 2,
                                                pr: 2,
                                                '&:hover': {
                                                  backgroundColor: BG_COLORS.HOVER
                                                }
                                              }}
                                            >
                                              <Typography sx={nestedMenuTextStyles}>{nestedItem.label}</Typography>
                                            </ListItemButton>
                                          </ListItem>
                                        );
                                      })}
                                    </List>
                                  </Collapse>
                                )}
                              </React.Fragment>
                            );
                          })}
                        </List>
                      </Collapse>
                    </>
                  )}
                </React.Fragment>
              );
            })
          ) : (
            <ListItem>
              <Typography sx={{ p: 2, color: 'text.secondary' }}>No items to display</Typography>
            </ListItem>
          )}
        </List>
      </Menu>
    </div>
  );
};

export default PegaDrawer2;
