import React, { useRef, useState, useEffect, useLayoutEffect } from 'react';

import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import Tooltip from '@mui/material/Tooltip';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import getAllFields from '../table-pagination/utils';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

const generateTimeSlots = (start, end, interval = 20) => {
  const result = [];
  const pad = (n) => (n < 10 ? '0' + n : n);
  let [h, m] = start.split(':').map(Number);
  const [eh, em] = end.split(':').map(Number);

  while (h < eh || (h === eh && m < em)) {
    result.push(`${pad(h)}:${pad(m)}`);
    m += interval;
    if (m >= 60) {
      h += 1;
      m -= 60;
    }
  }
  return result;
};

const parseParametersSafely = (queryString) => {
  const params = {};
  let currentKey = '';
  let currentValue = '';
  let inQuotes = false;

  for (let i = 0; i < queryString.length; i++) {
    const char = queryString[i];

    if (char === '"') {
      inQuotes = !inQuotes;
    }

    if (char === '=' && !inQuotes && !currentKey) {
      currentKey = currentValue;
      currentValue = '';
    } else if (char === '&' && !inQuotes) {
      params[currentKey.trim()] = currentValue.trim();
      currentKey = '';
      currentValue = '';
    } else {
      currentValue += char;
    }
  }

  if (currentKey) {
    params[currentKey.trim()] = currentValue.trim();
  }

  return params;
};

const getPegaStyleArrayValues = (rawKey, result) => {
  const match = rawKey.match(/^(\w+)\(\)\.(\w+)$/);
  if (!match) return [];

  const [_, arrayKey, propertyKey] = match; // eslint-disable-line no-unused-vars
  const array = result[arrayKey];
  if (!Array.isArray(array)) return [];

  return array.map((item) => item[propertyKey]);
};

const normalizeTime = (timeStr) => timeStr?.substring(0, 5);

const AppointmentCalendar = (props) => {
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [dynamicScheduleData, setDynamicScheduleData] = useState([]);
  const [dentists, setDentists] = useState([]);
  const [completeData, setCompleteData] = useState({});
  const [timeSlots, setTimeSlots] = useState([]);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [loading, setLoading] = useState(false);
  const [fieldParametersForSlot, setFieldParametersForSlot] = useState([]);

  const containerRef = useRef(null);
  const summaryRef = useRef(null);
  const [summaryHeight, setSummaryHeight] = useState(0);

  useEffect(() => {
    if (summaryRef.current) {
      setSummaryHeight(summaryRef.current.offsetHeight);
    }
  }, [loading, props.summaryPosition, completeData]); // adjust deps if needed

  useLayoutEffect(() => {
    const updateHeights = () => {
      if (summaryRef.current) {
        setSummaryHeight(summaryRef.current.offsetHeight);
      }
    };

    updateHeights();
    window.addEventListener('resize', updateHeights);

    return () => window.removeEventListener('resize', updateHeights);
  }, [loading, props.summaryPosition, completeData]);

  useEffect(() => {
    const fetchData = async () => {
      if (!window.PCore || !window.PCore.getDataApiUtils) return;
      setLoading(true);
      try {
        setSelectedSlot(null);
        const { getPConnect } = props;
        const context = getPConnect().getContextName();
        const fields = getAllFields(getPConnect);
        setFieldParametersForSlot(
          fields.map((field) => ({
            propref: field.propref.split('.').pop().trim()
          }))
        );
        const paramsObj = parseParametersSafely(props.calStructureDPParameters || '');

        if (props.datePickerKey && props.showDatePicker) {
          const date = selectedDate.format('YYYYMMDD');
          paramsObj[props.datePickerKey] = date;
        }
        const response = await window.PCore.getDataPageUtils().getPageDataAsync(
          props.calStructureDataPage,
          context,
          { ...paramsObj },
          {
            invalidateCache: true
          }
        );
        const result = response;
        setCompleteData(result);
        const [startTimeKey, endTimeKey] = props.timeStartEndKeys?.split(',') || [];
        const [breakStartKey, breakEndKey] = props.breakTimeKeys?.split(',') || [];
        const userDataKey = props.userDataKey;
        const userIds = getPegaStyleArrayValues(userDataKey, result);

        let userData = [];

        const columnDisplayName = props.columnDisplayName;
        const match = columnDisplayName && columnDisplayName.match(/^(\w+)\(\)\.(\w+)$/);
        const calenderMatchKeys = userDataKey && userDataKey.match(/^(\w+)\(\)\.(\w+)$/);
        const arrayKey = match?.[1];
        const propertyKey = match?.[2];
        const calendarUsers = result?.[arrayKey] || [];

        try {
          const responses = await Promise.all(
            userIds.map(async (userId) => {
              const response = await window.PCore.getDataApiUtils().getData(
                props.appointmentDataPage,
                { dataViewParameters: { ...paramsObj, [props.parametersUserIDKey]: userId } },
                context
              );
              const userMeta = calendarUsers.find((u) => u[calenderMatchKeys?.[2]] === userId);
              const displayName = userMeta?.[propertyKey] || userId;
              return {
                userId,
                data: response?.data?.data || [],
                label: displayName
              };
            })
          );
          userData = responses;
        } catch (error) {} //eslint-disable-line no-empty

        const times = generateTimeSlots(
          result[startTimeKey?.trim()],
          result[endTimeKey?.trim()],
          result[props.intervalKey] || 20
        );
        setTimeSlots(times);
        const breakStart = result[breakStartKey?.trim()];
        const breakEnd = result[breakEndKey?.trim()];
        const users = userData;
        setDentists(users);

        const toMinutes = (str) => {
          const [h, m] = str.split(':').map(Number);
          return h * 60 + m;
        };

        const breakStartMinutes = toMinutes(breakStart);
        const breakEndMinutes = toMinutes(breakEnd);
        const lastSlotMinutes = toMinutes(times[times.length - 1]);

        const hasSlotAfterBreak = lastSlotMinutes >= breakEndMinutes;

        let disableBeforeTime = null;

        if (props.disableSlotSelection) {
          if (props.showDatePicker) {
            const rawTimestamp = result['pzLoadTime'];
            if (rawTimestamp) {
              disableBeforeTime = dayjs.utc(rawTimestamp, 'MMM D, YYYY [at] h:mm:ss A [GMT]').tz('Europe/London');
            }
          } else {
            const parameterDateTime = paramsObj[props.parameterDateKey];
            disableBeforeTime = parameterDateTime
              ? dayjs.tz(parameterDateTime, 'YYYYMMDD', 'Europe/London')
              : dayjs().tz('Europe/London');
          }
        }

        const schedule = times
          .map((time) => {
            const timeMinutes = toMinutes(time);

            const isBreak = timeMinutes >= breakStartMinutes && timeMinutes < breakEndMinutes;

            if (!hasSlotAfterBreak && isBreak) {
              return null;
            }
            const slot = { time };
            if (props.disableSlotSelection) {
              const nowInLondon = dayjs().tz('Europe/London');

              if (!props.showDatePicker) {
                const parameterDate = paramsObj[props.parameterDateKey]; // 'YYYYMMDD'
                const formattedParamDate = dayjs.tz(parameterDate, 'YYYYMMDD', 'Europe/London');
                const parameterDateString = formattedParamDate.format('YYYY-MM-DD');

                const slotTimeObj = dayjs.tz(
                  `${parameterDateString}T${time}:00`,
                  'YYYY-MM-DDTHH:mm:ss',
                  'Europe/London'
                );

                if (formattedParamDate.isSame(nowInLondon, 'day') && slotTimeObj.isBefore(nowInLondon)) {
                  slot.disabled = true;
                }

                if (formattedParamDate.isBefore(nowInLondon, 'day')) {
                  slot.disabled = true;
                }
              } else if (disableBeforeTime) {
                if (selectedDate.isBefore(disableBeforeTime, 'day')) {
                  slot.disabled = true;
                } else if (selectedDate.isSame(disableBeforeTime, 'day')) {
                  const slotTimeObj = dayjs.tz(
                    `${selectedDate.format('YYYY-MM-DD')}T${time}:00`,
                    'YYYY-MM-DDTHH:mm:ss',
                    'Europe/London'
                  );

                  if (slotTimeObj.isBefore(disableBeforeTime)) {
                    slot.disabled = true;
                  }
                }
              }
            }

            if (hasSlotAfterBreak && isBreak) {
              slot.isBreak = true;
              return slot;
            }

            slot.slots = {};
            users.forEach(({ userId, data }) => {
              if (!Array.isArray(data)) {
                slot.slots[userId] = null;
                return;
              }

              const [bookedStartTime, endTimeKey] = props.keysForBookedAppointmentTimeSlots?.split(',') || []; // eslint-disable-line no-unused-vars

              const appointment = data.find(
                (appt) => appt?.[bookedStartTime] && normalizeTime(appt?.[bookedStartTime]) === time
              );

              if (appointment) {
                const appointmentKeys = props.keysForAppointmentData?.split(',').map((k) => k.trim()) || [];
                const nameParts = appointmentKeys.map((key) => appointment[key]).filter(Boolean);
                const displayName = nameParts.join(' - ') || userId;

                slot.slots[userId] = {
                  [props?.currentAppointmentKey]: appointment[props?.currentAppointmentKey] || false,
                  name: displayName
                };
              } else {
                slot.slots[userId] = null;
              }
            });

            return slot;
          })
          .filter(Boolean);

        setDynamicScheduleData(schedule);
      } catch (error) {
        // eslint-disable-line no-empty
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [props.calStructureDataPage, JSON.stringify(props.calStructureDPParameters), selectedDate]);

  const SummaryPanel = (props) => {
    const { summaryKeys = '', direction = 'column', summaryPosition = 'Top' } = props;
    const keys = summaryKeys
      .split(',')
      .map((k) => k.trim())
      .filter(Boolean);

    const dynamicSummaryData = keys.map((key) => ({
      label: key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase()),
      value: completeData[key] !== undefined ? completeData[key] : 'N/A'
    }));

    const isRow = direction === 'row';

    return (
      <>
        {loading ? (
          <Box display="flex" alignItems="center" justifyContent="center" height="100%" minHeight="300px">
            <CircularProgress size={48} />
          </Box>
        ) : (
          <div
            ref={summaryRef}
            style={{
              display: 'flex',
              flexDirection: direction,
              flexWrap: isRow ? 'wrap' : 'nowrap',
              gap: props.boxGap || '12px',
              alignItems: isRow ? 'stretch' : props.align || 'center',
              width: '100%',
              marginBottom: summaryPosition === 'Top' ? '16px' : undefined,
              marginTop: summaryPosition === 'Bottom' ? '16px' : undefined,
              color: '#050505',
              overflow: isRow ? '' : 'auto',
              flex: '1 1 0'
            }}
          >
            {dynamicSummaryData.map((item, index) => (
              <div
                key={index}
                style={{
                  width: isRow ? 'calc(25% - 12px)' : '100%',
                  minWidth: isRow ? '200px' : '100%',
                  padding: props.boxPadding || '10px',
                  border: `1px solid ${props.borderColor || '#ccc'}`,
                  borderRadius: props.borderRadius || '6px',
                  backgroundColor: props.boxBgColor || '#fff',
                  textAlign: 'left',
                  fontFamily: props.fontFamily || 'Open Sans',
                  boxSizing: 'border-box'
                }}
              >
                <div
                  style={{
                    fontSize: props.labelFontSize || '13px',
                    color: props.labelColor || '#00000099',
                    marginBottom: '4px'
                  }}
                >
                  {item.label}
                </div>
                <div
                  style={{
                    fontWeight: 'bold',
                    fontSize: props.valueFontSize || '17px',
                    color: props.valueColor || '#050505'
                  }}
                >
                  {item.value}
                </div>
              </div>
            ))}
          </div>
        )}
      </>
    );
  };

  const handleSlotClick = (time, dentistKey) => {
    const key = `${time}-${dentistKey}`;
    setSelectedSlot((prev) => (prev?.key === key ? null : { key, time, dentistKey }));

    if (props.mode !== 'Editable') return;
    const pageReference = props.getPConnect ? props.getPConnect().getPageReference() : null;

    if (props.getPConnect && timeSlots.length > 0) {
      const currentIndex = timeSlots.findIndex((t) => t === time);
      const endTime = timeSlots[currentIndex + 1] || time;
      const paramData = [time, endTime, dentistKey];

      fieldParametersForSlot.forEach((param, index) => {
        const propName = `${pageReference}.${param.propref.trim()}`;
        const keyValue = paramData[index].trim();
        props.getPConnect().getActionsApi().updateFieldValue(propName, keyValue);
        props.getPConnect().getActionsApi().triggerFieldChange(propName, keyValue);
      });
    }
  };

  const headingHeight = props.heading ? 36 : 0; // adjust 60px as needed

  const handleDateChange = (newValue) => {
    setSelectedDate(newValue);
  };

  return (
    <div
      ref={containerRef}
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: props.height || '100%',
        backgroundColor: props.backgroundColor || '#fff',
        fontFamily: props.fontFamily || 'Open Sans',
        maxWidth: '1800px',
        minWidth: props.width || '100%',
        padding: props.padding,
        borderRadius: props.borderRadius || '6px',
        overflow: 'hidden'
      }}
    >
      {props.heading && (
        <h2
          style={{
            textAlign: props.textAlign || 'center',
            color: props.headingColor || '#000',
            marginBottom: '4px',
            height: '32px'
          }}
        >
          {props.heading}
        </h2>
      )}
      {props.summaryPosition === 'Top' && <SummaryPanel {...props} direction="row" />}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          height:
            props.summaryPosition === 'Top' || props.summaryPosition === 'Bottom'
              ? `calc(100% - ${summaryHeight + headingHeight}px)`
              : `calc(100% - ${headingHeight}px)`,
          gap: props.tableGap || '0 30px',
          width: '100%'
        }}
      >
        {props.summaryPosition === 'Left' && (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              width: props.summaryWidth || '20%'
            }}
          >
            {props.showDatePicker && (
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateCalendar
                  value={selectedDate}
                  onChange={(newValue) => handleDateChange(newValue)}
                  disablePast
                  maxDate={dayjs().add(200, 'year')}
                  sx={{
                    width: '100%',
                    marginBottom: '16px',
                    padding: '0px 8px',
                    border: `1px solid ${props.borderColor || '#ccc'}`,
                    borderRadius: '6px',

                    '& .MuiPickersDay-root': {
                      borderRadius: '6px',
                      '&:hover': {
                        backgroundColor: `${props.freeSelectedBgColor}30`
                      }
                    },
                    '& .MuiPickersDay-root.Mui-selected': {
                      backgroundColor: props.freeSelectedBgColor,
                      borderRadius: '6px',
                      color: '#fff',
                      '&:hover': {
                        backgroundColor: `${props.freeSelectedBgColor}95`
                      }
                    },

                    '& .MuiYearCalendar-root': {
                      display: 'flex',
                      flexWrap: 'wrap',
                      justifyContent: 'flex-start',
                      gap: '4px',

                      width: '100%',
                      boxSizing: 'border-box',
                      maxHeight: 240,
                      overflowY: 'auto'
                    },

                    '& .MuiPickersYear-yearButton': {
                      flex: '1 0 calc(50% - 4px)',
                      minWidth: 0,
                      padding: '6px',
                      fontSize: '0.65rem',
                      textAlign: 'center'
                    }
                  }}
                />
              </LocalizationProvider>
            )}
            <SummaryPanel {...props} direction="column" />
          </div>
        )}
        <div
          style={{
            width:
              props.summaryPosition === 'Left' || props.summaryPosition === 'Right'
                ? `calc(100% - ${props.summaryWidth || '20%'})`
                : '100%',
            height: '100%',
            overflow: 'auto',
            boxSizing: 'border-box',
            flexGrow: 1
          }}
        >
          <table
            style={{
              width: '100%',
              borderCollapse: 'collapse',
              textAlign: props.textAlign || 'center',
              tableLayout: props.columnWidth === 'auto' ? 'fixed' : 'auto',
              height: '100%'
            }}
          >
            {loading ? (
              <Box display="flex" alignItems="center" justifyContent="center" height="100%" minHeight="300px">
                <CircularProgress size={48} />
              </Box>
            ) : (
              <>
                <thead style={{ fontSize: props.columnHeaderFontSize || '13px' }}>
                  <tr style={{ backgroundColor: '#ececec' }}>
                    <th
                      style={{
                        border: `1px solid ${props.borderColor || '#ccc'}`,
                        borderLeft: `1px solid ${props.borderColor || '#ccc'}`,
                        backgroundColor: '#ececec',
                        position: 'sticky',
                        top: 0,
                        left: 0,
                        zIndex: 10,
                        backgroundClip: 'padding-box',
                        padding: '8px'
                      }}
                    >
                      Time
                    </th>

                    {dentists.map((user) => (
                      <th
                        key={user.userId}
                        style={{
                          border: `1px solid ${props.borderColor || '#ccc'}`,
                          backgroundColor: '#ececec',
                          position: 'sticky',
                          top: 0,
                          zIndex: 5,
                          backgroundClip: 'padding-box',
                          width: props.columnWidth || '1%',
                          padding: '8px'
                        }}
                      >
                        {user.label || user.userId}
                      </th>
                    ))}
                  </tr>
                </thead>

                <tbody style={{ fontSize: props.fontSize || '12px' }}>
                  {dynamicScheduleData.map((slot, index) => {
                    const timeDisabled = props.disableSlotSelection && slot.disabled && props.mode === 'Editable';
                    if (slot.isBreak) {
                      return (
                        <tr key={index} style={{ backgroundColor: props.freeBgColor || '#fff' }}>
                          <td
                            style={{
                              borderRight: `1px solid ${props.borderColor || '#ccc'}`,
                              borderBottom: `1px solid ${props.borderColor || '#ccc'}`,
                              borderLeft: `1px solid ${props.borderColor || '#ccc'}`,
                              backgroundColor: timeDisabled ? '#9d9d9d14' : 'transparent',
                              position: 'sticky',
                              left: 0,
                              zIndex: 3,
                              backgroundClip: 'padding-box',
                              minWidth: props.timeSlotColumnWidth,
                              maxWidth: props.timeSlotColumnWidth,
                              fontSize: props.fontSize || '12px',
                              height: props.rowHeight || '40px',
                              opacity: timeDisabled ? 0.7 : 1
                            }}
                          >
                            {slot.time}
                          </td>

                          {dentists.map((_, i) => (
                            <td
                              key={i}
                              style={{
                                backgroundColor: props.breakTimeBgColor || '#fdd',
                                borderRight: `1px solid ${props.borderColor || '#ccc'}`,
                                borderBottom: `1px solid ${props.borderColor || '#ccc'}`,
                                height: props.rowHeight || '40px'
                              }}
                            />
                          ))}
                        </tr>
                      );
                    }

                    return (
                      <tr key={index} style={{ backgroundColor: props.freeBgColor || '#fff' }}>
                        <td
                          style={{
                            borderRight: `1px solid ${props.borderColor || '#ccc'}`,
                            borderBottom: `1px solid ${props.borderColor || '#ccc'}`,
                            borderLeft: `1px solid ${props.borderColor || '#ccc'}`,
                            backgroundColor: timeDisabled ? '#9d9d9d14' : 'transparent',
                            minWidth: props.timeSlotColumnWidth || '100px',
                            maxWidth: props.timeSlotColumnWidth || '100px',
                            height: props.rowHeight || '40px',
                            fontSize: props.fontSize || '12px',
                            position: 'sticky',
                            left: 0,
                            zIndex: 3,
                            boxSizing: 'border-box',
                            backgroundClip: 'padding-box',
                            opacity: timeDisabled ? 0.7 : 1,
                            cursor: timeDisabled ? '' : ''
                          }}
                        >
                          {slot.time}
                        </td>

                        {dentists &&
                          dentists.length &&
                          dentists.map((user) => {
                            const entry = slot.slots[user.userId];
                            const isFree = !entry;
                            const isSelected = selectedSlot?.key === `${slot.time}-${user.userId}`;
                            let backgroundColor = props.freeBgColor || '#fff';
                            const isDisabled = props.disableSlotSelection && slot.disabled && props.mode === 'Editable';

                            if (isSelected) {
                              backgroundColor = props.freeSelectedBgColor || '#ffcc00';
                            } else if (entry?.[props.currentAppointmentKey] === true) {
                              backgroundColor = props.examinationBgColor || '#fffbcc';
                            } else if (!entry) {
                              backgroundColor = props.freeBgColor || '#fff';
                            } else {
                              backgroundColor = props.bookedSlotBgColor || '#e6f7ff';
                            }

                            if (isDisabled && props.mode === 'Editable' && !entry) {
                              backgroundColor = props.disabledStateColor || '#9d9d9d12';
                            }

                            return (
                              <td // eslint-disable-line jsx-a11y/no-noninteractive-element-interactions, jsx-a11y/click-events-have-key-events
                                key={user.userId}
                                style={{
                                  backgroundColor,
                                  width: props.columnWidth || '1%',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  height: props.rowHeight || '40px',
                                  fontSize: props.rowsFontSize || '13px',
                                  color: '#000000',
                                  border: `1px solid ${props.borderColor || '#ccc'}`,
                                  borderRight: `1px solid ${props.borderColor || '#ccc'}`,
                                  borderBottom: `1px solid ${props.borderColor || '#ccc'}`,
                                  boxSizing: 'border-box',
                                  backgroundClip: 'padding-box',
                                  padding: '8px',
                                  cursor: isFree && !isDisabled && props.mode === 'Editable' ? 'pointer' : '',
                                  opacity: isDisabled ? 0.7 : 1
                                }}
                                onClick={() => {
                                  if (!isDisabled && isFree && props.mode === 'Editable') {
                                    handleSlotClick(slot.time, user.userId, user);
                                  }
                                }}
                              >
                                {entry && (
                                  <Tooltip
                                    title={props.showAppointmentData && `${entry.name}`}
                                    PopperProps={{
                                      disablePortal: true
                                    }}
                                  >
                                    <div
                                      style={{
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                      }}
                                    >
                                      {props.showAppointmentData && `${entry.name}`}
                                    </div>
                                  </Tooltip>
                                )}
                              </td>
                            );
                          })}
                      </tr>
                    );
                  })}
                </tbody>
              </>
            )}
          </table>
        </div>

        {props.summaryPosition === 'Right' && (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              width: props.summaryWidth || '20%'
            }}
          >
            {props.showDatePicker && (
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateCalendar
                  value={selectedDate}
                  onChange={(newValue) => handleDateChange(newValue)}
                  disablePast
                  maxDate={dayjs().add(200, 'year')}
                  sx={{
                    width: '100%',
                    marginBottom: '16px',
                    padding: '0px 8px',
                    border: `1px solid ${props.borderColor || '#ccc'}`,
                    borderRadius: '6px',
                    overflow: 'visible',
                    '& .MuiPickersDay-root': {
                      borderRadius: '6px',
                      '&:hover': {
                        backgroundColor: `${props.freeSelectedBgColor}30`
                      }
                    },
                    '& .MuiPickersDay-root.Mui-selected': {
                      backgroundColor: props.freeSelectedBgColor,
                      borderRadius: '6px',
                      color: '#fff',
                      '&:hover': {
                        backgroundColor: `${props.freeSelectedBgColor}95`
                      }
                    },

                    '& .MuiYearCalendar-root': {
                      display: 'flex',
                      flexWrap: 'wrap',
                      justifyContent: 'flex-start',
                      gap: '4px',

                      width: '100%',
                      boxSizing: 'border-box',
                      maxHeight: 240,
                      overflowY: 'auto'
                    },

                    '& .MuiPickersYear-yearButton': {
                      flex: '1 0 calc(50% - 4px)',
                      minWidth: 0,
                      padding: '6px',
                      fontSize: '0.65rem',
                      textAlign: 'center'
                    }
                  }}
                />
              </LocalizationProvider>
            )}
            <SummaryPanel {...props} direction="column" loading={loading} />
          </div>
        )}
      </div>
      {props.summaryPosition === 'Bottom' && <SummaryPanel {...props} direction="row" />}
    </div>
  );
};

export default AppointmentCalendar;
