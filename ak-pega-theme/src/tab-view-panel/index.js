import React, { useState, useEffect, useRef } from 'react';
import { Box, Tabs, Tab, IconButton, Popover, MenuItem, Tooltip } from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { getAllFields } from '../utils/icon-migration';
import { commonProperties } from '@astrakraft/core-lib';
import CircularProgress from '@mui/material/CircularProgress';
import CheckIcon from '@mui/icons-material/Check';
import InputBase from '@mui/material/InputBase';

const { commonPegaPropDefinitions, commonPegaProps, commonProps, commonRenderProps, getStyles } = commonProperties;

function CustomTabPanel({ children, value, index }) {
  if (value !== index) return null;
  return (
    <div
      role="tabpanel"
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      style={{ padding: '0px', margin: '0px' }}
    >
      {children}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

export function TabViewLayout(props) {
  const [value, setValue] = useState(0);
  const [initialCall, setInitialCall] = useState(false);
  const [popoverAnchor, setPopoverAnchor] = useState(null);
  const tabListRef = useRef(null);
  const [tabsMeta, setTabsMeta] = useState([]);
  const [tabContents, setTabContents] = useState({});
  const [loadingTabIndex, setLoadingTabIndex] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [refreshIndex, setRefreshTabIndex] = useState(0);
  const [visitedTabs, setVisitedTabs] = useState(new Set());

  const filteredTabs = tabsMeta
    .map((tab, originalIndex) => ({ ...tab, originalIndex }))
    .filter((tab) => tab.label.toLowerCase().includes(searchTerm.toLowerCase()));

  useEffect(() => {
    const fetchTabsMetadata = async () => {
      if (!window.PCore || !window.PCore.getDataApiUtils) return;

      const fields = getAllFields(props.getPConnect);
      const meta = fields.map((field) => ({
        label: field.inheritedProps?.find((p) => p.prop === 'label')?.value || field.name,
        name: field.name,
        ruleClass: field.ruleClass
      }));
      setTabsMeta(meta);

      const shouldPostAction =
        props.postAction &&
        (props.tabsToPerformPostActionOptions === 'All' ||
          props?.tabsToPerformPostActionOptions === undefined ||
          (props.tabsToPerformPostActionOptions === 'Specific' &&
            typeof props.tabsToPerformPostAction === 'string' &&
            props.tabsToPerformPostAction &&
            props.tabsToPerformPostAction
              .split(',')
              .map((s) => s.trim())
              .includes(meta[value]?.name)));

      if (meta[value]?.name && shouldPostAction && !initialCall) {
        const viewName = meta[value]?.name || '';
        const caseKey = props.getPConnect().getValue((await window.PCore.getConstants()).CASE_INFO.CASE_INFO_ID);
        const userId = await window.PCore.getEnvironmentInfo().getOperatorIdentifier();
        const context = props.getPConnect().getContextName();

        try {
          await window.PCore.getDataPageUtils().getPageDataAsync(
            props.dataPage,
            context,
            { caseKey, viewName, userId },
            { invalidateCache: true }
          );
          setInitialCall(true);
        } catch (error) {} // eslint-disable-line no-empty
      }
    };

    fetchTabsMetadata();
  }, [props.getPConnect]);

  useEffect(() => {
    const fetchTabContent = async () => {
      if (!tabsMeta[value] || tabContents[value]) return;
      setLoadingTabIndex(value);
      try {
        const { name, ruleClass } = tabsMeta[value];
        const metadata = await window.PCore.getViewResources().fetchViewResources(name, props.getPConnect(), ruleClass);

        const config = {
          meta: metadata,
          options: {
            contextName: props.getPConnect().getContextName(),
            context: props.getPConnect().getContextName(),
            pageReference: props.getPConnect().getPageReference(),
            target: props.getPConnect().getTarget()
          }
        };

        const pConn = window.PCore.createPConnect(config);
        const component = pConn.getPConnect().createComponent(config.meta);
        setTabContents((prev) => ({ ...prev, [value]: component }));
      } catch (error) {
        // eslint-disable-line no-empty
      } finally {
        setLoadingTabIndex(null);
      }
    };

    fetchTabContent();
  }, [value, tabsMeta, refreshIndex]);

  const handleChange = async (_, newValue) => {
    const isReClick = value === newValue;
    const isAlreadyVisited = visitedTabs.has(newValue);
    setValue(newValue);
    setRefreshTabIndex((prev) => prev + 1);

    const updatedVisited = new Set(visitedTabs);
    updatedVisited.add(newValue);
    setVisitedTabs(updatedVisited);

    const viewName = tabsMeta[newValue]?.name || '';
    const caseKey = props.getPConnect().getValue((await window.PCore.getConstants()).CASE_INFO.CASE_INFO_ID);
    const userId = await window.PCore.getEnvironmentInfo().getOperatorIdentifier();
    const context = props.getPConnect().getContextName();

    const shouldRefresh =
      props.refreshOnTabClick &&
      (props.tabsToRefreshOptions === 'All' ||
        props?.tabsToRefreshOptions === undefined ||
        (props.tabsToRefreshOptions === 'Specific' &&
          typeof props.tabsToRefresh === 'string' &&
          props.tabsToRefresh &&
          props.tabsToRefresh
            .split(',')
            .map((s) => s.trim())
            .includes(viewName)));

    if (shouldRefresh && (isReClick || isAlreadyVisited)) {
      props.getPConnect().getActionsApi().refreshCaseView(caseKey, viewName, '', {});
    }

    const shouldPostAction =
      props.postAction &&
      (props.tabsToPerformPostActionOptions === 'All' ||
        props?.tabsToPerformPostActionOptions === undefined ||
        (props.tabsToPerformPostActionOptions === 'Specific' &&
          typeof props.tabsToPerformPostAction === 'string' &&
          props.tabsToPerformPostAction &&
          props.tabsToPerformPostAction
            .split(',')
            .map((s) => s.trim())
            .includes(viewName)));

    if (shouldPostAction) {
      try {
        await window.PCore.getDataPageUtils().getPageDataAsync(
          props.dataPage,
          context,
          { caseKey, viewName, userId },
          { invalidateCache: true }
        );
      } catch (error) {} // eslint-disable-line no-empty
    }
  };

  const handlePopoverOpen = (event) => {
    setPopoverAnchor(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setPopoverAnchor(null);
  };

  const handleTabSelectFromPopover = (index) => {
    setValue(index);
    handleChange(null, index);
    handlePopoverClose();
  };

  const open = Boolean(popoverAnchor);

  return (
    <Box sx={getStyles(props)}>
      <Box sx={{ display: 'flex', alignItems: 'center', borderBottom: '0.0625rem solid #cfcfcf' }}>
        {tabsMeta.length > 4 && (
          <>
            <Tooltip title={props.popoverTooltipTitle || 'All Tabs'}>
              <IconButton
                onClick={handlePopoverOpen}
                size="small"
                sx={{
                  height: '30px',
                  width: '30px',
                  borderRadius: props.popoverBorderRadius || '3px',
                  backgroundColor: props.popoverButtonColor || 'transparent',
                  '&:hover': {
                    backgroundColor: props.popoverHoverColor || '#DDE1E7',
                    color: props.tabHoverTextColor || '#071531'
                  }
                }}
              >
                <ArrowDropDownIcon />
              </IconButton>
            </Tooltip>

            <Popover
              open={open}
              anchorEl={popoverAnchor}
              onClose={handlePopoverClose}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
              sx={{ mt: 1 }}
              PaperProps={{
                sx: {
                  width: '260px',
                  padding: 0
                }
              }}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', maxHeight: '300px' }}>
                <Box
                  sx={{
                    p: '4px 10px',
                    display: 'flex',
                    alignItems: 'center',
                    border: '1px solid #ccc',
                    borderRadius: '35px',
                    m: 1,
                    position: 'sticky',
                    top: 0,
                    backgroundColor: '#fff',
                    zIndex: 1
                  }}
                >
                  <svg role="presentation" viewBox="0 0 25 25" className="sc-csuSiG bpBxlD" width="18" height="18">
                    <path d="m18.513 17.115 4.754 4.755.047.14c.14.232.186.42.186.512 0 .653-.326.979-.979.979-.186 0-.42-.094-.652-.28l-4.708-4.708c-1.77 1.445-3.776 2.144-6.06 2.144-2.656 0-4.894-.932-6.758-2.797-1.91-1.91-2.843-4.148-2.843-6.758 0-2.61.932-4.848 2.843-6.759C6.253 2.433 8.491 1.5 11.102 1.5c2.61 0 4.847.932 6.758 2.843 1.865 1.864 2.797 4.102 2.797 6.759 0 2.237-.7 4.242-2.144 6.013Zm-7.365 1.631c2.098 0 3.869-.746 5.36-2.237 1.492-1.492 2.238-3.263 2.238-5.36 0-2.099-.746-3.916-2.237-5.408-1.492-1.492-3.263-2.237-5.36-2.237-2.099 0-3.916.745-5.408 2.237-1.492 1.492-2.237 3.31-2.237 5.407 0 2.098.745 3.869 2.237 5.36 1.492 1.492 3.31 2.238 5.407 2.238Z"></path>
                  </svg>
                  <InputBase
                    sx={{ ml: 1, flex: 1 }}
                    placeholder="Search..."
                    inputProps={{ 'aria-label': 'search tabs' }}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </Box>
                <Box sx={{ overflowY: 'auto', px: 1, pb: 1 }}>
                  {filteredTabs.map((tab) => {
                    const isSelected = tab.originalIndex === value;
                    return (
                      <MenuItem
                        key={`${tab.originalIndex}-${tab.label}`}
                        selected={isSelected}
                        onClick={() => handleTabSelectFromPopover(tab.originalIndex)}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          gap: 1
                        }}
                      >
                        <Box sx={{ width: '20px', display: 'flex', justifyContent: 'center' }}>
                          {isSelected && <CheckIcon sx={{ fontSize: 18, color: '#132D73' }} />}
                        </Box>
                        {tab.label}
                      </MenuItem>
                    );
                  })}
                </Box>
              </Box>
            </Popover>
          </>
        )}

        <Tabs
          value={value}
          variant="scrollable"
          scrollButtons="auto"
          ref={tabListRef}
          sx={{
            borderWidth: '0px',
            padding: '0px',
            flex: 1,
            minHeight: props.tabMinHeight || '34px',
            height: props.tabHeight || '34px',
            '& .MuiTabs-indicator': { display: 'none' }
          }}
        >
          {tabsMeta.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              {...a11yProps(index)}
              onClick={() => handleChange(null, index)}
              sx={{
                minHeight: props.tabMinHeight || '34px',
                height: props.tabHeight || '34px',
                lineHeight: props.tabLineHeight || props.tabHeight || '34px',
                textTransform: 'none',
                fontWeight: props.tabFontWeight || (value === index ? 'bold' : 'normal'),
                fontSize: props.tabFontSize || '14px',
                padding: props.tabPadding || '0 12px',
                color: value === index ? props.tabSelectedTextColor || '#132D73' : '#1D2A43',
                backgroundColor: props.tabButtonBgColor || 'transparent',
                borderTopRightRadius: props.tabBorderRadius || '3px',
                borderTopLeftRadius: props.tabBorderRadius || '3px',
                borderBottom: `3px solid ${value === index ? props.tabBorderColor || '#132D73' : 'transparent'}`,
                '&:hover': {
                  backgroundColor: props.tabButtonHoverColor || '#DDE1E7',
                  color: props.tabHoverTextColor || '#071531'
                },

                '&:active': {
                  backgroundColor: 'transparent',
                  boxShadow: 'none'
                },

                '&.Mui-selected': {
                  backgroundColor: props.tabSelectedBgColor || '#FFFFFF',
                  color: props.tabSelectedTextColor || '#000066',
                  borderBottom: `3px solid ${props.tabBorderColor || '#132D73'}`
                },

                '&:focus': {
                  borderRadius: '0.25rem',
                  boxShadow: `rgb(255, 255, 255) 0px 0px 0px 0.11rem inset,
                  rgb(7, 107, 201) 0px 0px 0px 0.18rem inset,
                  rgba(7, 107, 201, 0.1) 0px 0px 0px 0.3rem inset`,
                  outline: 'none',
                  border: 'none'
                }
              }}
            />
          ))}
        </Tabs>
      </Box>

      {tabsMeta.map((tab, index) => (
        <CustomTabPanel key={index} value={value} index={index}>
          {loadingTabIndex === index ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '200px'
              }}
            >
              <CircularProgress size={32} />
            </Box>
          ) : (
            tabContents[index] || null
          )}
        </CustomTabPanel>
      ))}
    </Box>
  );
}

export default {
  label: 'Tab View Layout',
  displayName: 'Tab View Layout',
  icon: (
    <svg
      version="1.0"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 64 64"
      enableBackground="new 0 0 64 64"
    >
      <rect x="1" y="12" fill="none" stroke="#000000" strokeWidth="2" strokeMiterlimit="10" width="62" height="40" />
    </svg>
  ),
  type: 'PEGA',
  events: [],
  properties: commonProps.concat(commonPegaPropDefinitions, [
    { name: 'tabButtonBgColor' },
    { name: 'tabButtonHoverColor' },
    { name: 'tabSelectedBgColor' },
    { name: 'tabSelectedTextColor' },
    { name: 'tabHoverTextColor' },
    { name: 'tabBorderColor' },
    { name: 'tabBorderRadius' },
    { name: 'tabFontSize' },
    { name: 'tabFontWeight' },
    { name: 'tabPadding' },
    { name: 'tabMinHeight' },
    { name: 'tabHeight' },
    { name: 'tabLineHeight' },

    { name: 'popoverButtonColor' },
    { name: 'popoverHoverColor' },
    { name: 'popoverBorderRadius' },
    { name: 'popoverTooltipTitle' }
  ]),
  variants: [
    {
      showRenderIconAsComponent: true,
      showDesignerProps: true,
      renderIcon: '/img/twocolprev2.png',
      id: 'tab_view_layout',
      renderProps: {
        ...commonRenderProps,
        ...commonPegaProps,
        height: '400px',
        width: '50%',
        layoutHeight: '100%',
        columnGap: '0px',
        childPadding: '0px',
        leftColumnBorder: '0px solid black',
        rightColumnBorder: '0px solid black',
        leftColumnBorderRadius: '0px',
        rightColumnBorderRadius: '0px',
        leftColumnPadding: '0px',
        rightColumnPadding: '0px',
        pegaConfig: [
          {
            name: 'tabs',
            label: 'Tabs',
            format: 'CONTENTPICKER',
            addTypeList: ['Views'],
            allowCreatingGroup: true
          },
          {
            label: 'Advanced',
            format: 'GROUP',
            collapsible: true,
            properties: [
              {
                name: 'refreshOnTabClick',
                label: 'Refresh view on tab click',
                format: 'BOOLEAN'
              },
              {
                name: 'tabsToRefreshOptions',
                label: 'Select options to refresh',
                format: 'SELECT',
                defaultValue: 'All',
                source: [
                  { key: 'All', value: 'All' },
                  { key: 'Specific', value: 'Specific' }
                ],
                visibility: '($this.refreshOnTabClick = true)'
              },
              {
                name: 'tabsToRefresh',
                label: 'Tabs to refresh',
                format: 'TEXT',
                required: true,
                visibility: '($this.tabsToRefreshOptions = Specific)'
              },
              {
                name: 'postAction',
                label: 'Perform post action',
                format: 'BOOLEAN',
                defaultValue: false
              },
              {
                name: 'tabsToPerformPostActionOptions',
                label: 'Select options to perform post action',
                format: 'SELECT',
                defaultValue: 'All',
                source: [
                  { key: 'All', value: 'All' },
                  { key: 'Specific', value: 'Specific' }
                ],
                visibility: '($this.postAction = true)'
              },
              {
                name: 'tabsToPerformPostAction',
                label: 'Tabs to perform post action',
                format: 'TEXT',
                required: true,
                visibility: '($this.tabsToPerformPostActionOptions = Specific)'
              },
              {
                name: 'dataPage',
                label: 'Data page name',
                format: 'TEXT',
                required: true,
                visibility: '($this.postAction = true)'
              }
            ]
          },
          {
            name: 'visibility',
            label: 'Visibility',
            format: 'VISIBILITY'
          }
        ]
      },
      renderComponent: function (props) {
        return <TabViewLayout {...props} />;
      }
    }
  ]
};
