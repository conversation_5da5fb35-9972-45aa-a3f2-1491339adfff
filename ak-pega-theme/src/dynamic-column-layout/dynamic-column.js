import React from 'react';

const DynamicColumnLayout = ({ columnLength = 4, ...props }) => {
  const generateColumnStyles = (index) => {
    const commonStyles = {
      height: props.layoutHeight,
      padding: props[`column${index + 1}Padding`],
      backgroundColor: props[`column${index + 1}BackgroundColor`] || '#ffffff',
      textAlign: props[`column${index + 1}TextAlign`] || 'left',
      border: props[`column${index + 1}Border`] || 'none',
      borderRadius: props[`column${index + 1}BorderRadius`] || '0px'
    };

    if (index < 5) {
      return {
        ...commonStyles,
        width: props[`column${index + 1}Width`] || `${100 / columnLength}%`
      };
    }

    return {
      ...commonStyles,
      width: `${100 / columnLength}%`
    };
  };

  const renderColumns = () => {
    const columns = [];
    for (let i = 0; i < columnLength; i++) {
      columns.push(
        <div key={i} style={generateColumnStyles(i)}>
          {props.children && props.children[i]}
        </div>
      );
    }
    return columns;
  };

  return (
    <div
      style={{
        ...props,
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        gap: props.columnGap || '10px'
      }}
    >
      {renderColumns()}
    </div>
  );
};

export default DynamicColumnLayout;
