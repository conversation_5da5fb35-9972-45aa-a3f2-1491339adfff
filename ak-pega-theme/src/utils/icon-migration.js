import React from 'react';
import Icon from '@mui/material/Icon';

// Mapping of MUI icon component names to their font icon names
const iconMap = {
  MoreVert: 'more_vert',
  Schedule: 'schedule',
  InsertDriveFileOutlined: 'insert_drive_file',
  ArrowDropDown: 'arrow_drop_down',
  DeleteOutline: 'delete_outline',
  ExpandLess: 'expand_less',
  ExpandMore: 'expand_more',
  Search: 'search',
  CheckCircleOutline: 'check_circle_outline',
  ArrowForward: 'arrow_forward',
  Edit: 'edit',
  Delete: 'delete',
  AddCircleOutline: 'add_circle_outline',
  ChevronRight: 'chevron_right',
  Send: 'send',
  SettingsOutlined: 'settings',
  Storage: 'storage',
  LibraryAddOutlined: 'library_add',
  AccountTreeOutlined: 'account_tree',
  LibraryBooksOutlined: 'library_books',
  OpenWithOutlined: 'open_with',
  FlipToBackOutlined: 'flip_to_back',
  Extension: 'extension',
  ControlPoint: 'control_point',
  MoreHorizRounded: 'more_horiz',
  North: 'north',
  South: 'south',
  KeyboardDoubleArrowUp: 'keyboard_double_arrow_up',
  KeyboardDoubleArrowDown: 'keyboard_double_arrow_down',
  FormatAlignLeft: 'format_align_left',
  FormatAlignCenter: 'format_align_center',
  FormatAlignRight: 'format_align_right',
  SettingsBackupRestore: 'settings_backup_restore',
  DragIndicator: 'drag_indicator',
  KeyboardArrowDown: 'keyboard_arrow_down',
  CheckCircle: 'check_circle',
  Close: 'close',
  WarningAmber: 'warning_amber',
  SelectAll: 'select_all',
  Reuse: 'recycling',
  Navigation: 'navigation',
  Visibility: 'visibility',
  VisibilityOff: 'visibility_off',
  LocationOn: 'location_on',
  DeleteSweep: 'delete_sweep',
  Chat: 'chat',
  Clear: 'clear',
  AccessTime: 'access_time',
  Favorite: 'favorite',
  FavoriteBorder: 'favorite_border'
};

// Helper function to get the icon name
export const getIconName = (muiIconName) => {
  return iconMap[muiIconName] || muiIconName.toLowerCase();
};

// Reusable Icon component
export const IconComponent = ({ iconName, ...props }) => {
  return <Icon {...props}>{getIconName(iconName)}</Icon>;
};

export const getAllFields = (pConnect) => {
  const metadata = pConnect().getRawMetadata();
  if (!metadata.children) {
    return [];
  }

  let allFields = [];

  const makeField = (f) => {
    const resolvedConfig = pConnect().resolveConfigProps(f.config);
    if (resolvedConfig.visibility === false) return null;
    if (typeof f.config.value === 'string') {
      return {
        ...resolvedConfig,
        type: f.type,
        propref: `@P ${f.config.value.substring(f.config.value.lastIndexOf('.'))}`,
        pageref: f.config.value.substring(f.config.value.indexOf(' .') + 2, f.config.value.indexOf('[].'))
      };
    }

    return {
      ...resolvedConfig,
      type: f.type
    };
  };

  const hasRegions = !!metadata.children[0]?.children;

  if (hasRegions) {
    metadata.children.forEach((region) =>
      region.children.forEach((field) => {
        const mainField = makeField(field);
        if (mainField) allFields.push(mainField);

        if (field.type === 'Group' && field.children) {
          field.children.forEach((gf) => {
            const groupField = makeField(gf);
            if (groupField) allFields.push(groupField);
          });
        }
      })
    );
  } else {
    allFields = metadata.children.map(makeField).filter(Boolean);
  }

  return allFields;
};

export default IconComponent;
