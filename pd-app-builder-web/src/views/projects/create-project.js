import React, { Component } from 'react';
import { withRouter } from 'next/router';
import { reduxUtil, genericAction } from '@limegroup/lime-redux-util';
import EditableForm from '../../components/EditableForm';
import newProjectSchema from './schemas/create-project.json';
import UserDetails from '../../components/user-details';
import { find } from 'lodash';

import DashboardView from '../common/dashboard-view';
import { projectConstants, utils } from '@astrakraft/core-lib';
const { basicScreensInfo, dialogScreen } = projectConstants;
const { stringUtil } = utils;
const { randstr } = stringUtil;

// Extended basicScreensInfo with mobileLandscape support
const extendedBasicScreensInfo = {
  ...basicScreensInfo,
  mobileLandscape: {
    data: {
      renderProps: {
        color: 'black',
        padding: 'none',
        margin: 'none',
        showInProgress: false,
        backgroundColor: '#fff',
        horizontalOverflow: 'auto',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        verticalOverflow: 'auto'
      }
    },
    items: [],
    resolutions: { height: '400px', width: '800px' }
  }
};

const { connectToStores } = reduxUtil;
const { sendActionAsync } = genericAction;

const themeScreensInfo = {
  id: randstr('screen_'),
  desktop: {
    data: {
      renderProps: {
        color: 'black',
        padding: 'none',
        margin: 'none',
        showInProgress: false,
        backgroundColor: '#fff',
        horizontalOverflow: 'auto',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        verticalOverflow: 'auto'
      }
    },
    items: [],
    resolutions: { height: '800px', width: '1200px' }
  }
};

class CreateProject extends Component {
  state = {
    sessionLoadPending: false,
    schema: null
  };
  componentDidMount() {
    if (this.props.userReducer.loggedInUser) {
      const { dispatch } = this.props;
      dispatch(sendActionAsync('LIST_PROJECT'));
      dispatch(
        sendActionAsync('GET_PROFILE', {
          profileId: this.props.userReducer.loggedInUser.profileId
        })
      );
    } else {
      this.setState({
        sessionLoadPending: true
      });
    }

    this.setState({
      schema: newProjectSchema
    });
  }

  componentDidUpdate(prevProps) {
    const { dispatch } = this.props;

    if (
      this.state.sessionLoadPending &&
      this.props.userReducer.loggedInUser &&
      this.props.userReducer.loggedInUser !== prevProps.userReducer.loggedInUser
    ) {
      dispatch(sendActionAsync('LIST_PROJECT'));
      dispatch(
        sendActionAsync('GET_PROFILE', {
          profileId: this.props.userReducer.loggedInUser.profileId
        })
      );
      this.setState({ sessionLoadPending: false });
    }

    if (
      this.props.userReducer.profileDetails &&
      this.props.userReducer.profileDetails.mobilePhoneNumber === undefined
    ) {
      this.props.router.push('/update-profile/');
      return;
    }

    if (
      this.props.projectReducer.projectCreated &&
      this.props.projectReducer.projectCreated !==
      prevProps.projectReducer.projectCreated
    ) {
      this.props.router.push(
        `/projects/${this.props.projectReducer.projectCreated._id}/Page1`
      );
    }
  }

  render() {
    const { inputErrors, schema } = this.state || {};
    const { loggedInUser, profileDetails } = this.props.userReducer || {};

    return (
      <DashboardView>
        <div style={{ padding: '15px', margin: '10px', marginTop: '10px' }}>
          <h1 style={{ color: '#165da2', fontSize: '24px' }}>
            Welcome to WorkProject
          </h1>
          <div className="d-flex m-2 mt-4">
            <div className="d-flex flex-column" style={{ width: '280px' }}>
              <h5 style={{ color: '#202020', fontSize: '18px' }}>
                Your Information
              </h5>
              <UserDetails
                loggedInUser={loggedInUser}
                profileDetails={profileDetails}
              />
            </div>
            <div
              style={{
                borderWidth: '1px',
                borderStyle: 'solid',
                margin: '15px',
                marginRight: '35px',
                color: '#c5c5c5'
              }}
            >
              {' '}
            </div>
            <div
              style={{
                width: '350px',
                borderColor: '#c5c5c5',
                padding: '20px',
                paddingLeft: '35px',
                paddingRight: '35px',
                borderStyle: 'solid',
                borderWidth: '2px',
                margin: '3px'
              }}
              className="d-flex flex-column"
            >
              <h5 style={{ color: '#0078d4', textAlign: 'center' }}>
                Start New Project
              </h5>
              <EditableForm
                data={schema}
                onSubmit={this.onSubmit}
                ref="new-project"
                dispatch={this.props.dispatch}
                onUpdate={this.onUpdate}
              />

              <p style={{ color: 'red', fontSize: '14px', margin: '10px' }}>
                {inputErrors}
              </p>
              <button
                style={{
                  backgroundColor: '#0078d4',
                  color: 'white',
                  padding: '8px',
                  borderRadius: '5px',
                  border: 'none'
                }}
                onClick={this.submitForm}
              >
                Create Project
              </button>
            </div>
          </div>
        </div>
      </DashboardView>
    );
  }

  submitForm = () => {
    this.refs['new-project'].submitForm();
  };

  onUpdate = (qa) => {
    this.setState({ inputErrors: '' });
  };

  onSubmit = (questionAnswers) => {
    const { dispatch, projectReducer } = this.props;
    const projects = projectReducer.projects || [];
    const existing = find(projects, { name: questionAnswers.name });
    if (!existing) {
      const projectPayload = { ...questionAnswers, assets: { fonts: ['Roboto'] }, currentLanguage: 'en-US', selectedScreenType: 'desktop' };
      const pagePayload = [{
        id: randstr('page_'),
        screen: extendedBasicScreensInfo,
        name: 'Page1',
        path: 'page1'
      }, {
        id: randstr('page_'),
        screen: themeScreensInfo,
        name: '_Theme',
        path: '_theme'
      }];
      const dialogPayload = {
        id: randstr('dialog_'),
        screen: dialogScreen,
        name: 'Dialog1',
        path: 'dialog1'
      };
      projectPayload.pagePayload = pagePayload;
      projectPayload.dialogPayload = dialogPayload;
      dispatch(sendActionAsync('CREATE_PROJECT', projectPayload));
    } else {
      this.setState({
        inputErrors: `Project name ${questionAnswers.name} already exists`
      });
    }
  };
}

export default withRouter(connectToStores(CreateProject));
