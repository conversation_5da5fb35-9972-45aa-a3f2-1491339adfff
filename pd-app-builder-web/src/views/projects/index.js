import React, { Component } from 'react';
import { withRouter } from 'next/router';
import { reduxUtil, genericAction } from '@limegroup/lime-redux-util';
import EditableForm from '../../components/EditableForm';
import newProjectSchema from './schemas/create-project.json';
import { GET_PROFILE } from '@astrakraft/user-plugin';
import { DELETE_PROJECT } from '@astrakraft/project-plugin';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import DashboardView from '../common/dashboard-view';
import { find, isEmpty, mapValues } from 'lodash';
import Select from 'react-select';
import { Box, CircularProgress, Menu } from '@mui/material';
import IconComponent from '../../utils/icon-util';
import Slide from '@mui/material/Slide';
import { utils, projectConstants } from '@astrakraft/core-lib';
const { stringUtil } = utils;
const { randstr } = stringUtil;

import stc from 'string-to-color';
import CustomMuiDialog from '../../components/custom-mui-dialog';
import ListContentLoader from '../../components/list-content-loader';


const styledButtonClass = 'btn btn-sm btn-ghost justify-start text-[10px] font-[400] hover:bg-transparent hover:text-[#0074F1] text-[#000000] tracking-widest';
const { basicScreensInfo, dialogScreen } = projectConstants;

// Extended basicScreensInfo with mobileLandscape support
const extendedBasicScreensInfo = {
  ...basicScreensInfo,
  mobileLandscape: {
    data: {
      renderProps: {
        color: 'black',
        padding: 'none',
        margin: 'none',
        showInProgress: false,
        backgroundColor: '#fff',
        horizontalOverflow: 'auto',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        verticalOverflow: 'auto'
      }
    },
    items: [],
    resolutions: { height: '400px', width: '800px' }
  }
};

const { connectToStores } = reduxUtil;
const { sendActionAsync, sendAction } = genericAction;

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

class ProjectList extends Component {
  state = {
    moreOptions: null,
    validProject: true,
    deleteModal: false,
    exportModal: false,
    importModal: false,
    url: '',
    selectedProject: null,
    sort: 'None',
    newProjectModal: false,
    loading: true,
    promoteModal: false,
    promoteModalConfirm: false,
    selectedOrg: null,
    selectedProdProject: null,
    availableOrgs: [],
    promotingProject: null,
    importErrorMessage: null
  };

  componentDidMount() {
    if (this.props.userReducer.loggedInUser) {
      const { dispatch } = this.props;
      dispatch(
        sendActionAsync(GET_PROFILE, {
          profileId: this.props.userReducer.loggedInUser.profileId
        })
      );
      dispatch(sendAction('NAV_COLLAPSIBLE', false));

      this.updateProjectTimestamp = (project) => {
        try {
          this.updateRecentProjectsInLocalStorage(project._id);
        } catch (error) {
          console.error('Error updating project timestamp locally:', error); //eslint-disable-line no-console
        }
      };

      // Initialize form schema with filtered project types
      if (this.props.userReducer.loggedInUser.projectTypes) {
        const schema = { ...newProjectSchema };
        const availableTypes = this.props.userReducer.loggedInUser.projectTypes;
        const questionSet = schema.questionSets[0].questions.find(q => q.questionId === 'projectType');
        if (questionSet) {
          questionSet.input.options = questionSet.input.options.filter(opt =>
            availableTypes.includes(opt.value)
          );
        }
        this.setState({ addProjectForm: schema });
      }
    } else {
      this.setState({
        sessionLoadPending: true,
        addProjectForm: newProjectSchema
      });
    }
    if (isEmpty(this.props.projectReducer.projects)) {
      this.setState({ loading: true });
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.userReducer.profileDetails && this.props.userReducer.profileDetails.mobilePhoneNumber === undefined) {
      this.props.router.push('/update-profile/');
      return;
    }
    if (this.props.projectReducer.projects && this.props.projectReducer.projects?.length !== prevProps.projectReducer.projects?.length) {
      this.setState({ loading: false });
    }

    // Update form schema when user logs in or when their project types change
    if (this.props.userReducer?.loggedInUser?.projectTypes !== prevProps.userReducer?.loggedInUser?.projectTypes) {
      const schema = { ...newProjectSchema };
      const availableTypes = this.props.userReducer?.loggedInUser?.projectTypes || [];
      const questionSet = schema.questionSets[0].questions.find(q => q.questionId === 'projectType');
      if (questionSet) {
        questionSet.input.options = questionSet.input.options.filter(opt =>
          availableTypes.includes(opt.value)
        );
      }
      this.setState({ addProjectForm: schema });
    }
    if (
      this.props.projectReducer.projectCreated &&
      this.props.projectReducer.projectCreated !==
      prevProps.projectReducer.projectCreated
    ) {
      this.props.router.push(
        `/projects/${this.props.projectReducer.projectCreated._id}/Page1?branch=master`
      );
    }
    if (this.props.projectReducer?.exportProject?.promoted !== prevProps.projectReducer?.exportProject?.promoted) {
      this.setState({ promotLoading: false });
    }
  }

  render() {
    const { validProject, sort, newProjectModal, addProjectForm, inputErrors } = this.state;
    const sortOptions = [{ value: 'none', label: 'None' }, { value: 'lastModified', label: 'Last Modified' }];
    const currentUrl = this.props.router.pathname;
    const isRecent = currentUrl.includes('recent');
    const isDrafts = currentUrl.includes('drafts');
    const customSelectStyles = {
      dropdownIndicator: (base, state) => ({
        ...base,
        transform: state.selectProps.menuIsOpen && 'rotate(180deg)',
        transition: 'all .2s ease',
        color: '#000000',
        padding: '0px'
      }),
      control: (base) => ({
        ...base,
        width: 'auto',
        border: 'none',
        boxShadow: 'none',
        cursor: 'pointer',
        padding: '0px',
        fontSize: '14px',
        color: '#000000',
        fontWeight: '500',
        fontFamily: 'Inter',
        backgroundColor: ''
      }),
      input: (base) => ({
        ...base,
        border: 'none',
        boxShadow: 'none',
        cursor: 'pointer',
        padding: '0px'
      }),

      option: (base) => ({
        ...base,
        borderRadius: '3px'
      }),
      indicatorSeparator: (base) => ({
        ...base,
        display: 'none',
        margin: '0px',
        padding: '0px'
      }),

      menu: (base) => ({
        ...base,
        width: '110px',
        color: '#000000',
        fontWeight: '500',
        fontFamily: 'Inter',
        padding: '5px',
        borderRadius: '3px',
        fontSize: '11px'
      })
    };
    const productionProjects = this.props.projectReducer?.orgProjects || [];
    return (
      <DashboardView initialState={this.props.initialState}>
        <div
          style={{
            padding: '30px 40px',
            paddingRight: '74px',
            width: '100%',
            height: '91vh',
            overflow: 'hidden'
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              height: '8%'
            }}
          >
            <span
              style={{
                fontSize: '20px',
                color: '#000000',
                fontWeight: '500',
                fontFamily: 'Inter'
              }}
            >
              <b>{isRecent ? 'Recent Projects' : isDrafts ? 'Draft Projects' : 'Projects'}</b>
            </span>
            <div>
              <button className="btn btn-sm custom-button border-none"
                onClick={this.handleClick}
                style={{
                  height: '50px',
                  width: '193px',
                  backgroundColor: '#0074F1',
                  color: '#FFFFFF',
                  fontSize: '14px',
                  fontWeight: '400',
                  borderRadius: '4px',
                  fontFamily: 'Inter',
                  '--fallback-background': '#0074F1',
                  letterSpacing: '0.1em'
                }}
              >
                NEW PROJECT
              </button>
              <Dialog
                open={newProjectModal}
                onClose={this.handleModalClose}
                TransitionComponent={Transition}
                keepMounted
                aria-labelledby="transition-modal-title"
                aria-describedby="transition-modal-description"
                fullWidth
                maxWidth={false}
                sx={{
                  '& .MuiDialog-paper': {
                    padding: '3% 5%',
                    width: '46%'
                  }
                }}
              >
                <h1 className='text-[22px] text-black font-semibold mb-4'>Add New Project</h1>
                <EditableForm
                  data={addProjectForm}
                  onSubmit={this.onNewProjectSubmit}
                  ref="new-project"
                  key="new-project"
                  dispatch={this.props.dispatch}
                  onUpdate={this.onAddProjectUpdate}
                />
                <p style={{ color: 'red', fontSize: '14px' }}>
                  {inputErrors}
                </p>
                <div className='flex justify-end w-full mt-[20px]'>
                  <button
                    style={{
                      '--fallback-background': '#FFFFFF',
                      fontSize: '14px',
                      fontWeight: '400',
                      letterSpacing: '0.1em'
                    }}
                    className="btn btn-sm custom-button font-semibold h-[50px] w-[132px] text-[#495057] bg-white border-solid border-[1px] border-[#495057] rounded-[4px] mr-[10px]"
                    onClick={this.handleModalClose}
                  >
                    Cancel
                  </button>
                  <button
                    style={{
                      '--fallback-background': '#0074F1',
                      fontSize: '14px',
                      fontWeight: '400',
                      letterSpacing: '0.1em'
                    }}
                    className="btn btn-sm custom-button h-[50px] w-[132px] text-white bg-[#0074F1] rounded-[4px] border-none"
                    onClick={this.submitNewProjectForm}
                  >
                    Create
                  </button>
                </div>
              </Dialog>
              <button
                onClick={this.toggleImportModal}
                className='btn btn-sm custom-button border-none ml-2'
                style={{
                  height: '50px',
                  width: '193px',
                  backgroundColor: '#495057',
                  color: '#FFFFFF',
                  fontSize: '14px',
                  fontWeight: '400',
                  fontFamily: 'Inter',
                  borderRadius: '4px',
                  '--fallback-background': '#495057',
                  letterSpacing: '0.1em'
                }}
              >
                IMPORT PROJECT
              </button>
            </div>
          </div>
          {currentUrl.includes('recent') || currentUrl.includes('drafts') ? (
            <div
              style={{
                padding: '17px'
              }}
            ></div>
          ) : (
            <div className='w-full flex items-center'>
              <span
                style={{
                  color: '#A3A3A3',
                  fontSize: '11px',
                  fontWeight: '400',
                  fontFamily: 'Inter'
                }}
              >
                Sort:
              </span>
              <Select
                options={sortOptions}
                isSearchable={false}
                placeholder={sort}
                isClearable={false}
                onChange={this.onProjectSort.bind(this)}
                styles={customSelectStyles}
                defaultValue={sortOptions.find(option => option.label === sort)}
              />
            </div>
          )}
          <div className='w-full h-[86%] pr-[10px] mt-2' style={{ overflowY: 'auto', overflowX: 'hidden' }} >
            {this.renderProjects()}
            <Dialog
              open={this.state.importModal}
              onClose={this.toggleImportModal}
              TransitionComponent={Transition}
              keepMounted
              aria-labelledby="transition-modal-title"
              aria-describedby="transition-modal-description"
              fullWidth
              maxWidth={false}
              sx={{
                '& .MuiDialog-paper': {
                  padding: '3% 5%',
                  width: '46%'
                }
              }}
            >
              <h1 className='text-[22px] text-black font-semibold mb-4'>Import Project</h1>
              <label>Select File</label>
              <input
                type="file"
                className="pointer w-[99%] h-[48px] rounded-[4px] text-sm file:mr-4 file:rounded-[4px] file:cursor-pointer file:custom-button file:btn file-input-custom file:btn-sm file:border-0 file:h-full file:w-[25%] file:text-white file:text-[12px] file:bg-[#0074F1] border border-gray-300"
                id="multiple_files"
                onChange={this.handleFileChange}
                accept=".json"
                ref={(ref) => {
                  this.fileInput = ref;
                  return ref;
                }}
              />
              <EditableForm
                data={newProjectSchema}
                onSubmit={this.onSubmit}
                ref="import-project"
                dispatch={this.props.dispatch}
                onUpdate={this.onUpdate}
              />
              {!validProject && this.state.importErrorMessage && <p className='text-[red]'>*{this.state.importErrorMessage}</p>}
              <div className='flex justify-end w-full mt-[20px]'>
                <button
                  style={{
                    '--fallback-background': '#FFFFFF',
                    fontSize: '14px',
                    fontWeight: '400',
                    letterSpacing: '0.1em'
                  }}
                  className="btn btn-sm custom-button font-semibold h-[50px] w-[132px] text-[#495057] bg-white border-solid border-[1px] border-[#495057] rounded-[4px] mr-[10px]"
                  onClick={() => {
                    this.toggleImportModal();
                    window.location.reload();
                  }}
                >
                  Cancel
                </button>
                <button
                  style={{
                    '--fallback-background': '#0074F1',
                    fontSize: '14px',
                    fontWeight: '400',
                    letterSpacing: '0.1em'
                  }}
                  className="btn btn-sm custom-button h-[50px] w-[132px] text-white bg-[#0074F1] rounded-[4px] border-none"
                  onClick={this.submitForm}
                >
                  Import
                </button>
              </div>
            </Dialog>

            <Dialog
              open={this.state.promoteModal}
              onClose={this.handlePromoteModalClose}
              fullWidth
              maxWidth="sm"
              PaperProps={{
                style: {
                  padding: '24px',
                  borderRadius: '8px'
                }
              }}
            >
              <DialogTitle className="text-2xl font-semibold text-gray-900 pb-2">
                Promote Project
              </DialogTitle>

              <DialogContent className="py-4">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target Organisation
                    </label>
                    <Select
                      options={this.state.availableOrgs.map(org => ({
                        value: org._id,
                        label: org.label
                      }))}
                      onChange={this.handleOrgSelect}
                      value={this.state.selectedOrg}
                      placeholder="Select Organisation"
                      className="w-full"
                      isSearchable
                      classNamePrefix="select"
                      styles={{
                        menu: (provided) => ({
                          ...provided,
                          zIndex: 9999 // Ensure dropdown appears above dialog
                        }),
                        menuPortal: (provided) => ({
                          ...provided,
                          zIndex: 9999
                        }),
                        menuList: (base) => ({
                          ...base,
                          maxHeight: '200px'
                        })

                      }}
                      menuPosition="fixed"
                    />
                  </div>
                  {this.state.selectedOrg && (
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Production Project
                      </label>
                      <Select
                        options={productionProjects?.map(proj => ({
                          value: proj._id,
                          label: proj.name
                        }))}
                        onChange={this.handleProjectSelect}
                        value={this.state.selectedProdProject}
                        placeholder="Select Production Project"
                        className="w-full"
                        isSearchable
                        classNamePrefix="select"
                        noOptionsMessage={() => "No production projects available"}
                        styles={{
                          menu: (provided) => ({
                            ...provided,
                            zIndex: 9999 // Ensure dropdown appears above dialog
                          }),
                          menuPortal: (provided) => ({
                            ...provided,
                            zIndex: 9999
                          }),
                          menuList: (base) => ({
                            ...base,
                            maxHeight: '200px'
                          })
                        }}
                        menuPosition="fixed" // Use fixed positioning
                      />
                    </div>
                  )}

                  <p className="text-sm text-gray-500 mt-2">
                    Select a target organisation and production project to promote your project.
                  </p>
                </div>
              </DialogContent>

              <DialogActions className="px-6 py-4 border-t border-gray-100">
                <button
                  onClick={this.handlePromoteCancel}
                  className="btn btn-sm custom-button font-semibold h-[50px] w-[132px] text-[#495057] bg-white border-solid border-[1px] border-[#495057] rounded-[4px] mr-[10px]"
                >
                  Cancel
                </button>
                <button
                  style={{
                    '--fallback-background': '#0074F1',
                    fontSize: '14px',
                    fontWeight: '400',
                    letterSpacing: '0.1em'
                  }}
                  onClick={this.handlePromoteModalConfirm}
                  disabled={!this.state.selectedProdProject}
                  className="btn btn-sm custom-button h-[50px] w-[132px] text-white bg-[#0074F1] rounded-[4px] border-none"
                >
                  Promote
                </button>
              </DialogActions>
            </Dialog>
            <Dialog
              open={this.state.promoteModalConfirm}
              onClose={this.handlePromoteModalClose}
              fullWidth
              maxWidth="sm"
              PaperProps={{
                style: {
                  padding: '24px',
                  borderRadius: '8px'
                }
              }}
            >
              <DialogTitle className="text-2xl font-semibold text-gray-900 pb-2">
                {this.state.promotLoading ? <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <CircularProgress />
                </Box>
                  : this.state.promoteModal ? "Please confirm before proceeding. This action will push changes to the selected project and cannot be undone. Proceed with caution." : <Box> <IconComponent iconName="CheckCircleOutline" sx={{ fontSize: 'large', color: 'green' }} />Project Promoted Successfully </Box>}
              </DialogTitle>

              <DialogContent className="py-4">
                <div className="space-y-6">

                </div>
              </DialogContent>

              <DialogActions className="px-6 py-4 border-t border-gray-100">
                {this.state.promoteModal ? <> <button
                  onClick={this.handlePromoteCancel}
                  className="btn btn-sm custom-button font-semibold h-[50px] w-[132px] text-[#495057] bg-white border-solid border-[1px] border-[#495057] rounded-[4px] mr-[10px]"
                >
                  Cancel
                </button>
                  <button
                    style={{
                      '--fallback-background': '#0074F1',
                      fontSize: '14px',
                      fontWeight: '400',
                      letterSpacing: '0.1em'
                    }}
                    onClick={this.confirmPromote}
                    disabled={!this.state.selectedProdProject}
                    className="btn btn-sm custom-button h-[50px] w-[132px] text-white bg-[#0074F1] rounded-[4px] border-none"
                  >
                    Promote
                  </button></> : <button
                    style={{
                      '--fallback-background': '#0074F1',
                      fontSize: '14px',
                      fontWeight: '400',
                      letterSpacing: '0.1em'
                    }}
                    onClick={this.handlePromoteSucces}
                    disabled={this.state.promotLoading}
                    className="btn btn-sm custom-button h-[50px] w-[132px] text-white bg-[#0074F1] rounded-[4px] border-none"
                  >
                  Close
                </button>}
              </DialogActions>
            </Dialog>
          </div>
        </div>

      </DashboardView>
    );
  }
  togglePromoteModal = (project) => {
    this.setState({ promoteModal: true, promotingProject: project });
    this.fetchOrganisations();
  };

  handlePromoteModalConfirm = () => {
    this.setState({ promoteModalConfirm: true });
  };

  handlePromoteSucces = () => {
    this.setState({ promoteModalConfirm: false });
  };

  handlePromoteModalClose = () => {
    this.setState({
      promoteModal: false,
      moreOptions: null,
      selectedProject: null,
      selectedOrg: null,
      selectedProdProject: null
    });
  };


  fetchOrganisations = () => {
    const organisationsList = this.props.organisationReducer?.memberOrganisations || [];
    this.setState({ availableOrgs: organisationsList });
  };

  fetchProductionProjects = (orgId) => {
    const { dispatch } = this.props;
    dispatch(sendActionAsync("LIST_ORG_PROJECT", { orgId }));
  };


  handleOrgSelect = (selectedOption) => {
    this.setState({ selectedOrg: selectedOption, selectedProdProject: null });
    this.fetchProductionProjects(selectedOption.value);
  };

  handleProjectSelect = (selectedProdProject) => {
    this.setState({ selectedProdProject });
  };

  openConfirmModal = () => {
    this.setState({ confirmModal: true });
  };

  handlePromoteCancel = () => {
    this.setState({
      promoteModal: false,
      moreOptions: null,
      selectedProject: null,
      selectedOrg: null,
      selectedProdProject: null,
      promoteModalConfirm: null
    });
  };

  confirmPromote = () => {
    const { dispatch } = this.props;
    const { selectedProdProject, selectedOrg, selectedProject } = this.state;

    const payload = {
      name: selectedProdProject.label,
      promoted: true,
      id: selectedProject._id,
      targetOrgID: selectedOrg.value,
      targetProjectId: selectedProdProject.value
    };

    dispatch(sendActionAsync("EXPORT_PROJECT", payload));
    this.setState({ promoteModal: false, moreOptions: null, selectedProject: null, selectedOrg: null, promotLoading: true });
  };

  toggleDeleteModal = (projectName) => {
    this.setState({ moreOptions: null, deleteModal: projectName });
  };

  handleModalClose = () => {
    this.setState({ moreOptions: null, addProjectForm: null, inputErrors: '', deleteModal: false, exportModal: false, newProjectModal: false, selectedProject: null });
  };

  handleDeleteProject = () => {
    this.setState({ moreOptions: null, deleteModal: false, selectedProject: null, loading: true });
    const project = { ...this.state.selectedProject, isDeleted: true };
    const { dispatch } = this.props;
    dispatch(sendActionAsync(DELETE_PROJECT, project));
  };

  handleMoreOptions = (project, event) => {
    this.setState({ moreOptions: this.state.moreOptions === null ? event.currentTarget : null, selectedProject: project });
  };

  handleOptionsClose = () => {
    this.setState({ moreOptions: null, selectedProject: null });
  };

  renderLoadingOverlay = () => (
    <div className="absolute bg-[#F7F7F7] bg-opacity-50 z-10 h-full w-full flex items-center justify-center text-[inter]">
      <ListContentLoader numberOfColumns={3} numberOfRows={3} />
    </div>
  );

  renderProjects = () => {
    let projects = this.props.projectReducer.projects || [];
    const { moreOptions, sort, clonedProjectName, validProject, loading } = this.state;
    const currentUrl = this.props.router.pathname;
    const isRecent = currentUrl.includes('recent');
    const open = Boolean(moreOptions);

    if (sort === 'Last Modified' || isRecent) {
      let recentProjectIds = [];
      try {
        const recentProjectsStr = localStorage.getItem('recentProjects');
        recentProjectIds = recentProjectsStr ? JSON.parse(recentProjectsStr) : [];
      } catch (error) {
        console.error('Error getting recent projects from localStorage:', error); //eslint-disable-line no-console
      }
      const sortedProjects = projects.slice().sort((a, b) => {
        let aDate = a.lastAccessed || a.updated || a.timestamp || a.createdAt || 0;
        let bDate = b.lastAccessed || b.updated || b.timestamp || b.createdAt || 0;

        if (isRecent && recentProjectIds.length > 0) {
          const aIndex = recentProjectIds.indexOf(a._id);
          const bIndex = recentProjectIds.indexOf(b._id);

          if (aIndex !== -1 && bIndex !== -1) {
            return aIndex - bIndex;
          }
          else if (aIndex !== -1) {
            return -1; // a comes first
          }
          else if (bIndex !== -1) {
            return 1; // b comes first
          }
        }

        return new Date(bDate) - new Date(aDate);
      });
      projects = sortedProjects;
    }

    const clonedProjectQuestionAnswer = clonedProjectName && { name: clonedProjectName };

    return (<div className='relative h-full w-full' style={{ pointerEvents: loading ? 'none' : 'auto' }}>
      {loading && this.renderLoadingOverlay()}
      {isRecent && (
        <div className="mb-4 px-2">
          <div className="flex items-center mb-2">
            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#0074F1" className="bi bi-clock" viewBox="0 0 16 16">
                <path d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z" />
                <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z" />
              </svg>
            </div>
            <span className="text-sm text-gray-500">Showing recently accessed projects</span>
          </div>
        </div>
      )}
      <div className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 w-full py-1'>
        {projects.map((project, index) => (
          <div key={index}
            className='flex flex-col p-4 justify-between'
            style={{
              backgroundColor: '#EDEDED',
              height: '150px',
              borderRadius: '8px',
              minWidth: '200px'
            }}
          >

            <div className='flex justify-between items-center w-full'>

              <div className='flex items-center w-[80%]' title={project.name}>
                <div className='w-[36px] h-[36px] mr-2 min-w-[36px] min-h-[36px]'>
                  {project.assets?.favIcons && project.assets.favIcons.length ? (
                    project.assets.favIcons.slice(-1).map((icon) => {
                      return <img key={project.name} alt={project.name} className='w-full h-full rounded-[6px]' src={icon.url} />;
                    })
                  ) : (<div style={{ backgroundColor: stc(project.name) }} className='text-neutral-content rounded-[6px] h-full w-full flex items-center justify-center'>
                    <span className="text-[20px] text-white"><span>{project.name.charAt(0).toUpperCase()}</span></span>
                  </div>)
                  }
                </div>
                <h2 style={{
                  fontWeight: '600',
                  color: '#000000',
                  fontSize: '16px',
                  width: '80%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  letterSpacing: '0.02em'
                }}
                >{project.name}
                </h2>
              </div>
              <button className={`btn btn-circle btn-ghost btn-sm ${this.state.selectedProject?._id === project._id && 'bg-[#0074F1] hover:bg-[#0074F1] text-white'}`} onClick={this.handleMoreOptions.bind(this, project)}>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" className="bi bi-three-dots" viewBox="0 0 16 16">
                  <path d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3m5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3m5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3" />
                </svg>
              </button>
              <Menu
                id={project._id}
                anchorEl={moreOptions}
                keepMounted
                open={open}
                onClose={this.handleOptionsClose.bind(this)}
                placement="bottom-end"
                sx={{
                  '& .MuiPaper-root': { padding: '0px', margin: '0px', borderRadius: '8px' },
                  '& .MuiList-root': { padding: '0px', margin: '0px' },
                  '& .MuiMenuItem-root': { padding: '0px', margin: '0px' }
                }}
              >
                <div className='menu bg-[#EEEEEE] rounded-[3px] text-[#343A40] font-[inter] justify-center w-[100px]' style={{ padding: '0px', margin: '0px', boxShadow: '0px 0px 25px 0px rgba(0, 0, 0, 0.1)', border: '0.5px solid #0074F1', borderRadius: '8px' }}>
                  <button className={styledButtonClass} onClick={this.toggleDeleteModal.bind(this, project.name)}>Delete</button>
                  <button className={styledButtonClass} onClick={this.handleExportProject.bind(this)}>Export</button>
                  <button className={styledButtonClass} onClick={this.toggleCloneProject.bind(this)}>Clone</button>
                  <button className={styledButtonClass} onClick={this.togglePromoteModal.bind(this)}>Promote</button>
                </div>
              </Menu>
              <Dialog
                BackdropProps={{
                  style: { backgroundColor: 'rgba(0, 0, 0, 0.3)' }
                }}
                open={this.state.exportModal === project.name}
                onClose={this.handleModalClose.bind(this)}
                PaperProps={{ style: { width: '30%' } }}
              >
                <DialogTitle>Confirm</DialogTitle>
                <DialogContent sx={{ minHeight: '60px', paddingBottom: '0px' }}>
                  <p>
                    {this.props.projectReducer.exportProject
                      ? 'Are you sure you want to download the project?'
                      : 'We are preparing your project for download. Please wait a moment...'}
                  </p>
                </DialogContent>
                <DialogActions sx={{ paddingTop: '0px' }}>
                  <Button onClick={this.handleModalClose.bind(this)}>
                    Cancel
                  </Button>
                  {this.props.projectReducer.exportProject ? (
                    <a href={this.props.projectReducer.exportProject} download={`${project.name}.json`} target="_blank" >
                      <Button variant="contained" onClick={this.handleModalClose.bind(this)} id="modalButtons">Download</Button>
                    </a>
                  ) : <Button variant="contained" disabled={true}>Download</Button>
                  }
                </DialogActions>
              </Dialog>
              <Dialog
                BackdropProps={{
                  style: { backgroundColor: 'rgba(0, 0, 0, 0.3)' }
                }}
                open={this.state.deleteModal === project.name}
                onClose={this.handleModalClose.bind(this)}
              >
                <DialogTitle>Confirm</DialogTitle>
                <DialogContent>
                  <p>Are you sure you want to delete the project?</p>
                </DialogContent>
                <DialogActions>
                  <Button onClick={this.handleModalClose.bind(this)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={this.handleDeleteProject.bind(this)}
                    id="modalButtons"
                  >
                    Delete
                  </Button>
                </DialogActions>
              </Dialog>
            </div>
            <div className='flex justify-end w-full'>
              <button className='btn btn-sm bg-transparent normal-case rounded-full border border-[2px] border-[#0074F1] hover:bg-[#0074F1] hover:border-transparent hover:text-white text-[#0074F1]'
                style={{ letterSpacing: '0.03em' }}
                onClick={this.handleOpenProject.bind(this, project)}
              >
                Open
              </button>
            </div>
          </div>
        ))}
        <CustomMuiDialog
          open={clonedProjectName}
          title={'Project Clone'}
          schema={newProjectSchema}
          field={clonedProjectQuestionAnswer}
          onSubmit={this.handleCloneProject}
          error={validProject && validProject ? false : "Project name already exists"}
          submitButtonText={'Clone'}
          cancelButtonText={'Cancel'}
        />
      </div>
    </div>
    );
  };

  onProjectSort = (option) => {
    this.setState({ sort: option.label });
  };

  handleClick = () => {
    // Initialize the form with filtered project types when opening the modal
    const schema = { ...newProjectSchema };
    const availableTypes = this.props.userReducer?.loggedInUser?.projectTypes || [];
    const questionSet = schema.questionSets[0].questions.find(q => q.questionId === 'projectType');
    if (questionSet) {
      questionSet.input.options = questionSet.input.options.filter(opt =>
        availableTypes.includes(opt.value)
      );
    }
    this.setState({
      newProjectModal: true,
      addProjectForm: schema
    });
  };

  handleOpenProject = (project) => {
    if (project.pagesPopulated && project.pagesPopulated.length) {
      const updatedProject = {
        ...project,
        lastAccessed: new Date().toISOString()
      };

      this.updateRecentProjectsInLocalStorage(project._id);

      if (this.updateProjectTimestamp) {
        this.updateProjectTimestamp(updatedProject);
      }

      const page1Name = project.pagesPopulated[0].name;
      const branchName = this.props.projectReducer?.projectBranches?.[0].name || 'master';
      this.props.router.push(`/projects/${project._id}/${page1Name}?branch=${branchName}`);
    }
  };

  updateRecentProjectsInLocalStorage = (projectId) => {
    try {
      const recentProjectsStr = localStorage.getItem('recentProjects');
      let recentProjects = recentProjectsStr ? JSON.parse(recentProjectsStr) : [];

      recentProjects = recentProjects.filter(id => id !== projectId);

      recentProjects.unshift(projectId);

      recentProjects = recentProjects.slice(0, 10);

      localStorage.setItem('recentProjects', JSON.stringify(recentProjects));
    } catch (error) {
      console.error('Error updating recent projects in localStorage:', error); //eslint-disable-line no-console
    }
  };

  updateNestedItems = (items) => {
    return items.map(item => {
      if (item.data?.renderProps?.items) {
        return {
          ...item,
          source: { label: item.source.label },
          data: {
            ...item.data,
            renderProps: {
              ...item.data.renderProps,
              items: this.updateNestedItems(item.data.renderProps.items)
            }
          }
        };
      } else {
        return {
          ...item,
          source: {
            label: item.source.label
          }
        };
      }
    });
  };

  toggleCloneProject = () => {
    const clonedProjectName = `${this.state.selectedProject.name}`;
    this.setState({ clonedProjectName, moreOptions: null });
  };

  handleCloneProject = (questionAnswers, cancelled) => {
    if (cancelled) {
      this.setState({ clonedProjectName: null, selectedProject: null });
      return;
    }
    const { dispatch, projectReducer } = this.props;
    const { selectedProject } = this.state;
    const projects = projectReducer.projects || [];
    const projectNameExists = find(projects, { name: questionAnswers.name });

    if (projectNameExists) {
      this.setState({ validProject: false });
    } else {
      this.setState({ clonedProjectName: null, selectedProject: null, loading: true });
      const payload = { name: selectedProject.name, clonedProjectName: questionAnswers.name, id: selectedProject._id };
      dispatch(sendActionAsync('EXPORT_PROJECT', payload));
    }
  };

  handleExportProject = () => {
    const { dispatch } = this.props;
    const selectedProject = this.state.selectedProject;
    const payload = { name: selectedProject.name, id: selectedProject._id };
    this.setState({ moreOptions: null, exportModal: selectedProject.name });
    dispatch(sendActionAsync('EXPORT_PROJECT', payload));
  };

  toggleExportModal = (file) => { // eslint-disable-line consistent-return
    const filteredProject = file ? { ...file } : { ...this.state.selectedProject };
    let projectName = filteredProject.name;

    if (filteredProject.assets?.images) {
      filteredProject.assets.images = filteredProject.assets.images.map((asset) => ({
        ...asset,
        url: asset.url.replace(`drive/astrakraft/${filteredProject._id}/`, "")
      }));
    }

    filteredProject.referenceId = filteredProject._id;
    delete filteredProject._id;
    delete filteredProject.name;
    delete filteredProject._profile;
    delete filteredProject.updatedBy;
    delete filteredProject.pages;
    delete filteredProject._organisation;
    if (!filteredProject.modelVersion || filteredProject.modelVersion < 1) {
      filteredProject.pagesPopulated = filteredProject.pagesPopulated && filteredProject.pagesPopulated.map(({ _project, _id, ...page }) => ({
        ...page,
        screen: mapValues(page.screen, ({ items, source, ...screenValue }) => ({
          ...screenValue,
          items: items && this.updateNestedItems(items)
        }))
      }));

      filteredProject.dialogsPopulated = filteredProject.dialogsPopulated && filteredProject.dialogsPopulated.map(({ _project, _id, ...dialog }) => {
        const { source, ...restScreen } = dialog.screen; // eslint-disable-line no-unused-vars
        return {
          ...dialog,
          screen: {
            ...restScreen,
            items: restScreen.items && this.updateNestedItems(restScreen.items)
          }
        };
      });

      filteredProject.reuse = filteredProject.reuse && filteredProject.reuse.length && this.updateNestedItems(filteredProject.reuse);
    } else {
      filteredProject.pagesPopulated = filteredProject.pagesPopulated.map(({ _project, _id, ...page }) => page);
      filteredProject.dialogsPopulated = filteredProject.dialogsPopulated.map(({ _project, _id, ...dialog }) => dialog);
    }

    if (file) {
      return filteredProject;
    } else {
      filteredProject.version = 0;
      const jsonData = JSON.stringify(filteredProject, null, 2);
      const dataURI = 'data:application/json;charset=utf-8,' + encodeURIComponent(jsonData);
      this.setState({ moreOptions: null, exportModal: projectName, url: dataURI });
    }
  };

  convertToBase64 = (project) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = () => {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsText(project);
    });
  };

  handleFileChange = async (e) => {
    const project = e.target.files[0];
    if (e.target.files.length === 0) {
      this.setState({ selectedFile: null, validProject: true, importErrorMessage: null });
      if (this.fileInput) {
        this.fileInput.value = '';
      }
    } else if (project && project.type === 'application/json') {
      const file = await this.convertToBase64(project);
      const finalProject = this.toggleExportModal(JSON.parse(file));
      this.setState({ selectedFile: finalProject, validProject: true, importErrorMessage: null });
    } else {
      this.setState({ selectedFile: null, validProject: false, importErrorMessage: 'Please select a valid JSON file.' });
      if (this.fileInput) {
        this.fileInput.value = '';
      }
    }
  };

  submitForm = () => {
    this.refs['import-project'].submitForm();
  };

  submitNewProjectForm = () => {
    this.refs['new-project'].submitForm();
  };

  onUpdate = () => {
    this.setState({ validProject: true });
  };

  toggleImportModal = () => {
    this.setState((prevState) => ({
      moreOptions: null,
      importModal: !prevState.importModal,
      selectedFile: null,
      validProject: true,
      importErrorMessage: null
    }));
    if (this.refs['import-project']) {
      this.refs['import-project'].resetForm();
    }
    if (this.fileInput) {
      this.fileInput.value = '';
    }
  };

  onNewProjectSubmit = (questionAnswers) => {
    const { dispatch, projectReducer } = this.props;
    const projects = projectReducer.projects || [];
    const existing = find(projects, { name: questionAnswers.name });
    if (!existing) {
      const updatedQA = { ...questionAnswers };
      if (!updatedQA.projectType) {
        updatedQA.projectType = 'WEB_APPLICATION';
      }
      const projectPayload = { ...updatedQA, assets: { fonts: ['Roboto'] }, currentLanguage: 'en-US', selectedScreenType: 'mobile', isProduction: questionAnswers.isProduction || false };
      const pagePayload = [{
        id: randstr('page_'),
        screen: extendedBasicScreensInfo,
        name: 'Page1',
        path: 'page1'
      }, {
        id: randstr('page_'),
        screen: extendedBasicScreensInfo,
        name: '_Theme',
        path: '_theme'
      }];
      const dialogPayload = {
        id: randstr('dialog_'),
        screen: dialogScreen,
        name: 'Dialog1',
        path: 'dialog1'
      };
      projectPayload.pagePayload = pagePayload;
      projectPayload.dialogPayload = dialogPayload;
      dispatch(sendActionAsync('CREATE_PROJECT', projectPayload));
      this.setState({ newProjectModal: false });
    } else {
      this.setState({
        inputErrors: `Project name ${questionAnswers.name} already exists`
      });
    }
  };

  onAddProjectUpdate = () => {
    this.setState({ inputErrors: '' });
  };

  onSubmit = (questionAnswers) => {
    const { dispatch } = this.props;
    const projects = this.props.projectReducer.projects || [];
    const organisation = this.props.userReducer.profileDetails._organisation;
    const existing = projects.filter((each) => each.name === questionAnswers.name);

    if (!this.state.selectedFile) {
      this.setState({ validProject: false, importErrorMessage: 'Please select a file to import.' });
      return;
    }

    if (existing.length === 0) {
      this.setState({ moreOptions: null, selectedFile: null, importModal: false, clonedProjectName: false, validProject: true, loading: true });
      const jsonData = this.state.selectedFile;
      const restoredProject = {
        ...jsonData,
        name: questionAnswers.name,
        importedProject: true,
        _organisation: organisation
      };
      dispatch(sendActionAsync('IMPORT_PROJECT', restoredProject));
      if (this.refs['import-project']) {
        this.refs['import-project'].resetForm();
      }
      // Clear the file input field
      if (this.fileInput) {
        this.fileInput.value = '';
      }
    } else {
      this.setState({
        validProject: false,
        importErrorMessage: `Project name ${questionAnswers.name} already exists`
      });
    }
  };
}
export default withRouter(connectToStores(ProjectList));
