import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { withRouter } from 'next/router';
import { reduxUtil, genericAction } from '@limegroup/lime-redux-util';
import { Popper } from '@mui/material';
import { utils } from '@astrakraft/core-lib';
const { stringUtil } = utils;
const { randstr, generateUniquePage } = stringUtil;
import editPageSchema from './schemas/edit-page.json';
import { cloneDeep, find, findIndex, isEmpty, isEqual } from 'lodash';
import { projectConstants } from '@astrakraft/core-lib';
import metaTagSchema from './schemas/meta-tag.json';
import linkTagSchema from './schemas/link-tag.json';
import titleSchema from './schemas/edit-title.json';
import CustomMuiDialog from '../../components/custom-mui-dialog';
import Select from 'react-select';
import { PAGE_TYPE_WEB, PAGE_TYPE_PEGA_LAYOUT, PAGE_TYPE_PEGA_WIDGET, PAGE_TYPE_PEGA_ELEMENT, PAGE_TYPE_LABELS } from './common/page-types';

const { PROJECT_TYPE_PEGA_DX_COMPONENT, basicScreensInfo } = projectConstants;
const { connectToStores } = reduxUtil;
const { sendAction } = genericAction;

// Extended basicScreensInfo with mobileLandscape support
const extendedBasicScreensInfo = {
  ...basicScreensInfo,
  mobileLandscape: {
    data: {
      renderProps: {
        color: 'black',
        padding: 'none',
        margin: 'none',
        showInProgress: false,
        backgroundColor: '#fff',
        horizontalOverflow: 'auto',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        verticalOverflow: 'auto'
      }
    },
    items: [],
    resolutions: { height: '400px', width: '800px' }
  }
};

const styledButtonClass = 'btn btn-sm btn-ghost justify-start text-[10px] font-[400] hover:bg-transparent hover:text-[#0074F1] text-[#000000] tracking-widest';

const tabList = ['Meta Tags', 'Links', 'Others'];

const visibilityOptions = [
  { label: "Public", value: "PUBLIC" },
  { label: "Private", value: "CUSTOMER_PRIVATE" },
  { label: "Any authenticated user", value: "CUSTOMER_PUBLIC" },
  { label: "Any authenticated colleague", value: "COLLEAGUE_USER" },
  { label: "Colleague Admin", value: "COLLEAGUE_ADMIN" },
  { label: "Colleague Limited 1", value: "COLLEAGUE_LIMITED1" },
  { label: "Colleague Limited 2", value: "COLLEAGUE_LIMITED2" },
  { label: "Colleague Limited 3", value: "COLLEAGUE_LIMITED3" }
];

function getPageName(pageType) {
  switch (pageType) {
    case `${PAGE_TYPE_PEGA_LAYOUT}`:
      return {
        name: 'Layout',
        path: 'layout'
      };

    case `${PAGE_TYPE_PEGA_ELEMENT}`:
      return {
        name: 'Fields',
        path: 'fields'
      };

    case `${PAGE_TYPE_PEGA_WIDGET}`:
      return {
        name: 'Widget',
        path: 'widget'
      };

    default:
      return {
        name: 'Page',
        path: 'page'
      };
  }
}

class AddPages extends Component {
  static propTypes = {
    pages: PropTypes.array.isRequired
  };

  constructor(props) {
    super(props);

    this.state = {
      showPageModal: false,
      showPageType: false,
      duplicatePageDetailsError: '',
      selectedFile: null,
      selectedPageOptionId: null,
      showSettings: false,
      settings: {
        visibility: ''
      },
      editTagData: null,
      showAddTagsDialog: false,
      selectedTab: tabList[0]
    };
    this.fileInputRef = React.createRef();
  }

  componentDidMount() {
    // backward compatibility for old meta, link tags
    if (this.props.projectReducer?.myProject?.pagesPopulated) {
      const pagesPopulated = this.props.projectReducer.myProject.pagesPopulated;
      for (const page of pagesPopulated) { // eslint-disable-line no-unused-vars
        const newSettings = { ...page.settings };

        if ('canonicalLink' in newSettings) {
          newSettings.linkTagData = newSettings.linkTagData || [];
          newSettings.linkTagData.push({ rel: 'canonical', href: newSettings.canonicalLink });
          delete newSettings.canonicalLink;
        }

        if ('keywords' in newSettings) {
          newSettings.metaTagData = newSettings.metaTagData || [];
          newSettings.metaTagData.push({ name: 'name', metaDataName: 'keywords', content: newSettings.keywords });
          delete newSettings.keywords;
        }

        if ('description' in newSettings) {
          newSettings.metaTagData = newSettings.metaTagData || [];
          newSettings.metaTagData.push({ name: 'name', metaDataName: 'description', content: newSettings.description });
          delete newSettings.description;
        }

        if (!isEqual(Object.keys(page.settings || {}), Object.keys(newSettings))) {
          this.props.dispatch(sendAction('UPDATE_PAGE_SETTINGS', { settings: newSettings, selectedPage: page }));
        }
      }
    }
    // ends here delete later
    document.addEventListener('click', this.handleClickOutside);
  }

  componentWillUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
  }

  handleChangePage = (pageName) => {
    const { dispatch, projectReducer: { myProject } } = this.props;
    const branchName = this.props.projectReducer?.selectedBranch?.name;
    this.props.router.push(`/projects/${myProject._id}/${pageName}?branch=${branchName}`);
    dispatch(sendAction('ELEMENT_SELECTED', null));
  }

  render() {
    const pages = this.props.pages && this.props.pages.filter((page) => page.path !== '_theme') || [];
    const themePage = this.props.pages && this.props.pages.filter((page) => page.path === '_theme') || [];
    const {
      duplicatePageDetailsError,
      pageDetailsQuestionAnswers,
      showPageModal,
      showSettings,
      selectedPageOptionId,
      showPageType,
      settings,
      editTagData,
      selectedTab,
      showAddTagsDialog,
      duplicateTagDataError
    } = this.state;

    const selectedPage = this.props.projectReducer.selectedPage && this.props.projectReducer.selectedPage;
    const pageType = settings?.pageType || PAGE_TYPE_WEB;
    const pageTypeLabel = PAGE_TYPE_LABELS[pageType];
    const visibilityOption = visibilityOptions.find(option => option.value === settings.visibility);

    const dropdownStyles = {
      dropdownIndicator: (base, state) => ({
        ...base,
        transform: state.selectProps.menuIsOpen && 'rotate(180deg)',
        transition: 'all .2s ease'
      }),
      control: (base) => ({
        ...base,
        width: 'full',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      })
    };

    return (
      <>
        {
          this.props.popupOpenMain &&
          <div className='h-full w-full'>
            {showSettings ?
              <>
                <div className='flex'>
                  <button className='btn btn-ghost normal-case mb-1 text-[#343A40] btn-sm' onClick={this.onBackToPages.bind(this)}><i className="bi bi-arrow-left"></i>Back</button>
                </div>
                <div style={{ marginTop: '3px', borderTop: '3px solid #75bbff' }} />
                <div className='w-full h-[93%] px-2 text-[14px]'>
                  <div className='flex items-center my-2 w-full'>
                    <span className='mr-2 text-gray-500 w-[30%]' title={'Visibility'}>Visibility :</span>
                    <div className='w-[70%]'>
                      <Select
                        name={visibilityOption.label}
                        options={visibilityOptions}
                        isSearchable={false}
                        placeholder={visibilityOption.label}
                        isClearable={false}
                        defaultValue={visibilityOptions.find(option => option.label === visibilityOption.label)}
                        onChange={this.onVisibilityChange.bind(this)}
                        styles={dropdownStyles}
                      />
                    </div>
                  </div>
                  <div className='flex items-center text-gray-500 mb-2'>
                    <span>Page type :</span>
                    <span className='ml-2'>{pageTypeLabel}</span>
                  </div>
                  <div className="border-b border-gray-200 w-full">
                    <ul className="flex flex-wrap">
                      {tabList.map((tab) => (
                        <li key={tab} className="mr-1">
                          <button
                            className={`btn btn-sm p-0 normal-case rounded-none px-2 ${selectedTab === tab
                              ? 'text-[#0074f1] border-b-2 border-[#0074f1] hover:border-[#0074f1]'
                              : 'text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300'
                              } text-sm font-medium border-t-0 border-l-0 border-r-0 bg-transparent hover:bg-transparent`}
                            onClick={() => this.setState({ selectedTab: tab })}
                          >
                            {tab}
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                  {this.renderMetaData(selectedTab)}
                </div>

                <CustomMuiDialog
                  open={showAddTagsDialog === 'add-tag'}
                  title={`Add ${selectedTab === 'Meta Tags' ? 'Meta' : 'Link'} Tag`}
                  schema={selectedTab === 'Meta Tags' ? metaTagSchema : linkTagSchema}
                  onSubmit={this.onAddTagDataSubmit}
                  field={editTagData}
                  submitButtonText={'Add'}
                  cancelButtonText={'Cancel'}
                  error={duplicateTagDataError}
                  formId={this.state.formId}
                />

                <CustomMuiDialog
                  open={showAddTagsDialog === 'edit-tag'}
                  title={`Edit ${selectedTab === 'Others' ? 'Title' : selectedTab === 'Meta Tags' ? 'Meta' : 'Link'} Tag`}
                  schema={selectedTab === 'Others' ? titleSchema : selectedTab === 'Meta Tags' ? metaTagSchema : linkTagSchema}
                  field={editTagData}
                  onSubmit={this.onEditTagDataSubmit}
                  error={duplicateTagDataError}
                  submitButtonText={'Update'}
                  cancelButtonText={'Cancel'}
                  formId={this.state.formId}
                />
              </>
              :
              <div className='p-3 h-full w-full'>
                <div className='flex justify-end items-center'>
                  <button className="btn btn-ghost btn-sm hover-sidemenu-options text-[#343A40] text-[12px] font-[400] leading-[12.1px] tracking-[0.1em] h-[26px]"
                    onClick={this.importPage}>
                    <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15.5 12.25V15.5H2.49998V12.25H0.333313V15.5C0.333313 16.6917 1.30831 17.6667 2.49998 17.6667H15.5C16.6916 17.6667 17.6666 16.6917 17.6666 15.5V12.25H15.5ZM3.58331 5.75004L5.11081 7.27754L7.91665 4.48254V13.3334H10.0833V4.48254L12.8891 7.27754L14.4166 5.75004L8.99998 0.333374L3.58331 5.75004Z" />
                    </svg>
                    Upload
                  </button>
                  <button data-popper-id="new-page-btn" className="btn btn-ghost btn-sm hover-sidemenu-options text-[#343A40] text-[12px] font-[400] leading-[12.1px] tracking-[0.1em] h-[26px]"
                    onClick={this.onNewPageClick}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15.5834 9.08329H9.08335V15.5833H6.91669V9.08329H0.416687V6.91663H6.91669V0.416626H9.08335V6.91663H15.5834V9.08329Z" />
                    </svg>
                    New
                  </button>

                  <Popper
                    disablePortal
                    open={showPageType}
                    anchorEl={document.querySelector(`[data-popper-id="new-page-btn"]`)}
                    close={this.handleClickOutside}
                    placement="bottom-end"
                    className='rounded-[8px] z-10'
                  >
                    <ul className='menu page-options bg-[#EEEEEE] rounded-[8px] text-[#343A40] font-[inter] w-[135px] h-[130px] p-2.5 px-3 justify-center my-[4px]' style={{ boxShadow: '0px 0px 25px 0px rgba(0, 0, 0, 0.1)', border: '1px solid #0074F1' }}>
                      <button className={styledButtonClass} onClick={this.addPage.bind(this, PAGE_TYPE_PEGA_LAYOUT)}>Layout</button>
                      <button className={styledButtonClass} onClick={this.addPage.bind(this, PAGE_TYPE_PEGA_ELEMENT)}>Field</button>
                    </ul>
                  </Popper>

                </div>
                <DragDropContext onDragEnd={this.onDragEnd}>
                  <Droppable droppableId="pageList">
                    {(provided) => (
                      <ul {...provided.droppableProps} ref={provided.innerRef}
                        style={{
                          listStyleType: 'none',
                          height: '78%',
                          width: '100%',
                          overflowY: 'auto'
                        }}
                        className='custom-scrollbar px-2 my-3 relative'
                      >
                        {pages.map((page, index) => {
                          const { data, _id, id, ...downloadPage } = page; // eslint-disable-line no-unused-vars
                          downloadPage.data = { ...data };
                          delete downloadPage.data.eventProps;
                          const jsonData = JSON.stringify(downloadPage, null, 2);
                          const dataURI = `data:application/json;charset=utf-8,${encodeURIComponent(jsonData)}`;
                          const pageId = page.id;
                          return (
                            <Draggable key={pageId} draggableId={pageId} index={index}>
                              {(provided) => (
                                <li
                                  onClick={this.handleChangePage.bind(this, page.name)}
                                  id={'draggable-icon'}
                                  className="flex justify-between mt-[4px] bg-[#EEEEEE] p-0 rounded-[8px] h-[60px] w-full text-[#343A40] hover:bg-[#e7e9eb]"
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  ref={provided.innerRef}
                                >
                                  <div style={{ height: '100%', borderRadius: '8px 0px 0px 8px', border: `4px solid ${selectedPage?.name === page.name ? '#0074F1' : '#495057'}` }} />
                                  <div className="flex justify-between items-center pl-3 pr-2" style={{ width: '96%' }}>
                                    <div className='flex items-center px-1'
                                      style={{
                                        display: 'flex',
                                        height: '100%',
                                        width: 'calc(100% - 38px)',
                                        overflow: 'hidden'
                                      }}
                                    >
                                      <svg width="18" height="18" viewBox="0 0 14 14" fill="#76818D" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M0.25 0.25V6.25H6.25V0.25H0.25ZM4.75 4.75H1.75V1.75H4.75V4.75ZM0.25 7.75V13.75H6.25V7.75H0.25ZM4.75 12.25H1.75V9.25H4.75V12.25ZM7.75 0.25V6.25H13.75V0.25H7.75ZM12.25 4.75H9.25V1.75H12.25V4.75ZM7.75 7.75V13.75H13.75V7.75H7.75ZM12.25 12.25H9.25V9.25H12.25V12.25Z" />
                                      </svg>
                                      <p
                                        style={{
                                          marginLeft: '10px',
                                          overflow: 'hidden',
                                          textOverflow: 'ellipsis',
                                          whiteSpace: 'nowrap',
                                          width: '100%',
                                          textAlign: 'left',
                                          fontSize: '14px',
                                          fontWeight: '400'
                                        }}
                                        title={page.name}
                                      >
                                        {page.name}
                                      </p>
                                    </div>
                                    <button className={`btn btn-circle btn-sm btn-ghost p-0 m-0 ${selectedPageOptionId === pageId && 'bg-[#0074F1] hover:bg-[#0074F1]'}`} onClick={this.handlePageOptions.bind(this, pageId)} data-popper-id={pageId}>
                                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" strokeWidth="1.5" width="25" height="25" stroke={selectedPageOptionId === pageId ? '#FFFFFF' : 'currentColor'}>
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z" />
                                      </svg>
                                    </button>
                                    <Popper
                                      disablePortal
                                      open={selectedPageOptionId === pageId}
                                      anchorEl={document.querySelector(`[data-popper-id="${selectedPageOptionId}"]`)}
                                      close={this.handleClickOutside}
                                      placement="bottom-end"
                                      className='rounded-[8px]'
                                    >
                                      <ul className='menu page-options bg-[#EEEEEE] rounded-[8px] text-[#343A40] font-[inter] w-[135px] p-3 justify-center my-[4px]' style={{ boxShadow: '0px 0px 25px 0px rgba(0, 0, 0, 0.1)', border: '1px solid #0074F1' }}>
                                        <a href={dataURI} download={`${page.name}.json`} target='_blank' onClick={this.exportProject.bind(this)}>
                                          <button className={styledButtonClass}>Download</button>
                                        </a>
                                        <button className={styledButtonClass} onClick={this.duplicatingPage.bind(this, page)}>Make Copy</button>
                                        <button className={styledButtonClass} onClick={this.handlePageNameEdit.bind(this, page)}>Edit</button>
                                        {pages.length > 1 && <button className={styledButtonClass} onClick={this.onDeletePage.bind(this, page)}>Delete</button>}
                                        <button className={styledButtonClass} onClick={this.onSettingsPage.bind(this, page)}>Settings</button>
                                      </ul>
                                    </Popper>
                                  </div>
                                </li>
                              )}
                            </Draggable>
                          );
                        })}
                        {provided.placeholder}
                      </ul>
                    )}
                  </Droppable>
                </DragDropContext>
                {themePage && themePage.length &&
                  <div
                    onClick={this.handleChangePage.bind(this, '_Theme')}
                    className="flex justify-between bg-[#EEEEEE] p-0 rounded-[8px] h-[60px] text-[#343A40] mx-2 pointer hover:bg-[#e7e9eb]"
                  >
                    {themePage.map((page) =>
                      <><div style={{ height: '100%', borderRadius: '8px 0px 0px 8px', border: `4px solid ${selectedPage?.name === page.name ? '#0074F1' : '#495057'}` }} />
                        <div className="flex justify-between items-center px-3" style={{ width: '96%' }}>
                          <div className='flex items-center px-1'
                            style={{
                              display: 'flex',
                              height: '100%',
                              width: 'calc(100% - 40px)',
                              overflow: 'hidden'
                            }}
                          >
                            <i className="bi bi-x-diamond-fill" style={{ color: '#76818D', fontSize: '18px' }}></i>
                            <p
                              style={{
                                marginLeft: '10px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                width: '100%',
                                textAlign: 'left',
                                fontSize: '14px',
                                fontWeight: '400'
                              }}
                              title={page.name}
                            >
                              {page.name}
                            </p>
                          </div>
                        </div>
                      </>)}
                  </div>
                }
                <CustomMuiDialog
                  open={showPageModal === 'edit-page'}
                  title={'Edit Page'}
                  schema={editPageSchema}
                  field={pageDetailsQuestionAnswers}
                  onSubmit={this.onEditPageSubmit}
                  submitButtonText={'Update'}
                  cancelButtonText={'Cancel'}
                  error={duplicatePageDetailsError}
                  formId={this.state.formId}
                />
                <CustomMuiDialog
                  open={showPageModal === 'import-page'}
                  title={'Import Page'}
                  schema={editPageSchema}
                  field={pageDetailsQuestionAnswers}
                  onSubmit={this.onImportPageSubmit}
                  submitButtonText={'Import'}
                  cancelButtonText={'Cancel'}
                  error={duplicatePageDetailsError}
                  formId={this.state.formId}
                >
                  <label>Select File</label>
                  <input
                    type="file"
                    ref={this.fileInputRef}
                    key="import-page"
                    className="pointer w-[100%] h-[48px] mb-1 rounded-[4px] text-sm file:mr-4 file:rounded-[4px] file:cursor-pointer file:custom-button file:btn file-input-custom file:btn-sm file:border-0 file:h-full file:w-[25%] file:text-white file:text-[12px] file:bg-[#0074F1] border border-gray-300"
                    id="import-page"
                    accept='.json'
                    onChange={this.handleFileChange}
                  />
                </CustomMuiDialog>
              </div>
            }
          </div>}
      </>
    );
  }

  renderMetaData = (selectedTab) => {
    const { selectedPage } = this.state;
    const pagesPopulated = this.props.projectReducer.myProject.pagesPopulated;
    const filteredPage = find(pagesPopulated, { id: selectedPage.id });
    const title = filteredPage.settings?.title;

    switch (selectedTab) {
      case 'Links':
        return (
          <div className='w-full h-[80%]'>
            <div className='w-full flex justify-end my-1'>
              <button className="btn btn-ghost btn-sm hover-sidemenu-options text-[#343A40] text-[12px] font-[400] leading-[12.1px] tracking-[0.1em] text-left h-[26px]"
                onClick={() => this.setState({ showAddTagsDialog: 'add-tag', formId: randstr() })}>
                <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15.5834 9.08329H9.08335V15.5833H6.91669V9.08329H0.416687V6.91663H6.91669V0.416626H9.08335V6.91663H15.5834V9.08329Z" />
                </svg>
                Add
              </button>
            </div>
            <div className='p-1 px-2 h-[90%] flex w-full flex-col items-center overflow-y-auto'>
              {filteredPage?.settings?.linkTagData && filteredPage.settings.linkTagData.length ? filteredPage.settings.linkTagData.map((each, index) => (
                <div className='bg-white p-3 w-full mb-2 rounded-md relative text-[14px]' key={each.id}>
                  <div className='w-full flex justify-end'>
                    <button onClick={this.handleDeleteTags.bind(this, 'linkTagData', index)} title='delete'
                      style={{ '--fallback-background': '#F7F7F7' }}
                      className='p-0 btn btn-ghost btn-sm absolute top-[14%] right-[3%] btn-circle custom-button'>
                      <svg width="14" height="14" viewBox="0 0 56 72" fill="red" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 64C4 68.4 7.6 72 12 72H44C48.4 72 52 68.4 52 64V16H4V64ZM12 24H44V64H12V24ZM42 4L38 0H18L14 4H0V12H56V4H42Z" fill="reed" />
                      </svg>
                    </button>
                    <button onClick={this.handleEditTagData.bind(this, each)} title='edit'
                      style={{ '--fallback-background': '#F7F7F7' }}
                      className='p-0 btn btn-ghost btn-sm absolute top-[14%] right-[13%] btn-circle custom-button'>
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="#0074f1" className="bi bi-pencil-square" viewBox="0 0 16 16">
                        <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                        <path fillRule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5z" />
                      </svg>
                    </button>
                  </div>
                  <div className='flex flex-col'>
                    {Object.entries(each).map(([key, value]) => {
                      if (value) {
                        return (
                          <span
                            className='text-black font-[500]'
                            style={{
                              width: '80%',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              letterSpacing: '0.02em'
                            }}
                            key={key}>
                            {key.charAt(0).toUpperCase() + key.slice(1)}: <span title={value} className='text-gray-500'>{value}</span>
                          </span>
                        );
                      }
                      return null;
                    })}
                  </div>
                </div>
              )) : (
                <div className='flex items-center justify-center w-full h-full'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-box-seam" viewBox="0 0 16 16">
                    <path d="M8.186 1.113a.5.5 0 0 0-.372 0L1.846 3.5l2.404.961L10.404 2zm3.564 1.426L5.596 5 8 5.961 14.154 3.5zm3.25 1.7-6.5 2.6v7.922l6.5-2.6V4.24zM7.5 14.762V6.838L1 4.239v7.923zM7.443.184a1.5 1.5 0 0 1 1.114 0l7.129 2.852A.5.5 0 0 1 16 3.5v8.662a1 1 0 0 1-.629.928l-7.185 2.874a.5.5 0 0 1-.372 0L.63 13.09a1 1 0 0 1-.63-.928V3.5a.5.5 0 0 1 .314-.464z" />
                  </svg>
                  <span className='text-gray-500 ml-2'>Nothing to show</span>
                </div>
              )}
            </div>
          </div>
        );

      case 'Others':
        return (
          <div className='w-full h-[80%]'>
            <div className='p-1 px-2 h-[90%] flex w-full flex-col items-center overflow-y-auto'>
              <div className='bg-white p-3 w-full mb-2 rounded-md relative text-[14px] mt-4'>
                <div className='w-full flex justify-end'>
                  <button onClick={this.handleEditTagData.bind(this, { title })} title={'edit'}
                    style={{ '--fallback-background': '#F7F7F7' }}
                    className='p-0 btn btn-ghost btn-sm absolute top-[14%] right-[3%] btn-circle custom-button'>
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="#0074f1" className="bi bi-pencil-square" viewBox="0 0 16 16">
                      <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                      <path fillRule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5z" />
                    </svg>
                  </button>
                </div>
                <span
                  className='text-black font-[500]'
                  style={{
                    width: '80%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    letterSpacing: '0.02em'
                  }}>
                  Tiltle : <span title={title} className='text-gray-500'>{title}</span>
                </span>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className='w-full h-[80%]'>
            <div className='w-full flex justify-end my-1'>
              <button className="btn btn-ghost btn-sm hover-sidemenu-options text-[#343A40] text-[12px] font-[400] leading-[12.1px] tracking-[0.1em] text-left h-[26px]"
                onClick={() => this.setState({ showAddTagsDialog: 'add-tag', formId: randstr() })}>
                <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15.5834 9.08329H9.08335V15.5833H6.91669V9.08329H0.416687V6.91663H6.91669V0.416626H9.08335V6.91663H15.5834V9.08329Z" />
                </svg>
                Add
              </button>
            </div>
            <div className='p-1 px-2 h-[90%] flex w-full flex-col items-center overflow-y-auto'>
              {filteredPage?.settings?.metaTagData && filteredPage.settings.metaTagData.length ? filteredPage.settings.metaTagData.map((each, index) => {
                return (
                  <div className='bg-white p-3 w-full mb-2 rounded-md relative text-[14px]'>
                    <div className='w-full flex justify-end'>
                      <button onClick={this.handleDeleteTags.bind(this, 'metaTagData', index)} title={'delete'}
                        style={{ '--fallback-background': '#F7F7F7' }}
                        className='p-0 btn btn-ghost btn-sm absolute top-[14%] right-[3%] btn-circle custom-button'>
                        <svg width="14" height="14" viewBox="0 0 56 72" fill="red" xmlns="http://www.w3.org/2000/svg">
                          <path d="M4 64C4 68.4 7.6 72 12 72H44C48.4 72 52 68.4 52 64V16H4V64ZM12 24H44V64H12V24ZM42 4L38 0H18L14 4H0V12H56V4H42Z" fill="reed" />
                        </svg>
                      </button>
                      <button onClick={this.handleEditTagData.bind(this, each)} title={'edit'}
                        style={{ '--fallback-background': '#F7F7F7' }}
                        className='p-0 btn btn-ghost btn-sm absolute top-[14%] right-[13%] btn-circle custom-button'>
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="#0074f1" className="bi bi-pencil-square" viewBox="0 0 16 16">
                          <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                          <path fillRule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5z" />
                        </svg>
                      </button>
                    </div>
                    <div className='flex flex-col'>
                      <span
                        className='text-black font-[500]'
                        style={{
                          width: '80%',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          letterSpacing: '0.02em'
                        }}>
                        {each.name} : <span title={each.metaDataName} className='text-gray-500'>{each.metaDataName}</span>
                      </span>

                      <span
                        className='text-black font-[500]'
                        style={{
                          width: '80%',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          letterSpacing: '0.02em'
                        }}>
                        Content : <span title={each.content} className='text-gray-500'>{each.content}</span>
                      </span>
                    </div>
                  </div>
                );
              }) : (
                <div className='flex items-center justify-center w-full h-full'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-box-seam" viewBox="0 0 16 16">
                    <path d="M8.186 1.113a.5.5 0 0 0-.372 0L1.846 3.5l2.404.961L10.404 2zm3.564 1.426L5.596 5 8 5.961 14.154 3.5zm3.25 1.7-6.5 2.6v7.922l6.5-2.6V4.24zM7.5 14.762V6.838L1 4.239v7.923zM7.443.184a1.5 1.5 0 0 1 1.114 0l7.129 2.852A.5.5 0 0 1 16 3.5v8.662a1 1 0 0 1-.629.928l-7.185 2.874a.5.5 0 0 1-.372 0L.63 13.09a1 1 0 0 1-.63-.928V3.5a.5.5 0 0 1 .314-.464z" />
                  </svg>
                  <span className='text-gray-500 ml-2'>Nothing to show</span>
                </div>
              )}
            </div>
          </div>
        );
    }
  }

  handleDeleteTags = (type, index) => {
    const { selectedPage } = this.state;
    const pagesPopulated = this.props.projectReducer.myProject.pagesPopulated;
    const filteredPage = find(pagesPopulated, { id: selectedPage.id });

    if (filteredPage) {
      const settings = { ...filteredPage.settings };
      settings[type] = [...settings[type]];
      settings[type].splice(index, 1);
      this.props.dispatch(sendAction('UPDATE_PAGE_SETTINGS', { settings, selectedPage }));
    }
  }

  onAddTagDataSubmit = (questionAnswers, cancelled) => {
    if (!cancelled) {
      const { dispatch, projectReducer } = this.props;
      const { selectedPage, selectedTab } = this.state;
      const pagesPopulated = projectReducer.myProject?.pagesPopulated;
      const filteredPage = pagesPopulated?.find(page => page.id === selectedPage.id);

      if (filteredPage) {
        const settings = { ...filteredPage.settings };
        const tagType = selectedTab === 'Meta Tags' ? 'metaTagData' : 'linkTagData';
        const updatedTagData = settings[tagType] ? [...settings[tagType]] : [];

        const findTagType = selectedTab === 'Meta Tags' ? { metaDataName: questionAnswers.metaDataName } : { rel: questionAnswers.rel };
        const duplicatePageError = filteredPage.settings && findIndex(filteredPage.settings[tagType], findTagType);
        if (duplicatePageError !== -1) {
          const duplicateTagDataError = selectedTab === 'Meta Tags' ? 'Meta Data Name Already Exists' : 'Rel Already Exists';
          this.setState({ duplicateTagDataError, editTagData: questionAnswers });
          return;
        }

        updatedTagData.push(questionAnswers);
        const updatedSettings = { ...settings, [tagType]: updatedTagData };

        dispatch(sendAction('UPDATE_PAGE_SETTINGS', { settings: updatedSettings, selectedPage }));
      }
    }
    this.setState({ showAddTagsDialog: false, duplicateTagDataError: null, editTagData: null, formId: null });
  };

  handleEditTagData = (editTagData) => {
    this.setState({ showAddTagsDialog: 'edit-tag', editTagData, formId: randstr(), selectedTagData: editTagData });
  }

  onEditTagDataSubmit = (questionAnswers, cancelled) => {
    if (!cancelled) {
      const { dispatch, projectReducer } = this.props;
      const { selectedPage, selectedTab, selectedTagData } = this.state;
      const pagesPopulated = projectReducer.myProject?.pagesPopulated;
      const filteredPage = pagesPopulated?.find(page => page.id === selectedPage.id);

      if (filteredPage) {
        let updatedSettings;
        if (selectedTab === 'Others') {
          updatedSettings = { ...filteredPage.settings, ...questionAnswers };
        } else {
          const settings = { ...filteredPage.settings };
          const tagType = selectedTab === 'Meta Tags' ? 'metaTagData' : 'linkTagData';
          const updatedTagData = settings[tagType] ? [...settings[tagType]] : [];

          const key = selectedTab === 'Meta Tags' ? 'metaDataName' : 'rel';

          if (questionAnswers[key] !== selectedTagData[key]) {
            const findTagType = selectedTab === 'Meta Tags' ? { metaDataName: questionAnswers.metaDataName } : { rel: questionAnswers.rel };
            const duplicatePageError = filteredPage.settings && findIndex(filteredPage.settings[tagType], findTagType);
            if (duplicatePageError !== -1) {
              const duplicateTagDataError = selectedTab === 'Meta Tags' ? 'Meta Data Name Already Exists' : 'Rel Already Exists';
              this.setState({ duplicateTagDataError, editTagData: questionAnswers });
              return;
            }
          }

          const findTagType = selectedTab === 'Meta Tags' ? { metaDataName: selectedTagData.metaDataName } : { rel: selectedTagData.rel };
          const tagIndex = findIndex(updatedTagData, findTagType);

          if (tagIndex !== -1) {
            updatedTagData[tagIndex] = questionAnswers;
          }
          updatedSettings = { ...settings, [tagType]: updatedTagData };
        }

        dispatch(sendAction('UPDATE_PAGE_SETTINGS', { settings: updatedSettings, selectedPage }));
      }
    }
    this.setState({ showAddTagsDialog: false, formId: null, duplicateTagDataError: null, selectedTagData: null, editTagData: null });
  };

  onCancelAddingTags = () => {
    this.setState({ showAddTagsDialog: false, editTagData: null });
  }

  handlePageOptions = (selectedPageOptionId, event) => {
    event.stopPropagation();
    this.setState(prevState => ({
      selectedPageOptionId: prevState.selectedPageOptionId === selectedPageOptionId ? null : selectedPageOptionId
    }));
  };

  handleClickOutside = (event) => {
    if (!event.target.closest(".page-options") && !event.target.closest(".custom-mui-dialog") && !this.state.showPageModal) {
      this.setState({ selectedPageOptionId: null });
    }
    if (this.state.showPageType) {
      this.setState({ showPageType: false });
    }
  };

  getDuplicatePageError = (questionAnswers, selectedPageId) => {
    const { projectReducer } = this.props;
    const pagesPopulated = projectReducer.myProject.pagesPopulated || [];
    const filteredOutSelectedPage = pagesPopulated.filter(each => each.id !== selectedPageId);

    const duplicatePageNameFound = filteredOutSelectedPage.find(page => page.name === questionAnswers.name);
    const duplicatePagePathFound = filteredOutSelectedPage.find(page => page.path === questionAnswers.path);
    if (duplicatePageNameFound || duplicatePagePathFound) {
      const errorMessages = [];
      if (duplicatePageNameFound) errorMessages.push('Page name already exists');
      if (duplicatePagePathFound) errorMessages.push(`Path already exists for Page: ${duplicatePagePathFound.name}`);
      return errorMessages.join(' and ');
    }
    return null;
  }

  onEditPageSubmit = (questionAnswers, cancelled) => {
    if (!cancelled) {
      const { dispatch } = this.props;
      const { selectedPage } = this.state;

      const trimmedQuestionAnswers = {
        ...questionAnswers,
        name: questionAnswers.name.trim(),
        path: questionAnswers.path.trim()
      };

      const payload = {
        editDetails: trimmedQuestionAnswers,
        page: selectedPage
      };

      const duplicatePageError = this.getDuplicatePageError(trimmedQuestionAnswers, selectedPage.id);

      if (duplicatePageError) {
        this.setState({ duplicatePageDetailsError: duplicatePageError, pageDetailsQuestionAnswers: { ...trimmedQuestionAnswers } });
        return;
      } else {
        this.handleChangePage(questionAnswers.name);
        dispatch(sendAction('EDIT_PAGE', payload));
      }
    }
    this.setState({ showPageModal: false, formId: null, selectedPage: null, duplicatePageDetailsError: null, pageDetailsQuestionAnswers: null });
  };

  convertToBase64 = (project) => {
    if (project) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = () => {
          resolve(reader.result);
        };
        reader.onerror = reject;
        reader.readAsText(project);
      });
    }
    return null;
  }

  handleFileChange = async (e) => {
    const page = e.target.files[0];
    const file = await this.convertToBase64(page);
    this.setState({ selectedFile: file, duplicatePageDetailsError: null });
  };

  onDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    const pages = this.props.pages && this.props.pages.filter((page) => page.path !== '_theme') || [];
    const themePage = this.props.pages && this.props.pages.filter((page) => page.path === '_theme') || [];
    const [removed] = pages.splice(result.source.index, 1);
    pages.splice(result.destination.index, 0, removed);
    const updatedPages = [...pages, ...themePage];
    this.props.dispatch(sendAction('UPDATE_PAGES', updatedPages));
  };

  importPage = () => {
    this.setState({ showPageModal: 'import-page', formId: randstr() });
    this.props.dispatch(sendAction('ELEMENT_SELECTED', null));
  }

  addPage = (pageType) => {
    const pages = this.props.pages && this.props.pages.filter((page) => page.path !== '_theme') || [];
    const themePage = this.props.pages && this.props.pages.filter((page) => page.path === '_theme') || [];
    const selectedScreenType = this.props.projectReducer?.myProject?.selectedScreenType;
    const pageSettings = {
      visibility: 'PUBLIC',
      pageType: pageType || PAGE_TYPE_WEB,
      title: ''
    };

    const pageName = getPageName(pageType);
    const { newPageName, newPagePath } = generateUniquePage(pages, pageName.name, pageName.path);

    const newPage = {
      id: randstr('page_'),
      screen: extendedBasicScreensInfo,
      name: newPageName,
      path: newPagePath,
      selectedScreenType: selectedScreenType || 'desktop',
      settings: pageSettings
    };
    const updatedPages = [...pages, newPage, ...themePage];
    this.props.dispatch(sendAction('UPDATE_PAGES', updatedPages));
  };

  onNewPageClick = () => {
    event.stopPropagation();
    const projectType = this.props.projectReducer?.myProject.projectType || 'WEB_APPLICATION';
    if (projectType === PROJECT_TYPE_PEGA_DX_COMPONENT) {
      this.setState({ showPageType: true });
    } else {
      this.addPage();
    }

  }

  onDeletePage = (page, e) => {
    e.stopPropagation();
    const { pages, dispatch } = this.props;
    const updatedPages = [...pages];
    const pageIndex = updatedPages.findIndex(each => each.id === page.id);
    if (pageIndex !== -1) updatedPages.splice(pageIndex, 1);
    dispatch(sendAction('UPDATE_PAGES', updatedPages));
    const newPageIndex = Math.min(Math.max(pageIndex - 1, 0), updatedPages.length - 1);
    const branchName = this.props.projectReducer?.selectedBranch?.name;
    this.props.router.push(`/projects/${this.props.projectReducer.myProject._id}/${updatedPages[newPageIndex]?.name}?branch=${branchName}`);
  }

  onSettingsPage = (page, e) => {
    e.stopPropagation();
    let settings = { ...page.settings };
    if (isEmpty(settings)) {
      settings = {
        visibility: 'PUBLIC'
      };
    }
    this.setState({ showSettings: true, selectedPage: page, settings: settings, selectedPageOptionId: null, selectedTab: tabList[0] });
  }

  onBackToPages = (e) => {
    e.stopPropagation();
    this.setState({ showSettings: false, selectedTab: null });
  }

  onVisibilityChange = (option) => {
    let settings = { ...this.state.settings };
    settings.visibility = option.value;
    this.setState({ settings: settings });
    const { dispatch } = this.props;
    let payload = { settings: settings, selectedPage: this.state.selectedPage };
    dispatch(sendAction('UPDATE_PAGE_SETTINGS', payload));
  }

  handlePageNameEdit = (page, e) => {
    e.stopPropagation();
    this.setState({ showPageModal: 'edit-page', formId: randstr(), selectedPage: page, pageDetailsQuestionAnswers: { name: page.name, path: page.path }, selectedPageOptionId: null });
    this.props.dispatch(sendAction('ELEMENT_SELECTED', null));
  };

  exportProject = (e) => {
    e.stopPropagation();
    this.setState({ selectedPageOptionId: null });
  }

  duplicatingPage = (page, e) => {
    e.stopPropagation();
    const { pages, dispatch } = this.props;
    const pageCopy = cloneDeep(page);

    pageCopy.id = randstr('page_');
    for (const screenKey of Object.keys(pageCopy.screen)) { // eslint-disable-line no-unused-vars
      if (screenKey !== 'id') {
        const screenValue = pageCopy.screen[screenKey];
        for (const item of screenValue.items) { // eslint-disable-line no-unused-vars
          item.id = randstr(`${item.data.id}_`);
          if (item.items) {
            for (const nestedItem of item.items) { // eslint-disable-line no-unused-vars
              nestedItem.id = randstr(`${nestedItem.data.id}_`);
              if (nestedItem.parent) {
                nestedItem.parent.id = item.id;
              }
            }
          }
        }
      }
    }
    delete pageCopy._id;
    delete pageCopy._project;
    delete pageCopy.created;
    delete pageCopy.updated;
    delete pageCopy.__v;

    let pageName = `${page.name}-copy`;

    const existingNames = pages.map(p => p.name);
    if (existingNames.includes(pageName)) {
      const copyCount = existingNames.filter(n => n.startsWith(pageName)).length + 1;
      pageName = `${pageName}(${copyCount})`;
    }

    pageCopy.name = pageName;
    pageCopy.path = pageName;
    const pageIndex = pages.findIndex(p => p.id === page.id);
    const updatedPages = [...pages];
    updatedPages.splice(pageIndex + 1, 0, pageCopy);
    dispatch(sendAction('UPDATE_PAGES', updatedPages));
    this.setState({ selectedPageOptionId: null });
  }

  onImportPageSubmit = (questionAnswers, cancelled) => {
    if (!cancelled) {
      const { pages, dispatch } = this.props;
      const { selectedFile } = this.state;

      const trimmedQuestionAnswers = {
        ...questionAnswers,
        name: questionAnswers.name.trim(),
        path: questionAnswers.path.trim()
      };
      const updatedQuestionAnswers = { ...trimmedQuestionAnswers };

      if (!selectedFile) {
        this.setState({ duplicatePageDetailsError: 'please select file(.json)', pageDetailsQuestionAnswers: updatedQuestionAnswers });
        return;
      }

      const duplicatePageError = this.getDuplicatePageError(updatedQuestionAnswers);

      if (duplicatePageError) {
        this.setState({ duplicatePageDetailsError: duplicatePageError, pageDetailsQuestionAnswers: updatedQuestionAnswers });
        return;
      }
      updatedQuestionAnswers.path = updatedQuestionAnswers.path || updatedQuestionAnswers.name;
      const restoredPage = {
        ...JSON.parse(selectedFile),
        ...updatedQuestionAnswers,
        id: randstr('page_')
      };
      const updatedPages = [...pages, restoredPage];
      dispatch(sendAction('UPDATE_PAGES', updatedPages));
    }
    this.setState({ showPageModal: false, formId: null, selectedPage: null, duplicatePageDetailsError: null, pageDetailsQuestionAnswers: null, selectedFile: null });
    if (this.fileInputRef.current) {
      this.fileInputRef.current.value = '';
    }
  }
}

export default withRouter(connectToStores(AddPages));
