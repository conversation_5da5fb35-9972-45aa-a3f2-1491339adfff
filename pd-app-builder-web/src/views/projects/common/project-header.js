import React, { Component } from 'react';
import { withRouter } from 'next/router';
import Link from 'next/link';
import { reduxUtil, genericAction } from '@limegroup/lime-redux-util';
import { LOGOUT } from '@astrakraft/user-plugin';
import {
  SAVE_PROJECT,
  PUBLISH_PEGA_COMPONENT
} from '@astrakraft/project-plugin';
import { getElements } from '../../../themes';
import { find } from 'lodash';
import { projectConstants } from '@astrakraft/core-lib';
import Dialog from '@mui/material/Dialog';
import Slide from '@mui/material/Slide';
import EditableForm from '../../../components/EditableForm';
import newBranchSchema from '../schemas/add-branch.json';
import mergeBranchSchema from '../schemas/merge-branch.json';
import Tooltip from '@mui/material/Tooltip';
import Switch from '@mui/material/Switch';
import { Select, MenuItem, OutlinedInput } from '@mui/material';
import IconComponent from '../../../utils/icon-util';
import Snackbar from '@mui/material/Snackbar';
import MuiAlert from '@mui/material/Alert';
import {
  Modal,
  Box,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Checkbox,
  Typography,
  Button
} from '@mui/material';


const { PROJECT_TYPE_PEGA_DX_COMPONENT } = projectConstants;

const { connectToStores } = reduxUtil;
const { sendActionAsync, sendAction } = genericAction;

const screenTypes = [
  {
    deviceType: 'mobile',
    icon: (
      <svg
        width="22"
        height="20"
        viewBox="0 0 12 20"
        fill="#0074F1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M10.1667 0.841585L1.83334 0.833252C0.916669 0.833252 0.175003 1.58325 0.175003 2.49992V17.4999C0.175003 18.4166 0.916669 19.1666 1.83334 19.1666H10.1667C11.0833 19.1666 11.8333 18.4166 11.8333 17.4999V2.49992C11.8333 1.58325 11.0833 0.841585 10.1667 0.841585ZM10.1667 15.8333H1.83334V4.16658H10.1667V15.8333ZM6.66667 11.0166V12.4749L9.33334 9.98325L6.66667 7.49992V8.91658C4.075 9.27492 3.04167 11.0499 2.66667 12.8333C3.59167 11.5833 4.81667 11.0166 6.66667 11.0166Z" />
      </svg>
    )
  },
  {
    deviceType: 'mobileLandscape',
    icon: (
      <svg
        width="22"
        height="20"
        viewBox="0 0 20 12"
        fill="#0074F1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M17.4999 10.1667L17.4999 1.83334C17.4999 0.916669 16.7499 0.175003 15.8333 0.175003L2.49992 0.175003C1.58325 0.175003 0.841585 0.916669 0.841585 1.83334L0.833252 10.1667C0.833252 11.0833 1.58325 11.8333 2.49992 11.8333L15.8333 11.8333C16.7499 11.8333 17.4999 11.0833 17.4999 10.1667ZM15.8333 10.1667L4.16658 10.1667L4.16658 1.83334L15.8333 1.83334L15.8333 10.1667ZM11.0166 6.66667L12.4749 6.66667L9.98325 9.33334L7.49992 6.66667L8.91658 6.66667C9.27492 4.075 11.0499 3.04167 12.8333 2.66667C11.5833 3.59167 11.0166 4.81667 11.0166 6.66667Z" />
      </svg>
    )
  },
  {
    deviceType: 'tablet',
    icon: (
      <svg
        width="22"
        height="20"
        viewBox="0 0 16 20"
        fill="#0074F1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M13.75 0H2.08333C0.933333 0 0 0.933333 0 2.08333V17.9167C0 19.0667 0.933333 20 2.08333 20H13.75C14.9 20 15.8333 19.0667 15.8333 17.9167V2.08333C15.8333 0.933333 14.9 0 13.75 0ZM7.91667 19.1667C7.225 19.1667 6.66667 18.6083 6.66667 17.9167C6.66667 17.225 7.225 16.6667 7.91667 16.6667C8.60833 16.6667 9.16667 17.225 9.16667 17.9167C9.16667 18.6083 8.60833 19.1667 7.91667 19.1667ZM14.1667 15.8333H1.66667V2.5H14.1667V15.8333Z" />
      </svg>
    )
  },
  {
    deviceType: 'desktop',
    icon: (
      <svg
        width="22px"
        height="20px"
        viewBox="0 0 22 19"
        fill="#0074F1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M19 0H2.11111C0.95 0 0 0.95 0 2.11111V13.7222C0 14.8833 0.95 15.8333 2.11111 15.8333H5.27778L4.22222 16.8889V19H16.8889V16.8889L15.8333 15.8333H19C20.1611 15.8333 21.1111 14.8833 21.1111 13.7222V2.11111C21.1111 0.95 20.1611 0 19 0ZM19 13.7222H2.11111V2.11111H19V13.7222Z" />
      </svg>
    )
  }
];

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const SUCCESS_STATUSES = ['Merge Conflicts resolved successfully', 'Conflicts resolved successfully', 'Already Up to Date'];

const buttonStyle = {
  width: '135px',
  padding: '0px',
  borderRadius: '80px',
  height: '40px',
  fontSize: '14px',
  fontWeight: '700',
  lineHeight: '16.94px',
  letterSpacing: '0.1em',
  textAlign: 'center',
  background: 'linear-gradient(95.1deg, #0074F1 10.43%, #DE00F1 90.96%)',
  color: '#FFFFFF',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
};

class ProjectHeader extends Component {
  state = {
    selectedItems: [],
    dropdowns: {
      projectsDropdown: false,
      pagesDropdown: false,
      branchesDropdown: false,
      menuDropdown: false,
      userDropdown: false
    },
    saveDropdownOpen: false,
    isBranchDialogOpen: false,
    branchCreatedError: false,
    showTooltip: false,
    mergeBranchDialog: false,
    mergeTriggered: false,
    toBranchName: 'master',
    mergeValidationStatus: '',
    notes: '',
    isConflictDialogOpen: false,
    isPreviewing: false,
    previewSuccess: false
  };

  componentDidMount() {
    document.addEventListener('click', this.handleOutsideClick);
    document.addEventListener('keydown', this.handleKeyDown);
  }

  componentWillUnmount() {
    document.removeEventListener('click', this.handleOutsideClick);
    document.removeEventListener('keydown', this.handleKeyDown);
  }
  componentDidUpdate(prevProps) {
    // Handle save errors
    if (
      this.props.projectReducer.saveProjectError &&
      this.props.projectReducer.saveProjectError !==
      prevProps.projectReducer.saveProjectError
    ) {
      // handle 
    }

    if (
      this.props.projectReducer.saveProject &&
      this.props.projectReducer.saveProject !==
      prevProps.projectReducer.saveProject
    ) {
      window.location.reload(); // Added back to force full page refresh on save
    }

    if (
      this.props.projectReducer.newBranchCreated &&
      this.props.projectReducer.newBranchCreated !==
      prevProps.projectReducer.newBranchCreated
    ) {
      this.props.dispatch(
        sendActionAsync('LIST_BRANCHES', {
          _project: this.props.projectReducer.myProject._id
        })
      );
      this.setState({ isBranchDialogOpen: false });
      this.refs['create-branch-dialog'].resetForm();
      this.props.router.push({
        pathname: this.props.router.pathname,
        query: {
          ...this.props.router.query,
          branch: this.props.projectReducer.newBranchCreated.name
        }
      });
    }

    if (
      this.props.projectReducer.showCreateBranchDialog &&
      this.props.projectReducer.showCreateBranchDialog !==
      prevProps.projectReducer?.showCreateBranchDialog
    ) {
      this.setState({ isBranchDialogOpen: true });
      this.refs['create-branch-dialog'].resetForm();
    }

    if (
      this.props.projectReducer.readyToMerge !== null &&
      this.props.projectReducer.readyToMerge !==
      prevProps.projectReducer.readyToMerge
    ) {
      if (this.props.projectReducer.readyToMerge === false) {
        this.setState({ mergeValidationStatus: 'Branch Ahead' });
      } else if (this.props.projectReducer.readyToMerge) {
        this.pushToMaster();
      }
    }

    if (
      this.props.projectReducer.mergedStatus !==
      prevProps.projectReducer?.mergedStatus
    ) {
      this.setState({
        mergeBranchDialog: false,
        mergeStatus: this.props.projectReducer.mergedStatus,
        isMergeStatusOpen: true,
        mergeValidationStatus: null,
        isConflictDialogOpen: false,
        conflicts: [],
        selectedConflict: null
      });
      this.refs['merge-branch'].resetForm();
      if (this.state.mergeTriggered) {
        this.props.dispatch(
          sendActionAsync('LIST_BRANCHES', {
            _project: this.props.projectReducer.myProject._id
          })
        );
        this.handleChangeProject(
          this.props.projectReducer.myProject._id,
          this.state.toBranchName
        );
      }
    }
    if (
      this.props.projectReducer.conflicts &&
      this.props.projectReducer.conflicts !==
      prevProps.projectReducer?.conflicts
    ) {
      this.setState({
        isMergeStatusOpen: true,
        mergeStatus: 'Merge conflicts detected',
        isConflictDialogOpen: true,
        conflicts: this.props.projectReducer.conflicts,
        selectedConflict: this.props.projectReducer.conflicts[0]
      });
    }
  }

  handleToggle = (e) => {
    this.props.toggleLiveReplicate && this.props.toggleLiveReplicate(e); //eslint-disable-line no-unused-expressions
  };

  // handle push and pull from master

  pushToMaster = () => {
    const {
      projectReducer: { selectedBranch, projectBranches },
      dispatch
    } = this.props;
    const masterBranch = find(projectBranches, { branchType: 'SYSTEM' });
    if (masterBranch) {
      const payload = {
        _fromBranch: selectedBranch._id,
        _toBranch: masterBranch._id,
        isPushRequest: true
      };
      dispatch(sendActionAsync('MERGE_BRANCH', payload));
      this.setState({ mergeTriggered: true });
    }
  };

  pullFromMaster = () => {
    const {
      projectReducer: { selectedBranch, projectBranches },
      dispatch
    } = this.props;
    const masterBranch = find(projectBranches, { branchType: 'SYSTEM' });
    if (masterBranch) {
      const payload = {
        _fromBranch: selectedBranch._id,
        _toBranch: masterBranch._id,
        isPullRequest: true
      };
      dispatch(sendActionAsync('MERGE_BRANCH', payload));
    }
  };

  handleKeyDown = (event) => {
    if (event.keyCode === 13) { //keycode for Enter
      event.preventDefault();
      event.stopPropagation();
    }
  };

  // handle push and pull from custom branch

  pullFromBranch = () => {
    const {
      projectReducer: { selectedBranch },
      dispatch
    } = this.props;
    const payload = {
      _fromBranch: selectedBranch._id,
      _toBranch: selectedBranch._fromBranch,
      isPullRequest: true
    };
    dispatch(sendActionAsync('MERGE_BRANCH', payload));
  };

  toggleSaveDropdown = (e) => {
    e.stopPropagation();
    this.setState((prevState) => ({
      saveDropdownOpen: !prevState.saveDropdownOpen
    }));
  };

  renderBranchActionButton = () => {
    const { selectedBranch, myProject } = this.props.projectReducer;

    if (!selectedBranch) {
      return (
        <button className="btn btn-sm btn-outline" style={buttonStyle}>
          <span className="loading loading-spinner"></span>
        </button>
      );
    }

    const isSystemBranch = selectedBranch.branchType === 'SYSTEM';
    const canEditMasterBranch = myProject.settings?.enableMasterEdit ?? true;
    // Reverted button content logic
    const buttonContent = isSystemBranch && !canEditMasterBranch ? 'EDIT' : 'SAVE';

    const handleClick =
      isSystemBranch && !canEditMasterBranch
        ? this.toggleBranchDialog.bind(this)
        : this.handleSaveButton.bind(this, null);

    return (
      <div className="relative flex items-center">
        <button
          onClick={handleClick}
          className="btn btn-sm btn-outline"
          style={buttonStyle} // Always use original style
        >
          <span className="w-[90px] mr-[-6px]">{buttonContent}</span>
          {!isSystemBranch && (
            <div
              onClick={this.toggleSaveDropdown.bind(this)}
              className="w-[40px] h-[100%] flex items-center"
              style={{
                borderTopRightRadius: '80px',
                borderBottomRightRadius: '80px'
              }}
            >
              <div className="border-l-2">
                <svg
                  className={`w-6 h-6 ml-1.5 shrink-0 transition duration-300 ${this.state.saveDropdownOpen && '-rotate-180'
                    }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          )}
        </button>
        {this.state.saveDropdownOpen && !isSystemBranch && (
          <div className="absolute right-0 top-[86%] mt-2 w-48 bg-white rounded-md shadow-lg z-10 saveDropdown p-[3px]">
            <button
              className={`btn btn-sm border-none btn-primary bg-[transparent] hover:bg-[#0074F110] w-[100%] my-[2px] flex justify-start`}
              onClick={this.toggleMergeBranchDialog.bind(this)}
              style={{
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'none',
                color: '#343A40'
              }}
            >
              Push to Master
            </button>
            <button
              className={`btn btn-sm border-none btn-primary bg-[transparent] hover:bg-[#0074F110] w-[100%] my-[2px] flex justify-start`}
              onClick={this.pullFromMaster.bind(this)}
              style={{
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'none',
                color: '#343A40'
              }}
            >
              Pull from Master
            </button>
          </div>
        )}
      </div>
    );
  };

  handleBranchChange = (event) => {
    const selectedBranch = event.target.value;
    this.setState({ toBranchName: selectedBranch });
  };

  renderBranchDropdown = () => {
    const { projectBranches } = this.props.projectReducer;
    const { selectedBranch } = this.props.projectReducer;

    const filteredBranches = projectBranches?.filter(
      (branch) => branch.name !== selectedBranch?.name
    );

    return (
      <div className="flex items-center">
        <div className="flex items-center bg-[#CCCCCC40] rounded-[4px] px-[14px] py-[10px] text-[14px] mr-[10px] w-[36%]">
          <svg width="24" height="24" viewBox="0 0 24 24" role="presentation">
            <path
              fill="#0074f1"
              fill-rule="evenodd"
              d="M19 11c0 1.3-.8 2.4-2 2.8V15c0 2.2-1.8 4-4 4h-2.2c-.4 1.2-1.5 2-2.8 2-1.7 0-3-1.3-3-3 0-1.3.8-2.4 2-2.8V8.8C5.9 8.4 5 7.3 5 6c0-1.7 1.3-3 3-3s3 1.3 3 3c0 1.3-.8 2.4-2.1 2.8v6.4c.9.3 1.6.9 1.9 1.8H13c1.1 0 2-.9 2-2v-1.2c-1.2-.4-2-1.5-2-2.8 0-1.7 1.3-3 3-3s3 1.3 3 3M8 5c-.5 0-1 .4-1 1s.4 1 1 1 1-.4 1-1-.4-1-1-1m8 7c.6 0 1-.4 1-1s-.4-1-1-1-1 .4-1 1 .4 1 1 1m-8 7c.6 0 1-.4 1-1s-.4-1-1-1-1 .4-1 1 .4 1 1 1"
            ></path>
          </svg>
          <span className="branch-name ml-1 text-[#0074f1] font-[500]">
            {selectedBranch?.name || 'Select Branch'}
          </span>
        </div>
        <svg width="24" height="24" viewBox="0 0 24 24" role="presentation">
          <path
            fill="currentcolor"
            fill-rule="evenodd"
            d="M11.793 5.793a1 1 0 0 0 0 1.414L15.586 11H6a1 1 0 0 0 0 2h9.586l-3.793 3.793a1 1 0 0 0 0 1.414c.39.39 1.024.39 1.415 0l5.499-5.5a1 1 0 0 0 .293-.679v-.057a1 1 0 0 0-.293-.678l-5.499-5.5a1 1 0 0 0-1.415 0"
          ></path>
        </svg>
        <Select
          value={this.state.toBranchName}
          input={<OutlinedInput />}
          onChange={this.handleBranchChange}
          //eslint-disable-next-line react/jsx-no-bind
          IconComponent={(props) => (
            <IconComponent iconName="KeyboardArrowDown" {...props} style={{
              color: '#343A40',
              fontSize: '20px'
            }} />
          )}
          inputProps={{ 'aria-label': 'Select box' }}
          sx={{
            height: '45px',
            marginLeft: '10px',
            width: '45%',
            paddingRight: '0px',
            paddingLeft: '0px',
            backgroundColor: '#0074f120',
            '.MuiSelect-select': {
              display: 'flex',
              alignItems: 'center',
              paddingRight: '0px'
            },
            '.MuiOutlinedInput-notchedOutline': {
              borderWidth: '0px'
            },
            '&:hover': {
              backgroundColor: '#0074f140'
            }
          }}
          //eslint-disable-next-line react/jsx-no-bind
          renderValue={(selected) => (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                color: '#0074f1'
              }}
            >
              <svg
                width="24"
                height="24"
                className="mr-1"
                viewBox="0 0 24 24"
                role="presentation"
              >
                <path
                  fill="#0074f1"
                  fill-rule="evenodd"
                  d="M19 11c0 1.3-.8 2.4-2 2.8V15c0 2.2-1.8 4-4 4h-2.2c-.4 1.2-1.5 2-2.8 2-1.7 0-3-1.3-3-3 0-1.3.8-2.4 2-2.8V8.8C5.9 8.4 5 7.3 5 6c0-1.7 1.3-3 3-3s3 1.3 3 3c0 1.3-.8 2.4-2.1 2.8v6.4c.9.3 1.6.9 1.9 1.8H13c1.1 0 2-.9 2-2v-1.2c-1.2-.4-2-1.5-2-2.8 0-1.7 1.3-3 3-3s3 1.3 3 3M8 5c-.5 0-1 .4-1 1s.4 1 1 1 1-.4 1-1-.4-1-1-1m8 7c.6 0 1-.4 1-1s-.4-1-1-1-1 .4-1 1 .4 1 1 1m-8 7c.6 0 1-.4 1-1s-.4-1-1-1-1 .4-1 1 .4 1 1 1"
                ></path>
              </svg>
              {selected}
            </div>
          )}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
                overflowY: 'auto',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                marginTop: '4px'
              }
            }
          }}
        >
          {filteredBranches?.map((option) => (
            <MenuItem
              key={option.name}
              value={option.name}
              sx={{
                paddingLeft: '12px', // Reserve space for the border
                boxSizing: 'border-box', // Ensure padding and border are accounted for
                borderLeft:
                  this.state.toBranchName === option.name
                    ? '3px solid #0074f1'
                    : '3px solid transparent', // Transparent border when not selected
                color: this.state.toBranchName === option.name ? '#0074f1' : '',
                '&:hover': {
                  borderLeft: '3px solid #0074f1',
                  color: '#0074f1'
                }
              }}
            >
              {option.name}
            </MenuItem>
          ))}
        </Select>
      </div>
    );
  };

  handleClose = () => {
    this.setState({ isMergeStatusOpen: false });
  };

  render() {
    const { dropdowns, branchCreatedError, isMergeStatusOpen, mergeStatus } =
      this.state;
    const projects = this.props.projectReducer.projects || [];
    const myProject = this.props.projectReducer.myProject || {};
    const selectedPage =
      this.props.view === 'dialogs'
        ? this.props.projectReducer.selectedDialog
        : this.props.projectReducer.selectedPage;
    const selectedBranch = this.props.projectReducer.selectedBranch;
    const loggedInUser = this.props.userReducer.loggedInUser || {};
    let selectedScreenType = this.props.projectReducer.myProject?.selectedScreenType;
    // If mobile app and no screen type set, default to mobile and dispatch only once
    if (myProject.projectType === 'PROJECT_TYPE_APP') {
      selectedScreenType = 'mobile';
      if (this.props.projectReducer.myProject?.selectedScreenType !== 'mobile') {
        this.props.dispatch && this.props.dispatch(sendAction('CHANGE_SCREEN_TYPE', 'mobile'));
      }
    }
    const isSelectedThemePage = selectedPage && selectedPage.name === '_Theme';
    const { pages = [] } = this.props;
    const branches = this.props.projectReducer.projectBranches;

    return (
      <div
        style={{
          height: '8vh',
          backgroundColor: '#F7F7F7',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          width: '100%',
          alignItems: 'center',
          minHeight: '46px'
        }}
      >
        {this.state.showModal && (
          <Modal open={true} onClose={() => this.setState({ showModal: false })}>
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '50%',
                bgcolor: 'background.paper',
                boxShadow: 24,
                p: 0,
                maxHeight: '80vh',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              {/* Header */}
              <Box sx={{ p: 2 }}>
                <Typography variant="h6" sx={{ color: '#000' }}>
                  Component List
                </Typography>
              </Box>

              {/* Table Scroll Area */}
              <Box sx={{ overflowY: 'auto', px: 2, flex: 1 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ backgroundColor: '#fff', position: 'sticky', top: 0, zIndex: 1 }}>
                        Select
                      </TableCell>
                      <TableCell sx={{ backgroundColor: '#fff', position: 'sticky', top: 0, zIndex: 1 }}>
                        Component
                      </TableCell>
                      <TableCell sx={{ backgroundColor: '#fff', position: 'sticky', top: 0, zIndex: 1 }}>
                        Live Editing
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {this.state.flattenedItems.map((item) => {
                      const isMissing = [];
                      if (!item.key) isMissing.push('pegaComponentKey');
                      if (!item.type) isMissing.push('pegaType');
                      if (!item.subtype) isMissing.push('pegaSubtype');
                      const tooltipText = isMissing.length ? `Missing: ${isMissing.join(', ')}` : '';
                      const disabled = !item.isValid;

                      return (
                        <TableRow key={item.key}>
                          <TableCell>
                            <Checkbox
                              disabled={disabled}
                              checked={this.state.selectedItems[item.key]}
                              onChange={(e) => {
                                this.setState((prev) => ({
                                  selectedItems: {
                                    ...prev.selectedItems,
                                    [item.key]: e.target.checked
                                  }
                                }));
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {!item.isValid && (
                                <Tooltip title={tooltipText}>
                                  <IconComponent iconName="WarningAmber" sx={{ color: 'red' }} />
                                </Tooltip>
                              )}
                              {item.key || item.compId}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Switch
                              disabled={disabled}
                              checked={this.state.liveEditingStates[item.key]}
                              onChange={(e) => {
                                this.setState((prev) => ({
                                  liveEditingStates: {
                                    ...prev.liveEditingStates,
                                    [item.key]: e.target.checked
                                  }
                                }));
                              }}
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </Box>
              <Box
                sx={{
                  p: 2,
                  borderTop: '1px solid #ddd',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  backgroundColor: '#fff'
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  disabled={
                    !Object.entries(this.state.selectedItems).some(([key, value]) => value === true)
                  }
                  onClick={this.publishSelectedComponents}
                >
                  Publish
                </Button>
              </Box>
            </Box>
          </Modal>
        )}

        <Snackbar
          open={isMergeStatusOpen}
          autoHideDuration={3000}
          onClose={this.handleClose}
        >
          <MuiAlert
            variant="filled"
            onClose={this.handleClose}
            severity={SUCCESS_STATUSES.includes(mergeStatus) ? 'success' : 'error'}
          >
            {mergeStatus}
          </MuiAlert>
        </Snackbar>
        <div className="flex align-items-center h-full">
          <Link href="/projects/">
            <div className="m-[12px] ml-[35px] 2xl:w-[150px] ">
              <svg
                width="131"
                height="26"
                viewBox="0 0 131 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0 4.15256C0 1.85916 1.85916 0 4.15256 0H4.68599C5.23117 0 5.75447 0.214422 6.1429 0.596964L16.9563 11.2465L19.014 13.3849C21.5525 16.023 19.6829 20.4168 16.0217 20.4168H4.15256C1.85916 20.4168 0 18.5576 0 16.2642V4.15256Z"
                  fill="#FF0000"
                  fillOpacity="0.5"
                />
                <path
                  d="M23.3582 21.1092C23.3582 23.4026 21.499 25.2617 19.2056 25.2617H18.6722C18.127 25.2617 17.6037 25.0473 17.2153 24.6648L6.40187 14.0152L4.34418 11.8768C1.80561 9.2387 3.67524 4.84496 7.3364 4.84496H19.2056C21.499 4.84496 23.3582 6.70413 23.3582 8.99752V21.1092Z"
                  fill="#4200FF"
                  fillOpacity="0.5"
                />
                <path
                  d="M129.858 20.5463C129.477 20.5463 129.117 20.4749 128.777 20.3321C128.444 20.1894 128.152 19.9923 127.9 19.7407C127.656 19.4892 127.458 19.1969 127.309 18.8638C127.166 18.5307 127.095 18.1738 127.095 17.7932V11.2979H125.219V10.1049H127.095V7.59651L128.349 7.39258V10.1049H131V11.2979H128.349V17.8544C128.349 18.0651 128.386 18.2622 128.461 18.4458C128.543 18.6225 128.651 18.7789 128.787 18.9148C128.923 19.0508 129.083 19.1595 129.267 19.2411C129.45 19.3159 129.647 19.3533 129.858 19.3533H131V20.5463H129.858Z"
                  fill="#5729D9"
                />
                <path
                  d="M121.56 7.11684C121.349 7.11684 121.152 7.15762 120.968 7.2392C120.792 7.31397 120.635 7.41934 120.499 7.55529C120.363 7.69125 120.255 7.851 120.173 8.03454C120.098 8.21808 120.061 8.41521 120.061 8.62594V10.1045H122.498V11.2975H120.061V20.5458H118.807V11.2975H116.92V10.1045H118.807V8.68712C118.807 8.30645 118.878 7.94956 119.021 7.61647C119.164 7.27659 119.361 6.98088 119.612 6.72936C119.864 6.47785 120.156 6.28071 120.489 6.13796C120.822 5.9952 121.179 5.92383 121.56 5.92383H122.712V7.11684H121.56Z"
                  fill="#5729D9"
                />
                <path
                  d="M112.956 20.5457L112.263 19.5056C111.977 19.8795 111.62 20.182 111.192 20.4131C110.764 20.6375 110.295 20.7496 109.785 20.7496H109.469C109.047 20.7496 108.65 20.6715 108.276 20.5151C107.902 20.352 107.576 20.131 107.297 19.8523C107.018 19.5736 106.797 19.2473 106.634 18.8735C106.478 18.4996 106.4 18.1019 106.4 17.6804V17.5785C106.4 17.157 106.478 16.7593 106.634 16.3855C106.797 16.0116 107.018 15.6853 107.297 15.4066C107.576 15.1279 107.902 14.9104 108.276 14.754C108.65 14.5909 109.047 14.5093 109.469 14.5093H112.12V12.8982C112.12 12.6467 112.072 12.4122 111.977 12.1946C111.882 11.9703 111.753 11.7766 111.59 11.6134C111.426 11.4503 111.233 11.3211 111.008 11.226C110.791 11.1308 110.556 11.0832 110.305 11.0832H109.571C109.319 11.0832 109.081 11.1308 108.857 11.226C108.639 11.3211 108.449 11.4503 108.286 11.6134C108.123 11.7766 107.994 11.9703 107.898 12.1946C107.803 12.4122 107.756 12.6467 107.756 12.8982V13.2755L106.502 13.0716V12.9696C106.502 12.5481 106.58 12.1505 106.736 11.7766C106.899 11.4027 107.12 11.0764 107.399 10.7977C107.678 10.519 108.004 10.3015 108.378 10.1451C108.752 9.98196 109.149 9.90039 109.571 9.90039H110.305C110.726 9.90039 111.124 9.98196 111.498 10.1451C111.872 10.3015 112.198 10.519 112.477 10.7977C112.755 11.0764 112.973 11.4027 113.129 11.7766C113.292 12.1505 113.374 12.5481 113.374 12.9696V20.5457H112.956ZM112.12 15.4882H109.469C109.217 15.4882 108.979 15.5357 108.755 15.6309C108.537 15.7261 108.347 15.8586 108.184 16.0286C108.021 16.1917 107.892 16.3855 107.796 16.6098C107.701 16.8273 107.654 17.0618 107.654 17.3134V17.7518C107.654 18.0033 107.701 18.2413 107.796 18.4656C107.892 18.6831 108.021 18.8735 108.184 19.0366C108.347 19.1997 108.537 19.3289 108.755 19.4241C108.979 19.5192 109.217 19.5668 109.469 19.5668H110.305C110.556 19.5668 110.791 19.5192 111.008 19.4241C111.233 19.3289 111.426 19.1997 111.59 19.0366C111.753 18.8735 111.882 18.6831 111.977 18.4656C112.072 18.2413 112.12 18.0033 112.12 17.7518V15.4882Z"
                  fill="#5729D9"
                />
                <path
                  d="M101.385 11.2973C101.133 11.2973 100.895 11.3449 100.671 11.4401C100.454 11.5353 100.263 11.6644 100.1 11.8276C99.9369 11.9907 99.8077 12.1844 99.7126 12.4088C99.6174 12.6263 99.5698 12.8608 99.5698 13.1123V20.5457H98.3156V10.1043H98.7337L99.4169 11.1648C99.7024 10.7773 100.059 10.4714 100.488 10.2471C100.923 10.016 101.395 9.90039 101.905 9.90039H102.639V11.2973H101.385Z"
                  fill="#5729D9"
                />
                <path
                  d="M92.9134 20.5458L87.4072 13.2348L92.9134 5.92383H94.5449L88.9265 13.2348L94.5449 20.5458H92.9134ZM85.7758 20.5458V5.92383H87.0911V20.5458H85.7758Z"
                  fill="#5729D9"
                />
                <path
                  d="M80.343 20.5457L79.6496 19.5056C79.3641 19.8795 79.0072 20.182 78.5789 20.4131C78.1507 20.6375 77.6816 20.7496 77.1718 20.7496H76.8557C76.4342 20.7496 76.0366 20.6715 75.6627 20.5151C75.2888 20.352 74.9625 20.131 74.6838 19.8523C74.4051 19.5736 74.1842 19.2473 74.021 18.8735C73.8647 18.4996 73.7865 18.1019 73.7865 17.6804V17.5785C73.7865 17.157 73.8647 16.7593 74.021 16.3855C74.1842 16.0116 74.4051 15.6853 74.6838 15.4066C74.9625 15.1279 75.2888 14.9104 75.6627 14.754C76.0366 14.5909 76.4342 14.5093 76.8557 14.5093H79.5068V12.8982C79.5068 12.6467 79.4592 12.4122 79.3641 12.1946C79.2689 11.9703 79.1397 11.7766 78.9766 11.6134C78.8135 11.4503 78.6197 11.3211 78.3954 11.226C78.1779 11.1308 77.9433 11.0832 77.6918 11.0832H76.9577C76.7061 11.0832 76.4682 11.1308 76.2439 11.226C76.0264 11.3211 75.836 11.4503 75.6729 11.6134C75.5097 11.7766 75.3806 11.9703 75.2854 12.1946C75.1902 12.4122 75.1427 12.6467 75.1427 12.8982V13.2755L73.8885 13.0716V12.9696C73.8885 12.5481 73.9666 12.1505 74.123 11.7766C74.2861 11.4027 74.5071 11.0764 74.7858 10.7977C75.0645 10.519 75.3908 10.3015 75.7647 10.1451C76.1385 9.98196 76.5362 9.90039 76.9577 9.90039H77.6918C78.1133 9.90039 78.511 9.98196 78.8848 10.1451C79.2587 10.3015 79.585 10.519 79.8637 10.7977C80.1424 11.0764 80.3599 11.4027 80.5163 11.7766C80.6794 12.1505 80.761 12.5481 80.761 12.9696V20.5457H80.343ZM79.5068 15.4882H76.8557C76.6042 15.4882 76.3663 15.5357 76.1419 15.6309C75.9244 15.7261 75.7341 15.8586 75.5709 16.0286C75.4078 16.1917 75.2786 16.3855 75.1834 16.6098C75.0883 16.8273 75.0407 17.0618 75.0407 17.3134V17.7518C75.0407 18.0033 75.0883 18.2413 75.1834 18.4656C75.2786 18.6831 75.4078 18.8735 75.5709 19.0366C75.7341 19.1997 75.9244 19.3289 76.1419 19.4241C76.3663 19.5192 76.6042 19.5668 76.8557 19.5668H77.6918C77.9433 19.5668 78.1779 19.5192 78.3954 19.4241C78.6197 19.3289 78.8135 19.1997 78.9766 19.0366C79.1397 18.8735 79.2689 18.6831 79.3641 18.4656C79.4592 18.2413 79.5068 18.0033 79.5068 17.7518V15.4882Z"
                  fill="black"
                />
                <path
                  d="M68.7718 11.2973C68.5203 11.2973 68.2823 11.3449 68.058 11.4401C67.8405 11.5353 67.6501 11.6644 67.487 11.8276C67.3238 11.9907 67.1947 12.1844 67.0995 12.4088C67.0043 12.6263 66.9568 12.8608 66.9568 13.1123V20.5457H65.7026V10.1043H66.1206L66.8038 11.1648C67.0893 10.7773 67.4462 10.4714 67.8745 10.2471C68.3095 10.016 68.782 9.90039 69.2918 9.90039H70.026V11.2973H68.7718Z"
                  fill="black"
                />
                <path
                  d="M60.1577 20.5463C59.777 20.5463 59.4167 20.4749 59.0768 20.3321C58.7437 20.1894 58.4514 19.9923 58.1999 19.7407C57.9552 19.4892 57.7581 19.1969 57.6085 18.8638C57.4658 18.5307 57.3944 18.1738 57.3944 17.7932V11.2979H55.5182V10.1049H57.3944V7.59651L58.6486 7.39258V10.1049H61.2997V11.2979H58.6486V17.8544C58.6486 18.0651 58.686 18.2622 58.7607 18.4458C58.8423 18.6225 58.9511 18.7789 59.087 18.9148C59.223 19.0508 59.3827 19.1595 59.5663 19.2411C59.7498 19.3159 59.9469 19.3533 60.1577 19.3533H61.2997V20.5463H60.1577Z"
                  fill="black"
                />
                <path
                  d="M51.8596 17.8946C51.8596 18.316 51.7746 18.7035 51.6047 19.057C51.4347 19.4037 51.2036 19.7028 50.9113 19.9543C50.619 20.2058 50.2791 20.403 49.8916 20.5457C49.511 20.6817 49.1099 20.7496 48.6884 20.7496H47.8523C47.4309 20.7496 47.0264 20.6715 46.6389 20.5151C46.2582 20.352 45.9217 20.131 45.6294 19.8523C45.3439 19.5736 45.1128 19.2473 44.9361 18.8735C44.7661 18.4996 44.6812 18.1019 44.6812 17.6804V17.2624L45.9353 17.0584V17.7518C45.9353 18.0033 45.9863 18.2413 46.0883 18.4656C46.1971 18.6831 46.3398 18.8735 46.5166 19.0366C46.6933 19.1997 46.8972 19.3289 47.1284 19.4241C47.3595 19.5192 47.6008 19.5668 47.8523 19.5668H48.6884C48.94 19.5668 49.1813 19.5294 49.4124 19.4547C49.6435 19.3799 49.8475 19.2745 50.0242 19.1386C50.2077 18.9958 50.3505 18.8259 50.4525 18.6287C50.5612 18.4316 50.6156 18.2073 50.6156 17.9558C50.6156 17.5819 50.534 17.276 50.3709 17.0381C50.2077 16.8001 49.9902 16.603 49.7183 16.4466C49.4532 16.2835 49.1473 16.1509 48.8006 16.049C48.4607 15.9402 48.1106 15.8348 47.7503 15.7329C47.3901 15.6241 47.0366 15.5018 46.6899 15.3658C46.35 15.2298 46.0441 15.0531 45.7722 14.8356C45.5071 14.6112 45.293 14.3325 45.1298 13.9994C44.9667 13.6664 44.8851 13.2517 44.8851 12.7555C44.8851 12.334 44.9667 11.9499 45.1298 11.6032C45.293 11.2497 45.5139 10.9472 45.7926 10.6957C46.0713 10.4442 46.3942 10.2505 46.7613 10.1145C47.1351 9.97177 47.5328 9.90039 47.9543 9.90039H48.6884C49.1099 9.90039 49.5076 9.98196 49.8815 10.1451C50.2553 10.3015 50.5816 10.519 50.8603 10.7977C51.139 11.0764 51.3566 11.4027 51.5129 11.7766C51.6761 12.1505 51.7576 12.5481 51.7576 12.9696V13.0104L50.5034 13.2143V12.8982C50.5034 12.6467 50.4559 12.4122 50.3607 12.1946C50.2655 11.9703 50.1364 11.7766 49.9732 11.6134C49.8101 11.4503 49.6163 11.3211 49.392 11.226C49.1745 11.1308 48.94 11.0832 48.6884 11.0832H47.9543C47.7028 11.0832 47.4648 11.1206 47.2405 11.1954C47.023 11.2701 46.8326 11.3789 46.6695 11.5217C46.5064 11.6576 46.3772 11.8242 46.282 12.0213C46.1869 12.2184 46.1393 12.4428 46.1393 12.6943C46.1393 13.0546 46.2208 13.3503 46.384 13.5814C46.5471 13.8057 46.7613 13.996 47.0264 14.1524C47.2983 14.3087 47.6042 14.4413 47.9441 14.5501C48.2908 14.652 48.6409 14.7608 48.9943 14.8764C49.3546 14.9851 49.7047 15.1109 50.0446 15.2536C50.3913 15.3964 50.6972 15.5799 50.9623 15.8043C51.2342 16.0286 51.4517 16.3107 51.6149 16.6506C51.778 16.9837 51.8596 17.3983 51.8596 17.8946Z"
                  fill="black"
                />
                <path
                  d="M38.81 17.1402H34.1807L33.2222 20.5458H31.8558L36.1384 5.92383H36.8318L41.1348 20.5458H39.7786L38.81 17.1402ZM34.5376 15.886H38.4633L36.5361 9.14597L36.4953 8.60555L36.4545 9.14597L34.5376 15.886Z"
                  fill="black"
                />
              </svg>
            </div>
          </Link>
          <div className="dropdown text-[#343A40] ml-2" tabIndex={1}>
            <button
              className="btn btn-sm bg-transparent border-none justify-between items-center hover:bg-[#00000010]"
              style={{
                width: '150px',
                height: '40px',
                padding: '0 10px',
                fontSize: '14px',
                fontWeight: '700',
                color: '#343A40',
                textTransform: 'none'
              }}
              onClick={this.toggleDropdown.bind(this, 'projectsDropdown')}
              title={`${(myProject && myProject.name) || 'My Project'}`}
            >
              <div
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flexGrow: 1,
                  width: '80%',
                  textAlign: 'start',
                  padding: '10px 0',
                  letterSpacing: '0.04em'
                }}
              >
                {(myProject && myProject.name) || 'My Project'}
              </div>
              <svg
                width="13"
                height="13"
                fill="#343A40"
                className={`shrink-0 transition duration-300 ${dropdowns['projectsDropdown'] && '-rotate-180'
                  }`}
                viewBox="0 0 20 20"
              >
                <path d="M0 7 L 20 7 L 10 16" />
              </svg>
            </button>
            {dropdowns['projectsDropdown'] && (
              <div
                style={{
                  width: '100%',
                  maxHeight: '300px',
                  overflowX: 'hidden'
                }}
                tabIndex={1}
                className="dropdown-content d-flex flex-column text-start z-[2] shadow-lg bg-white rounded-lg px-1 py-2 custom-scroll-dropdown mt-1"
              >
                <Link
                  href="/projects"
                  className="btn btn-sm rounded-lg border-none bg-transparent flex justify-start hover:bg-[#0074F110]"
                  style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    color: '#343A40',
                    textTransform: 'none',
                    letterSpacing: '0.04em',
                    padding: '6px'
                  }}
                  title="All Projects"
                >
                  All Projects
                </Link>
                {projects &&
                  projects.map((item) => {
                    const isProjectSelected =
                      myProject && myProject._id === item._id;
                    return (
                      <button
                        className={`btn btn-sm rounded-lg border-none ${isProjectSelected
                          ? 'bg-[#0074F110]'
                          : 'bg-[transparent]'
                          } hover:bg-[#0074F110] items-center justify-${isProjectSelected ? 'between' : 'start'
                          } text-start w-100 h-auto px-[6px] mt-[3px] overflow-none`}
                        onClick={this.handleChangeProject.bind(
                          this,
                          item._id,
                          null
                        )}
                        style={{
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#343A40',
                          textTransform: 'none',
                          letterSpacing: '0.04em'
                        }}
                        title={`${item.name}`}
                      >
                        <p
                          className="py-1"
                          style={{
                            width: '80%',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            marginRight: isProjectSelected && '5px',
                            letterSpacing: '0.04em'
                          }}
                        >
                          {item.name}
                        </p>
                        {isProjectSelected && (
                          <span className="w-[10px] h-[10px] rounded-[50%] bg-[green]"></span>
                        )}
                      </button>
                    );
                  })}
              </div>
            )}
          </div>
          <div className="dropdown text-[#343A40] ml-2" tabIndex={1}>
            <button
              className="btn btn-sm bg-transparent border-none justify-between items-center hover:bg-[#00000010]"
              style={{
                width: '150px',
                height: '40px',
                padding: '0 10px',
                fontSize: '14px',
                fontWeight: '700',
                color: '#343A40',
                textTransform: 'none'
              }}
              onClick={this.toggleDropdown.bind(this, 'pagesDropdown')}
              title={`${(selectedPage && selectedPage.name) || 'Select Page'}`}
            >
              <div
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flexGrow: 1,
                  width: '80%',
                  textAlign: 'start',
                  padding: '10px 0',
                  letterSpacing: '0.04em'
                }}
              >
                {(selectedPage && selectedPage.name) || 'Select Page'}
              </div>
              <svg
                width="13"
                height="13"
                fill="#343A40"
                className={`shrink-0 transition duration-300 ${dropdowns['pagesDropdown'] && '-rotate-180'
                  }`}
                viewBox="0 0 20 20"
              >
                <path d="M0 7 L 20 7 L 10 16" />
              </svg>
            </button>
            {dropdowns['pagesDropdown'] && (
              <div
                style={{
                  width: '100%',
                  maxHeight: '300px',
                  overflowX: 'hidden'
                }}
                tabIndex={1}
                className="dropdown-content d-flex flex-column text-start z-[2] shadow-lg bg-white rounded-lg px-1 py-2 custom-scroll-dropdown mt-1"
              >
                {pages &&
                  pages.map((item) => {
                    const isPageSelected =
                      selectedPage && selectedPage.name === item.name;
                    return (
                      <button
                        className={`btn btn-sm rounded-lg border-none ${isPageSelected ? 'bg-[#0074F110]' : 'bg-[transparent]'
                          } hover:bg-[#0074F110] items-center justify-${isPageSelected ? 'between' : 'start'
                          } text-start w-100 h-auto px-[6px] mt-[3px] overflow-none`}
                        onClick={this.handleChangePage.bind(this, item.name)}
                        style={{
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#343A40',
                          textTransform: 'none',
                          letterSpacing: '0.04em'
                        }}
                        title={`${item.name}`}
                      >
                        <p
                          className="py-1"
                          style={{
                            width: '80%',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            marginRight: isPageSelected && '5px',
                            letterSpacing: '0.04em'
                          }}
                        >
                          {item.name}
                        </p>
                        {isPageSelected && (
                          <span className="w-[10px] h-[10px] rounded-[50%] bg-[green]"></span>
                        )}
                      </button>
                    );
                  })}
              </div>
            )}
          </div>
          <div className="dropdown text-[#343A40] ml-2" tabIndex={1}>
            <button
              className="btn btn-sm bg-transparent border-none justify-between items-center hover:bg-[#00000010]"
              style={{
                width: '150px',
                height: '40px',
                padding: '0 10px',
                fontSize: '14px',
                fontWeight: '700',
                color: '#343A40',
                textTransform: 'none'
              }}
              onClick={this.toggleDropdown.bind(this, 'branchesDropdown')}
              title={`${(selectedBranch && selectedBranch.name) || 'Select Branch'
                }`}
            >
              <div
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flexGrow: 1,
                  width: '80%',
                  textAlign: 'start',
                  padding: '10px 0',
                  letterSpacing: '0.04em'
                }}
              >
                {(selectedBranch && selectedBranch.name) || 'Select Branch'}
              </div>
              <svg
                width="13"
                height="13"
                fill="#343A40"
                className={`shrink-0 transition duration-300 ${dropdowns['branchesDropdown'] && '-rotate-180'
                  }`}
                viewBox="0 0 20 20"
              >
                <path d="M0 7 L 20 7 L 10 16" />
              </svg>
            </button>
            {dropdowns['branchesDropdown'] && (
              <div
                style={{
                  width: '100%',
                  maxHeight: '300px',
                  overflowX: 'hidden'
                }}
                tabIndex={1}
                className="dropdown-content d-flex flex-column text-start z-[2] shadow-lg bg-white rounded-lg px-1 py-2 custom-scroll-dropdown mt-1"
              >
                {branches &&
                  branches.map((item) => {
                    const isPageSelected =
                      selectedBranch && selectedBranch.name === item.name;
                    return (
                      <button
                        className={`btn btn-sm rounded-lg border-none ${isPageSelected ? 'bg-[#0074F110]' : 'bg-[transparent]'
                          } hover:bg-[#0074F110] items-center justify-${isPageSelected ? 'between' : 'start'
                          } text-start w-100 h-auto px-[6px] mt-[3px] overflow-none`}
                        onClick={this.handleChangeBranch.bind(this, item.name)}
                        style={{
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#343A40',
                          textTransform: 'none',
                          letterSpacing: '0.04em'
                        }}
                        title={`${item.name}`}
                      >
                        <p
                          className="py-1"
                          style={{
                            width: '80%',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            marginRight: isPageSelected && '5px',
                            letterSpacing: '0.04em'
                          }}
                        >
                          {item.name}
                        </p>
                        {isPageSelected && (
                          <span className="w-[10px] h-[10px] rounded-[50%] bg-[green]"></span>
                        )}
                      </button>
                    );
                  })}
              </div>
            )}
          </div>
          <button
            className="btn btn-sm btn-outline"
            onClick={this.toggleBranchDialog.bind(this)}
            style={{ ...buttonStyle, width: '34px', height: '34px' }}
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 16 16"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M15.5834 9.08329H9.08335V15.5833H6.91669V9.08329H0.416687V6.91663H6.91669V0.416626H9.08335V6.91663H15.5834V9.08329Z" />
            </svg>
          </button>
        </div>
        {this.props.view !== 'dialogs' && !isSelectedThemePage && (
          <div className="flex">
            {(myProject.projectType === 'PROJECT_TYPE_APP'
              ? screenTypes.filter(t => t.deviceType === 'mobile' || t.deviceType === 'mobileLandscape')
              : screenTypes
            ).map(({ icon, deviceType }) => (
              <button
                className={`btn p-0 border-none ${selectedScreenType === deviceType
                  ? 'bg-[#00000040]'
                  : 'bg-transparent'
                  } hover:bg-[#00000010]`}
                key={deviceType}
                d-Automation={`screen-${deviceType}`}
                style={{
                  width: '40px',
                  height: '90%',
                  borderRadius: '0px',
                  margin: '2px'
                }}
                onClick={this.onClickScreenMode.bind(this, deviceType)}
              >
                {icon}
              </button>
            ))}
            {myProject.projectType === 'PROJECT_TYPE_APP' && (
              <button
                className="btn p-0 border-none bg-transparent hover:bg-[#00000010]"
                d-Automation="screen-rotation"
                style={{
                  width: '40px',
                  height: '90%',
                  borderRadius: '0px',
                  margin: '2px'
                }}
                title="Screen Rotation"
                onClick={this.handleScreenRotation.bind(this)}
              >
                <IconComponent
                  iconName="ScreenRotation"
                  style={{
                    color: '#1876D2',
                    fontSize: '22px',
                    fontWeight: 'bold'
                  }}
                />
              </button>
            )}
          </div>
        )}
        <Tooltip
          title="Enable to live replicate copies elements across different screens"
          arrow
          placement="top"
        >
          {this.props.view !== 'dialogs' && !isSelectedThemePage && (
            <div
              className="relative group text-blue-500 font-bold flex justify-center items-center cursor-pointer"
              style={{ fontFamily: 'Inter' }}
              onClick={(e) => this.handleToggle(e)}
            >
              Live Replicate
              <Switch
                checked={
                  this.props.projectReducer.myProject?.settings
                    ?.liveReplicate ?? false
                }
                color="primary"
                className="ml-2"
              />
            </div>
          )}
        </Tooltip>

        <div className="flex align-items-center h-full">
          <div className="dropdown dropdown-start" tabIndex={3}>
            <button
              className="btn btn-sm btn-circle btn-outline btn-primary"
              style={{ height: '40px', width: '40px' }}
              onClick={this.toggleDropdown.bind(this, 'menuDropdown')}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="w-6 h-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 6.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-********* 0 0 1 0 1.5Z"
                />
              </svg>
            </button>
            {dropdowns['menuDropdown'] && this.getMenuDropDown()}
          </div>
          <button
            onClick={this.handlePreviewButton.bind(this)}
            className="btn btn-sm  mx-2 btn-outline btn-primary"
            style={{
              width: '135px',
              padding: '9px 30px',
              borderRadius: '80px',
              height: '40px',
              fontSize: '14px',
              fontWeight: '700',
              lineHeight: '16.94px',
              letterSpacing: '0.1em',
              textAlign: 'center'
            }}
          >
            PREVIEW
          </button>
          {this.renderBranchActionButton()}
          <div className="dropdown dropdown-end ml-0.5 mr-1" tabIndex={4}>
            <button
              onClick={this.toggleDropdown.bind(this, 'userDropdown')}
              className="btn btn-xs mx-1 btn-outline"
              key="user"
              style={{
                width: '40px',
                height: '40px',
                backgroundColor: '#0074F1',
                borderRadius: '100%',
                color: 'white',
                fontWeight: '500',
                fontSize: '18px'
              }}
            >
              {loggedInUser.email && loggedInUser.email[0].toUpperCase()}
            </button>
            {dropdowns['userDropdown'] && (
              <div
                tabIndex={4}
                style={{ width: '350px', color: '#343A40' }}
                className="dropdown-content d-flex flex-column align-items-center z-[2] p-3 shadow bg-white rounded-box mt-2 mr-2"
              >
                <div style={{ fontWeight: '500' }}>
                  {loggedInUser && loggedInUser.email}
                </div>
                <Link
                  href="/login"
                  className="btn btn-sm border-none btn-hover-effect"
                  style={{
                    width: '75%',
                    height: '40px',
                    fontSize: '14px',
                    fontWeight: '700',
                    lineHeight: '16.94px',
                    letterSpacing: '0.1em',
                    background:
                      'linear-gradient(43deg, #4158D0 0%, #C850C0 46%, #FFCC70 100%)',
                    borderRadius: '80px',
                    color: '#FFFFFF',
                    marginTop: '80px'
                  }}
                  onClick={this.handleLogout.bind(this)}
                >
                  LOGOUT
                </Link>
              </div>
            )}
          </div>
        </div>
        <Dialog
          open={this.state.isBranchDialogOpen}
          onClose={this.toggleBranchDialog}
          TransitionComponent={Transition}
          keepMounted
          aria-labelledby="transition-modal-title"
          aria-describedby="transition-modal-description"
          fullWidth
          maxWidth={false}
          sx={{
            '& .MuiDialog-paper': {
              padding: '3% 5%',
              width: '46%'
            }
          }}
        >
          <h1 className="text-[22px] text-black font-semibold mb-4">
            Create New Branch
          </h1>
          <EditableForm
            data={newBranchSchema}
            onSubmit={this.onSubmit}
            ref="create-branch-dialog"
            key="create-branch-dialog"
            dispatch={this.props.dispatch}
          />
          {branchCreatedError && (
            <p className="text-[red]">{branchCreatedError}</p>
          )}
          <div className="flex justify-end w-full mt-[20px]">
            <button
              style={{
                '--fallback-background': '#FFFFFF',
                fontSize: '14px',
                fontWeight: '400',
                letterSpacing: '0.1em',
                width: 'auto'
              }}
              className="btn btn-sm custom-button font-semibold h-[50px] min-w-[132px] text-[#495057] bg-white border-solid border-[1px] border-[#495057] rounded-[4px] mr-[10px]"
              onClick={this.toggleBranchDialog}
            >
              Cancel
            </button>
            <button
              style={{
                '--fallback-background': '#0074F1',
                fontSize: '14px',
                fontWeight: '400',
                letterSpacing: '0.1em',
                width: 'auto'
              }}
              className="btn btn-sm custom-button h-[50px] min-w-[132px] text-white bg-[#0074F1] rounded-[4px] border-none"
              onClick={this.submitForm}
            >
              Create Branch
            </button>
          </div>
        </Dialog>
        <Dialog
          open={this.state.mergeBranchDialog}
          onClose={this.toggleMergeBranchDialog}
          TransitionComponent={Transition}
          keepMounted
          aria-labelledby="transition-modal-title"
          aria-describedby="transition-modal-description"
          fullWidth
          maxWidth={false}
          sx={{
            '& .MuiDialog-paper': {
              padding: '3% 5%',
              width: '46%'
            }
          }}
        >
          <h1 className="text-[22px] text-black font-semibold mb-4">
            Confirm Branch Merge
          </h1>
          {this.renderBranchDropdown()}
          <EditableForm
            data={mergeBranchSchema}
            onSubmit={this.onSubmitMerge}
            ref="merge-branch"
            key="merge-branch"
            dispatch={this.props.dispatch}
          />
          {this.state.mergeValidationStatus ? (
            this.state.mergeValidationStatus !== 'ready to merge' ? (
              this.state.mergeValidationStatus === 'Branch Ahead' ? (
                <div className="flex-col">
                  <div
                    className="flex items-center text-[18px]"
                    style={{ color: '#AF1740' }}
                  >
                    <IconComponent iconName="Close" />
                    <span className="ml-2">
                      Target branch is ahead of this branch. Please resync.
                    </span>
                  </div>
                </div>
              ) : (
                <div
                  className="flex items-center text-[18px]"
                  style={{ color: '#f19605' }}
                >
                  <span className="loading loading-ring loading-md mr-2"></span>
                  <span>{this.state.mergeValidationStatus}</span>
                </div>
              )
            ) : (
              <div
                className="flex items-center text-[18px]"
                style={{ color: '#118B50' }}
              >
                <IconComponent iconName="CheckCircle" />
                <span className="ml-2">{this.state.mergeValidationStatus}</span>
              </div>
            )
          ) : null}
          <div className="flex justify-end w-full mt-[20px]">
            <button
              style={{
                '--fallback-background': '#FFFFFF',
                fontSize: '14px',
                fontWeight: '400',
                letterSpacing: '0.1em',
                width: 'auto'
              }}
              className="btn btn-sm custom-button font-semibold h-[50px] min-w-[132px] text-[#495057] bg-white border-solid border-[1px] border-[#495057] rounded-[4px] mr-[10px]"
              onClick={this.toggleMergeBranchDialog}
            >
              Cancel
            </button>
            <button
              style={{
                '--fallback-background': '#0074F1',
                fontSize: '14px',
                fontWeight: '400',
                letterSpacing: '0.1em',
                width: 'auto'
              }}
              className="btn btn-sm custom-button h-[50px] min-w-[132px] text-white bg-[#0074F1] rounded-[4px] border-none"
              onClick={
                this.state.mergeValidationStatus === 'Branch Ahead'
                  ? this.pullFromBranch
                  : this.submitMergeForm
              }
            >
              {this.state.mergeValidationStatus === 'Branch Ahead'
                ? 'Branch Resync'
                : 'Confirm Merge'}
            </button>
          </div>
        </Dialog>
        <Dialog
          open={this.state.isConflictDialogOpen}
          onClose={this.toggleConflictDialog}
          TransitionComponent={Transition}
          keepMounted
          aria-labelledby="transition-modal-title"
          aria-describedby="transition-modal-description"
          fullWidth
          maxWidth={false}
          sx={{
            '& .MuiDialog-paper': {
              padding: '2% 4%',
              width: '86%'
            }
          }}
        >
          {this.renderConflicts()}
        </Dialog>
      </div>
    );
  }

  toggleBranchDialog = () => {
    if (this.props.projectReducer.showCreateBranchDialog) {
      this.setState({ isBranchDialogOpen: false });
      this.props.dispatch(sendAction('SET_BRANCH_POPUP'));
    } else {
      this.setState((prevState) => ({
        isBranchDialogOpen: !prevState.isBranchDialogOpen
      }));
    }
  };

  toggleMergeBranchDialog = () => {
    this.setState((prevState) => ({
      mergeBranchDialog: !prevState.mergeBranchDialog
    }));
  };

  toggleConflictDialog = () => {
    this.setState((prevState) => ({
      isConflictDialogOpen: !prevState.isConflictDialogOpen
    }));
  };

  submitForm = () => {
    this.refs['create-branch-dialog'].submitForm();
  };

  submitMergeForm = () => {
    this.refs['merge-branch'].submitForm();
  };

  onSubmitMerge = (questionAnswers) => {
    if (questionAnswers.notes && this.state.toBranchName) {
      const {
        projectReducer: { selectedBranch },
        dispatch
      } = this.props;
      this.setState({
        notes: questionAnswers.notes,
        mergeValidationStatus: 'validating ready to merge...'
      });
      const payload = {
        _fromBranch: selectedBranch._id,
        _toBranch: selectedBranch._fromBranch
      };
      dispatch(sendActionAsync('READY_TO_MERGE', payload));
    }
  };
  onSubmit = (questionAnswers) => {
    const { dispatch, projectReducer } = this.props;
    const branches = projectReducer.projectBranches || [];
    const myProject = projectReducer.myProject;
    const existing = branches.filter(
      (each) => each.name === questionAnswers.name
    );
    if (existing.length === 0) {
      this.setState({ isBranchDialogOpen: false, branchCreatedError: false });
      const branchPayload = {
        tags: [],
        _project: myProject._id,
        _profile: myProject._profile,
        deleted: false,
        name: questionAnswers.name,
        _branchType: 'USER',
        _fromBranch: this.props.projectReducer.selectedBranch._id,
        checkoutToBranch: true,
        latestCommitId: this.props.projectReducer.selectedBranch._latestCommit
      };
      dispatch(sendActionAsync('CREATE_BRANCH', branchPayload));
    } else {
      this.setState({ branchCreatedError: 'Branch name already exists' });
    }
  };

  onClickScreenMode = (screenType) => {
    const { dispatch } = this.props;
    dispatch(sendAction('CHANGE_SCREEN_TYPE', screenType));
    dispatch(sendAction('ELEMENT_SELECTED', null));
    this.setState({ copyScreen: false, screenDropDown: [] });
  };

  handleScreenRotation = () => {
    const { dispatch, projectReducer } = this.props;
    const currentScreenType = projectReducer.myProject?.selectedScreenType;

    // Toggle between mobile and mobileLandscape
    let newScreenType;
    if (currentScreenType === 'mobile') {
      newScreenType = 'mobileLandscape';
    } else if (currentScreenType === 'mobileLandscape') {
      newScreenType = 'mobile';
    } else {
      // If currently on any other screen type, default to mobile
      newScreenType = 'mobile';
    }

    dispatch(sendAction('CHANGE_SCREEN_TYPE', newScreenType));
    dispatch(sendAction('ELEMENT_SELECTED', null));
    this.setState({ copyScreen: false, screenDropDown: [] });
  };

  screenRadioSelected = (e) => {
    const items =
      this.props.projectReducer.selectedPage?.screen[e.target.value]?.items;
    this.setState({ screenDropDown: items, copyScreen: e.target.value });
  };

  handleTranslations = (pagesPopulated, currentLanguage) => {
    let i18nResources = {};
    pagesPopulated.forEach((page) => {
      Object.keys(page.screen).forEach((screenType) => {
        const itemsArray = page.screen[screenType]?.items;
        itemsArray?.forEach((item) => {
          let id = item.id;
          if (
            item.source.label === 'Simple Form' ||
            item.source.label === 'Simple Container' ||
            item.source.label === 'List Container'
          ) {
            let items = item.data.renderProps.items || [];
            items.forEach((innerItem) => {
              let id = innerItem.id;
              let finalObject = this.handleFinalObject(innerItem);
              i18nResources = this.handleUpdatei18Resources(
                id,
                i18nResources,
                currentLanguage,
                finalObject
              );
            });
          }
          let finalObject = this.handleFinalObject(item);
          i18nResources = this.handleUpdatei18Resources(
            id,
            i18nResources,
            currentLanguage,
            finalObject
          );
        });
      });
    });
    return i18nResources;
  };

  handleUpdatei18Resources = (
    id,
    i18nResources,
    currentLanguage,
    finalObject
  ) => {
    let updatedI18nResources = { ...i18nResources };

    if (!updatedI18nResources[currentLanguage]) {
      updatedI18nResources[currentLanguage] = {};
    }
    if (updatedI18nResources[currentLanguage][id]) {
      updatedI18nResources[currentLanguage][id] = {
        ...updatedI18nResources[currentLanguage][id],
        ...finalObject
      };
    } else {
      updatedI18nResources[currentLanguage][id] = finalObject;
    }
    return updatedI18nResources;
  };

  handleFinalObject = (item) => {
    const elements = getElements(
      this.props.projectReducer?.myProject?.projectType
    );
    const themedElement = find(elements, { label: item.source.label });
    let properties = themedElement.properties;
    let renderProps = item.data.renderProps;
    let filteredObjects = properties.filter(
      (obj) => obj.hasOwnProperty('i18n') && obj.i18n === true
    );
    const modifiedObjects = filteredObjects.map((obj) => ({
      ...obj,
      ...(obj.i18n === true ? {} : { i18n: undefined })
    }));
    const finalObject = {};
    modifiedObjects.forEach((obj) => {
      const key = obj.name;
      const value = renderProps[key];

      if (value !== undefined) {
        if (key === 'options') {
          value.forEach((item, index) => {
            finalObject['option' + (index + 1)] = item.label;
          });
        } else {
          finalObject[key] = value;
        }
      }
    });
    return finalObject;
  };

  handleSaveButton = (publishedTarget) => {
    const { dispatch, projectReducer } = this.props;
    const payload = Object.assign({}, projectReducer.myProject);
    payload._branch = this.props.projectReducer.selectedBranch._id;

    if (publishedTarget) {
      payload.publishedTarget = publishedTarget;
    }

    dispatch(sendActionAsync(SAVE_PROJECT, payload));
  };

  handlePreviewButton = () => {
    const projectid = this.props.router.query.projectid;
    const pagePath = this.props.projectReducer.selectedPage.path;
    const selectedBranch = this.props.projectReducer.selectedBranch;

    // Set preview loading state
    this.setState({ isPreviewing: true });

    // Use setTimeout to ensure state is updated before opening new window
    setTimeout(() => {
      window.open(
        `/preview/${projectid}/${pagePath}?branch=${selectedBranch.name}`,
        '_blank'
      );

      // Reset preview state
      this.setState({ isPreviewing: false });
    }, 0);
  };

  handleLogout = () => {
    const { dispatch } = this.props;
    dispatch(sendActionAsync(LOGOUT));
  };

  handleChangeProject = (projectId, selectedBranchName) => {
    const project = find(this.props.projectReducer.projects, {
      _id: projectId
    });
    if (project) {
      this.props.dispatch(sendAction('GET_PAGE', null));
      const page1Name = project.pagesPopulated[0].name;
      const branchName =
        selectedBranchName ||
        this.props.projectReducer?.projectBranches[0]?.name ||
        'master';
      this.props.router.push(
        `/projects/${project._id}/${page1Name}?branch=${branchName}`
      );
    }
  };

  handleChangePage = (pageName) => {
    const {
      projectReducer: { myProject },
      view
    } = this.props;
    const branchName = this.props.projectReducer?.selectedBranch?.name;
    if (view === 'dialogs') {
      this.props.router.push(
        `/projects/${myProject._id}/dialogs/${pageName}?branch=${branchName}`
      );
    } else {
      this.props.router.push(
        `/projects/${myProject._id}/${pageName}?branch=${branchName}`
      );
    }
  };

  handleChangeBranch = (branchName) => {
    const currentBranch = this.props.router.query.branch;
    if (currentBranch !== branchName) {
      this.props.router.push({
        pathname: this.props.router.pathname,
        query: { ...this.props.router.query, branch: branchName }
      });
      this.props.dispatch(sendAction('SET_BRANCH', null));
    }
  };

  handleSelectAll = () => {
    this.setState((prevState) => {
      const selectAll = !prevState.selectAll;
      return {
        selectAll,
        selectedItems: selectAll ? this.state.screenDropDown : []
      };
    });
  };

  handleSelectItem = (item) => {
    this.setState((prevState) => {
      if (
        prevState.selectedItems.some(
          (selectedItem) => selectedItem.id === item.id
        )
      ) {
        return {
          selectedItems: prevState.selectedItems.filter(
            (selectedItem) => selectedItem.id !== item.id
          )
        };
      } else {
        return { selectedItems: [...prevState.selectedItems, item] };
      }
    });
  };

  toggleDropdown = (dropdownId, e) => {
    e.stopPropagation();
    this.setState((prevState) => ({
      dropdowns: Object.keys(prevState.dropdowns).reduce(
        (acc, key) => ({
          ...acc,
          [key]: key === dropdownId ? !prevState.dropdowns[dropdownId] : false
        }),
        {}
      )
    }));
  };

  handleOutsideClick = (event) => {
    const isDropdownClick =
      event.target.closest('.dropdown-content') ||
      event.target.closest('.saveDropdown');
    if (isDropdownClick) {
      this.setState({ isDropdownOpen: true });
    } else {
      this.setState({
        saveDropdownOpen: false,
        dropdowns: {
          projectsDropdown: false,
          menuDropdown: false,
          userDropdown: false,
          pagesDropdown: false,
          branchesDropdown: false
        }
      });
    }
  };

  getMenuDropDown = () => {
    const targets = this.props.projectReducer.myProject?.targets || [];
    const projectType =
      this.props.projectReducer?.myProject.projectType || 'WEB_APPLICATION';
    if (projectType === PROJECT_TYPE_PEGA_DX_COMPONENT) {
      return (
        <div
          style={{
            width: '320px',
            maxHeight: '420px',
            overflowY: 'auto',
            color: '#343A40',
            fontSize: '14px'
          }}
          tabIndex={3}
          className="dropdown-content d-flex flex-column text-start z-[2] shadow-lg bg-white rounded-lg p-1 px-2 mt-1 ml-1 custom-scroll-dropdown"
        >
          <button className="btn" onClick={this.onPublishComponent}>
            Publish
          </button>
        </div>
      );
    } else {
      return (
        <div
          style={{
            width: '320px',
            maxHeight: '420px',
            overflowY: 'auto',
            color: '#343A40',
            fontSize: '14px'
          }}
          tabIndex={3}
          className="dropdown-content d-flex flex-column text-start z-[2] shadow-lg bg-white rounded-lg p-1 px-2 mt-1 ml-1 custom-scroll-dropdown"
        >
          <div className="collapse collapse-arrow rounded-lg border-b-2 custom-collapse">
            <input type="checkbox" className="peer" />
            <div className="collapse-title flex cursor-pointer items-center justify-between text-[16px] text-[#343A40] font-medium">
              Publish
            </div>
            <div className="collapse-content bg-transparent rounded-md p-0 m-0">
              {targets && targets.length ? (
                <div
                  className="d-flex flex-column overflow-auto custom-scroll-dropdown bg-[#F7F7F7] rounded-md m-0 p-0"
                  style={{
                    maxHeight: '200px',
                    overflowX: 'hidden',
                    margin: '0px'
                  }}
                >
                  {targets.map((targetItem) => (
                    <button
                      className="btn btn-sm text-[#343A40] text-[14px] bg-transparent d-flex justify-content-start border-none hover:bg-transparent mb-1"
                      style={{ textTransform: 'none' }}
                      onClick={this.handleSaveButton.bind(
                        this,
                        targetItem.name
                      )}
                    >
                      {targetItem.name}
                    </button>
                  ))}
                </div>
              ) : (
                <div className="d-flex flex-column align-items-center">
                  <p className="text-[16px] font-medium text-[#343A40] text-center">
                    No targets to show
                  </p>
                  <button
                    className="btn btn-outline btn-primary my-2 mb-3"
                    onClick={(e) => {
                      if (this.props.router.pathname.endsWith('/targets')) {
                        // If already on targets page, just close the dropdown
                        this.toggleDropdown('menuDropdown', e);
                      } else {
                        // If not on targets page, navigate to targets page
                        this.props.router.push(`/projects/${this.props.router.query.projectid}/targets`);
                      }
                    }}
                  >
                    New target
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }
  };

  publishSelectedComponents = () => {
    const { dispatch } = this.props;
    const projectId = this.props.router.query.projectid;
    const componentsToPublish = this.state.flattenedItems
      .filter(item => this.state.selectedItems[item.key])
      .map(item => ({
        key: item.key,
        type: item.type,
        subtype: item.subtype,
        liveEditing: this.state.liveEditingStates[item.key]
      }));
    dispatch(sendActionAsync(PUBLISH_PEGA_COMPONENT, { projectId, componentsToPublish }));
    const branchName = this.props.projectReducer?.selectedBranch?.name;
    this.setState({ showModal: false });
    this.props.router.push(
      `/projects/${projectId}/targets?branch=${branchName}`
    );
  };

  onPublishComponent = () => {
    const { selectedCommit } = this.props.projectReducer;
    const filterPages = selectedCommit.pages.filter(page => page.path !== '_theme');

    const flattenedItems = [];

    filterPages.forEach(page => {
      const items = page?.screen?.desktop?.items || [];
      items.forEach(item => {
        const renderProps = item?.data?.renderProps || {};
        const key = renderProps.pegaComponentKey;
        const compId = item?.data?.id;
        const type = renderProps.pegaType || '';
        const subtype = renderProps.pegaSubtype || '';
        const isValid = key && type && subtype;

        flattenedItems.push({
          key,
          compId,
          type,
          subtype,
          isValid,
          liveEditing: item?.liveReplicate || false
        });
      });
    });

    const selectedItems = {};
    const liveEditingStates = {};

    flattenedItems.forEach(item => {
      selectedItems[item.key] = false;
      liveEditingStates[item.key] = item.liveEditing;
    });

    this.setState({
      showModal: true,
      flattenedItems,
      selectedItems,
      liveEditingStates
    });
  };

  handleResolveConflict = (conflictIndex) => {
    this.setState({ selectedConflict: this.state.conflicts[conflictIndex] });
  };

  handleCheckboxChange = (conflictKey, value) => {
    const { selectedConflict, conflicts } = this.state;

    const updatedConflict = {
      ...selectedConflict,
      resolvedValue: {
        [conflictKey]: value
      },
      resolved: true
    };

    const updatedConflicts = conflicts.map((conflict) =>
      conflict?.key === selectedConflict?.key &&
        conflict?.id === selectedConflict?.id &&
        (conflict?.id?.startsWith('page_') ? conflict?.path === selectedConflict?.path : true)
        ? updatedConflict
        : conflict
    );

    this.setState({
      selectedConflict: updatedConflict,
      conflicts: updatedConflicts
    });
  };

  handleMergeResolvedConflicts = () => {
    const {
      projectReducer: {
        selectedBranch,
        currentBranchHasConflicts,
        myProject,
        selectedCommit
      },
      dispatch
    } = this.props;
    if (currentBranchHasConflicts) {
      const payload = Object.assign({}, myProject);
      payload._branch = selectedBranch._id;
      payload._commitId = selectedCommit._id;
      payload.resolvedConflicts = this.state.conflicts;
      payload.mergeWithConflicts = true;
      dispatch(sendActionAsync(SAVE_PROJECT, payload));
    } else {
      const payload = {
        _fromBranch: selectedBranch._id,
        _toBranch: selectedBranch._fromBranch,
        mergeWithConflicts: true,
        resolvedConflicts: this.state.conflicts
      };
      dispatch(sendActionAsync('MERGE_BRANCH', payload));
    }
  };

  renderConflicts = () => {
    const { conflicts = [], selectedConflict } = this.state;

    // Filter conflicts into pages and components
    const pageConflicts = conflicts
      .map((conflict, index) => ({ ...conflict, originalIndex: index }))
      .filter(conflict => conflict.id.startsWith('page'));

    const componentConflicts = conflicts
      .map((conflict, index) => ({ ...conflict, originalIndex: index }))
      .filter(conflict => !conflict.id.startsWith('page'));

    const selectedIndex = conflicts.findIndex((conflict) =>
      conflict?.key === selectedConflict?.key &&
      conflict?.id === selectedConflict?.id &&
      (conflict?.id?.startsWith('page_') ? conflict?.path === selectedConflict?.path : true)
    );

    const allResolved = conflicts.every(conflict => conflict.resolved);
    const hasNextConflict = selectedIndex >= (conflicts.length - 1);

    return (
      <div className="font-[inter]">
        <h1 className="text-[18px] text-[#000000] font-[500] mb-1">
          Resolve Conflicts before updating the branch
        </h1>
        <h1 className="text-[14px] text-[#495057] font-[400] mb-4">
          Select the version you would like to resolve conflicts. After selection, you can update the branch.
        </h1>
        <h1 className="text-[18px] text-[#E21818] font-[500] mb-2">
          {conflicts.length} Conflicts
        </h1>


        {componentConflicts.length > 0 && (
          <div
            className="w-full overflow-auto mb-6"
            style={{
              boxShadow: '0px 0px 3px 3px #0074F133',
              maxHeight: '260px',
              overflowY: 'auto'
            }}
          >
            <table className="w-full">
              <thead className="bg-[#ffffff] text-left text-[16px] text-[#000000] font-[500]">
                <tr>
                  <th className="py-2 px-4" style={{ width: '30%' }}>Components</th>
                  <th className="py-2 px-4" style={{ width: '30%' }}>Id</th>
                  <th className="py-2 px-4" style={{ width: '30%' }}>Action</th>
                </tr>
              </thead>
              <tbody>
                {componentConflicts.map((conflict, index) => (
                  <tr
                    key={`component-${conflict.originalIndex}`}
                    className={
                      conflict.key === selectedConflict?.key && conflict.id === selectedConflict?.id
                        ? 'bg-[#0074f110]'
                        : index % 2 === 0
                          ? 'bg-[#F8F9FA]'
                          : 'bg-white'
                    }
                  >
                    <td className="py-2 px-4">{conflict.label}</td>
                    <td className="py-2 px-4">{conflict.id}</td>
                    <td className="py-2 px-4">
                      {conflict.resolved ? (
                        <button
                          className="bg-[#0D6EFD] text-white py-1 rounded-md w-[80px]"
                          onClick={() => this.handleResolveConflict(conflict.originalIndex)}
                        >
                          ✓
                        </button>
                      ) : (
                        <button
                          className="border border-[#0D6EFD] text-[#0D6EFD] py-1 rounded-md w-[80px]"
                          onClick={() => this.handleResolveConflict(conflict.originalIndex)}
                        >
                          Check
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}


        {pageConflicts.length > 0 && (
          <div
            className="w-full overflow-auto mb-6"
            style={{
              boxShadow: '0px 0px 3px 3px #0074F133',
              maxHeight: '260px',
              overflowY: 'auto'
            }}
          >
            <table className="w-full">
              <thead className="bg-[#ffffff] text-left text-[16px] text-[#000000] font-[500]">
                <tr>
                  <th className="py-2 px-4" style={{ width: '30%' }}>Page</th>
                  <th className="py-2 px-4" style={{ width: '30%' }}>Id</th>
                  <th className="py-2 px-4" style={{ width: '30%' }}>Action</th>
                </tr>
              </thead>
              <tbody>
                {pageConflicts.map((conflict, index) => (
                  <tr
                    key={`page-${conflict.originalIndex}`}
                    className={
                      conflict.key === selectedConflict?.key && conflict.id === selectedConflict?.id && conflict?.path === selectedConflict?.path
                        ? 'bg-[#0074f110]'
                        : index % 2 === 0
                          ? 'bg-[#F8F9FA]'
                          : 'bg-white'
                    }
                  >
                    <td className="py-2 px-4">{conflict.label}</td>
                    <td className="py-2 px-4">{conflict.id}</td>
                    <td className="py-2 px-4">
                      {conflict.resolved ? (
                        <button
                          className="bg-[#0D6EFD] text-white py-1 rounded-md w-[80px]"
                          onClick={() => this.handleResolveConflict(conflict.originalIndex)}
                        >
                          ✓
                        </button>
                      ) : (
                        <button
                          className="border border-[#0D6EFD] text-[#0D6EFD] py-1 rounded-md w-[80px]"
                          onClick={() => this.handleResolveConflict(conflict.originalIndex)}
                        >
                          Check
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}


        {selectedConflict && (
          <div key={selectedConflict} className="grid grid-cols-3 gap-5 my-5">
            {/* Local Box */}
            <div
              className="bg-[#DECE9620] border border-[#DECE96] rounded-md"
              style={{ boxShadow: '0px 0px 10px 0px #DECE96' }}
            >
              <div className="flex items-center px-4 py-3 bg-[#DECE964D] rounded-t-md">
                <h2 className="text-[14px] font-[600] text-[#C29700] flex-grow text-center">
                  LOCAL
                </h2>
                <input
                  type="checkbox"
                  className="checkbox checkbox-warning"
                  checked={
                    selectedConflict?.resolvedValue?.hasOwnProperty('local') &&
                    selectedConflict.resolvedValue?.local ===
                    selectedConflict.fromValue
                  }
                  onChange={() =>
                    this.handleCheckboxChange(
                      'local',
                      selectedConflict.fromValue
                    )
                  }
                />
              </div>
              <div className="px-4 py-3 text-[14px] rounded-b-md">
                <span className="font-semibold">{selectedConflict.key}:</span>
                &nbsp;
                <span>{selectedConflict.fromValue}</span>
              </div>
            </div>

            {/* Base Box */}
            <div
              className="bg-[#FFFFFF] border border-[#DE00F1] rounded-md"
              style={{ boxShadow: '0px 0px 10px 0px #DE00F14D' }}
            >
              <div className="flex items-center px-4 py-3 bg-[#DE00F123] rounded-t-md">
                <h2 className="text-[14px] font-[600] text-[#DE00F1] flex-grow text-center">
                  BASE
                </h2>
                <input
                  type="checkbox"
                  className="checkbox checkbox-secondary"
                  checked={
                    selectedConflict?.resolvedValue?.hasOwnProperty('base') &&
                    selectedConflict.resolvedValue?.base ===
                    selectedConflict.baseValue
                  }
                  onChange={() =>
                    this.handleCheckboxChange(
                      'base',
                      selectedConflict.baseValue
                    )
                  }
                />
              </div>
              <div className="px-4 py-3 text-[14px] rounded-b-md">
                <span className="font-semibold">{selectedConflict.key}:</span>
                &nbsp;
                <span>{selectedConflict.baseValue || 'N/A'}</span>
              </div>
            </div>

            {/* Remote Box */}
            <div
              className="bg-[#27B56913] border border-[#2E9A25] rounded-md"
              style={{ boxShadow: '0px 0px 10px 0px #2E9A224D' }}
            >
              <div className="flex items-center px-4 py-3 bg-[#27B56923] rounded-t-md">
                <h2 className="text-[14px] font-[600] text-[#2E9A25] flex-grow text-center">
                  REMOTE
                </h2>
                <input
                  type="checkbox"
                  className="checkbox checkbox-success"
                  checked={
                    selectedConflict?.resolvedValue?.hasOwnProperty('remote') &&
                    selectedConflict.resolvedValue?.remote ===
                    selectedConflict.toValue
                  }
                  onChange={() =>
                    this.handleCheckboxChange(
                      'remote',
                      selectedConflict.toValue
                    )
                  }
                />
              </div>
              <div className="px-4 py-3 text-[14px] rounded-b-md">
                <span className="font-semibold">{selectedConflict.key}:</span>
                &nbsp;
                <span>{JSON.stringify(selectedConflict.toValue, null, 2)}</span>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end gap-4">
          {selectedIndex > 0 && (
            <button
              className="btn btn-sm btn-outline"
              style={{
                ...buttonStyle,
                width: '100px',
                borderRadius: '4px',
                fontWeight: 400,
                fontSize: '12px',
                textTransform: 'none'
              }}
              onClick={() => this.handleResolveConflict(selectedIndex - 1)}
            >
              Prev
            </button>
          )}

          {!hasNextConflict && <button
            className="btn btn-sm btn-outline"
            style={{
              ...buttonStyle,
              width: '100px',
              borderRadius: '4px',
              fontWeight: 400,
              fontSize: '12px',
              textTransform: 'none'
            }}
            onClick={() => this.handleResolveConflict(selectedIndex + 1)}
            disabled={selectedIndex >= conflicts.length - 1}
          >
            Next
          </button>}

          {allResolved && <button
            className="btn btn-sm btn-primary"
            style={{
              ...buttonStyle,
              width: '100px',
              borderRadius: '4px',
              fontWeight: 400,
              fontSize: '12px',
              textTransform: 'none',
              borderWidth: '0px'
            }}
            onClick={this.handleMergeResolvedConflicts}
          >
            Merge
          </button>}
        </div>
      </div>
    );
  };
}


export default withRouter(connectToStores(ProjectHeader));
