import React, { Component } from 'react';
import { withRouter } from 'next/router';
import { reduxUtil, genericAction } from '@limegroup/lime-redux-util';
const { connectToStores } = reduxUtil;
const { sendAction } = genericAction;
import Head from 'next/head';
import { ADD_ELEMENT_TO_DIALOG, ADD_ELEMENT_TO_SELECTED_ELEMENT, GET_DIALOG, MOVE_LAYERS_PAGE_ITEMS } from '@astrakraft/project-plugin';
import { find, isEmpty } from 'lodash';
import { designerHelper, settings, projectConstants} from '@astrakraft/core-lib';


import Snackbar from '@mui/material/Snackbar';
import MuiAlert from '@mui/material/Alert';
import { getElements } from '../../themes';


import ContainerBase from '../../themes/materialui/containers/reuse-containerbase';

const { UNIT_PX } = projectConstants;
const { getNormaliseUnit, gridValueInPx } = settings;
const { getXInStage } = designerHelper;

class DialogCanvas extends Component {

  state = {
    snackbarOpen: false,
    snackbarMessage: ''
  }

  componentDidUpdate(prevProps) {
    const dialogName = this.props.router.query.dialogname;
    const { dispatch } = this.props;

    if (this.props.router.query.dialogname !== prevProps.router.query.dialogname || (!this.props.projectReducer.selectedDialog && this.props.projectReducer.selectedCommit)) {
      const selectedDialog = find(
        this.props.projectReducer.myProject.dialogsPopulated,
        { name: dialogName }
      );
      dispatch(sendAction('SET_PAGE', null));
      if (selectedDialog) {
        dispatch(sendAction(GET_DIALOG, selectedDialog.path));
      } else if (dialogName && !selectedDialog) {
        const firstDialogName = this.props.projectReducer.myProject.dialogsPopulated[0].name;
        const branchName = this.props.projectReducer?.selectedBranch?.name;
        this.props.router.push(`/projects/${this.props.projectReducer.myProject._id}/dialogs/${firstDialogName}?branch=${branchName}`);
      }
    }
  }

  render() {
    const { snackbarOpen, snackbarMessage } = this.state;
    const selectedElement = this.props.projectReducer?.selectedElement;
    const project = this.props.projectReducer.myProject || {};
    const fonts = project.assets?.fonts || [];
    const pages = project.dialogsPopulated || [];
    const dialog = this.props.projectReducer.selectedDialog;
    const selectedDialog = this.props.projectReducer.selectedDialog?.screen;
    const items = selectedDialog && selectedDialog.items || [];
    const divStyle = selectedDialog && selectedDialog.data.renderProps;
    const isElementReusable = this.props.projectReducer.myProject?.reuse?.some(obj => obj.id === selectedElement?.id);
    const clickedElement = this.props.projectReducer.doubleClickElement;

    let fontLink = '';
    if (!isEmpty(fonts)) {
      fonts.map((item) => {
        fontLink = fontLink + 'family=' + item + '&';
      });
      fontLink = fontLink.substring(0, fontLink.length - 1);
    }

    return (
      <div
        className='flex col justify-center items-center'
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: '#EDEDED',
          padding: '12px'
        }}
        onClick={this.handleWindowClick}
      >
        <Head>
          <link
            id="fontLink"
            href={`https://fonts.googleapis.com/css2?${fontLink ? fontLink : '&family=Roboto'
              }&display=swap`}
            rel="stylesheet"
          />
        </Head>
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={3000}
          onClose={this.handleSnackbarClose}
        >
          <MuiAlert
            elevation={6}
            variant="filled"
            onClose={this.handleSnackbarClose}
            severity="success"
          >
            {snackbarMessage}
          </MuiAlert>
        </Snackbar>
        <React.Fragment>
        <div className='flex col justify-center self-center' style={{ maxHeight: '100%' }}>
        {selectedDialog ?
          <ContainerBase
            unitPx={UNIT_PX}
            items={items}
            divStyle={divStyle}
            selectedScreen={selectedDialog}
            selectedDialog={dialog}
            getElementRender={this.getElementRender}
            isShowOutline={this.isShowOutline}
            onElementClick={this.onElementClick}
            onDoubleClick={this.handleDoubleClick}
            doubleClickElement={clickedElement}
            onDoubleClickPage={this.handleDoubleClickPage}
            onElementUpdate={this.onElementUpdate}
            onDeleteClick={this.deleteElement}
            onPaste={this.handlePaste}
            pages={pages}
            resolutions={selectedDialog?.resolutions}
            selectedElement={selectedElement}
            isElementReusable={isElementReusable}
            resize={this.handleResize}
            copyToSelectedPages={this.copyToSelectedDialogs}
            copyToReuse={this.copyToReuse}
            moveLayers={this.moveLayers}
            alignElement={this.alignElement}
handleKeydown={this.handleKeydown.bind(this)}
          ></ContainerBase>
          : <div className="canvas-loader">
            <div className="justify-content-center canvas-primary-loading"></div>
          </div>}
        </div>
        </React.Fragment>
      </div>
    );
  }

  handleDoubleClick = (element) => {
    const { dispatch } = this.props;
    dispatch(sendAction('DOUBLE_CLICK_ELEMENT', element));
  }

  handleDoubleClickPage = (element) => {
    const { dispatch } = this.props;
    dispatch(sendAction('DOUBLE_CLICK_PAGE', element));
  }

  handlePaste = (copiedElement, e) => {
    const { dispatch, projectReducer: { selectedElement } } = this.props;
    if (selectedElement && selectedElement.source.type === 'CONTAINER') {
      dispatch(sendAction(ADD_ELEMENT_TO_SELECTED_ELEMENT, copiedElement));
    } else {
      dispatch(sendAction(ADD_ELEMENT_TO_DIALOG, copiedElement));
    }
  };

  moveLayers = (layer) => {
    const { dispatch } = this.props;
    dispatch(sendAction(MOVE_LAYERS_PAGE_ITEMS, layer));
  };

  deleteElement = (i) => {
    const { dispatch } = this.props;
    dispatch(sendAction('DELETE_ELEMENT_FROM_DIALOG', i));
    dispatch(sendAction('ELEMENT_SELECTED', null));
  };

  handleKeydown = (element, selectedElementProps, selectedElementDataProps, selectedElementRootProps) => {
    const { dispatch } = this.props;
    const payload = {
      selectedElementProps,
      selectedElementDataProps,
      selectedElementRootProps,
      element
    };
    dispatch(sendAction('ELEMENT_PROPERTY_CHANGE', payload));
  };

  handleResize = (resizeData) => {
    const { dispatch } = this.props;
    const selectedElement = this.props.projectReducer.selectedElement;
    if (selectedElement) {
      const selectedElementProps = { ...selectedElement.data.renderProps };
      selectedElementProps.height = resizeData.height;
      selectedElementProps.width = resizeData.width;


      const selectedElementDataProps = {};
      if (typeof resizeData.x !== 'undefined') {
        selectedElementDataProps.x = resizeData.x;
      }

      if (typeof resizeData.y !== 'undefined') {
        selectedElementDataProps.y = resizeData.y;
      }

      dispatch(
        sendAction('ELEMENT_PROPERTY_CHANGE', {
          selectedElement,
          selectedElementProps,
          selectedElementDataProps
        })
      );
    }
  };

  copyToSelectedDialogs = (selectedDialogs) => {
    const { dispatch } = this.props;
    dispatch(sendAction('COPY_TO_SELECTED_DIALOGS', selectedDialogs));
  };

  copyToReuse = () => {
    const { dispatch } = this.props;
    const selectedElement = this.props.projectReducer.selectedElement;
    dispatch(sendAction('ADD_ELEMENT_TO_REUSE', selectedElement));
    this.setState({
      snackbarOpen: true,
      snackbarMessage: 'Added to Reusable Elements'
    });

  }

  updateSnackBar = () => {
    this.setState({ snackbarOpen: true, snackbarMessage: 'Added to Reusable Elements' });
  }

  getElementRender = (payload, index, resolutions) => {
    const elements = getElements(this.props.projectReducer?.myProject?.projectType);
    const resultItem = find(elements, {
      label: payload.source.label
    });

    const selectedDialog = this.props.projectReducer.selectedDialog;
    const divStyle = selectedDialog && selectedDialog.screen.data.renderProps;
    let renderComponentFn = resultItem?.renderComponent;

    let renderProps = { ...payload.data.renderProps }; // Copy renderProps to ensure it's extensible
    renderProps.groupName = this.props.groupName;
    renderProps.key = `element-render-${index}`;
    //this is when the whole component is updated like a form
    renderProps.onElementUpdate = this.onElementUpdate.bind(this, payload);
    //this is only a field is updated like the value in text input.
    renderProps.onChange = this.onElementChange;
    renderProps.updateSnackBar = this.updateSnackBar;

    if (resultItem && payload.data.id) {
      let variant = find(resultItem.variants, { id: payload.data.id });
      if (variant) {
        renderComponentFn = variant.renderComponent;
        // we pass the app them just for visual purpose. we don't store apptheme in the backend.
        renderProps = Object.assign(
          {},
          variant.renderProps,
          renderProps,
          { id: payload.id },
          { appTheme: { elements }, items: payload.items }
        );
      }
    }
    const widthValue = divStyle?.gridWidth && gridValueInPx(resolutions?.width, divStyle?.gridWidth);
    const heightValue = divStyle?.gridHeight && gridValueInPx(resolutions?.height, divStyle?.gridHeight);

    //scale width and height
    renderProps.width = getNormaliseUnit(renderProps.width, divStyle?.gridWidth ? widthValue : resolutions?.width);
    renderProps.height = getNormaliseUnit(renderProps.height, divStyle?.gridHeight ? heightValue : resolutions?.height);
    return renderComponentFn ? renderComponentFn(renderProps) : null;
  };


  onElementUpdate = (element, selectedElementProps, selectedElementDataProps, selectedElementRootProps) => {
    const { dispatch } = this.props;
    const payload = {
      selectedElementProps,
      selectedElementDataProps,
      selectedElementRootProps,
      element
    };
    dispatch(sendAction('ELEMENT_PROPERTY_CHANGE', payload));
  };

  onElementChange = (e) => {
    const { dispatch } = this.props;
    const payload = {
      selectedElementProps: {
        value: e.target.value
      },
      selectedElement: this.props.projectReducer.selectedElement
    };
    dispatch(sendAction('ELEMENT_PROPERTY_CHANGE', payload));
  }

  onElementClick = (element, e) => {
    e.stopPropagation();
    const { dispatch, projectReducer } = this.props;
    if (element.id !== projectReducer.selectedElement?.id) {
      dispatch(sendAction('ELEMENT_SELECTED', { selectedElement: element, selectedGridIndex: 0 }));
    }
  }

  isShowOutline = (p) => {
    return p.id === this.props.projectReducer.selectedElement?.id;
  }

  handleWindowClick = (event) => {
    const { dispatch } = this.props;
    dispatch(sendAction('ELEMENT_SELECTED', null));
  };

  handleSnackbarClose = () => {
    this.setState({ snackbarOpen: false });
  };

  alignElement = (alignmentType) => {
    const { dispatch } = this.props;
    const selectedDialog = this.props.projectReducer.selectedDialog;
    const selectedElement = this.props.projectReducer.selectedElement;
    const selectedScreen = selectedDialog && selectedDialog.screen;
    const containerWidth = selectedScreen.data.renderProps.gridWidth || selectedScreen.resolutions.width;
    const resolutionsWidth = selectedScreen.resolutions.width;

    const selectedElementDataProps = {
      x: getXInStage(alignmentType, selectedElement, containerWidth, resolutionsWidth, UNIT_PX)
    };

    const selectedElementProps = {
      alignHorizontal: alignmentType,
      anchorEdgeHorizontal: alignmentType
    };

    const payload = {
      selectedElementDataProps,
      selectedElementProps
    };

    dispatch(
      sendAction('ELEMENT_PROPERTY_CHANGE', payload)
    );

  }

}

export default withRouter(connectToStores(DialogCanvas));
