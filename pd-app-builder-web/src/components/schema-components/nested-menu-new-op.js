import React, { Component } from 'react';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import IconComponent from '../../utils/icon-util';
import MoreOptions from './designerProps';
import { commonProperties, utils } from '@astrakraft/core-lib';
const { sanitizeIconData } = commonProperties;
const { stringUtil, schemaUtil, paramUtil } = utils;
const { parseVariables } = stringUtil;
const { setInputValue, setInputValueToQuestion } = schemaUtil;
const { getParametersForAction } = paramUtil;

import EditableForm from '../EditableForm';
import { cloneDeep } from 'lodash';
import eventSchema from '../../views/projects/schemas/events.json';

class NestedMenuOptions extends Component {
  state = {
    options: this.props.options,
    dynamicInput: this.props.options && typeof this.props.options === 'object' ? JSON.stringify(this.props.options) : this.props.options || []
  };

  componentDidUpdate(prevProps) {
    if (this.props.optionType !== prevProps.optionType && this.props.optionType === 'dynamic') {
      let dynamicInput = [];
      if (this.props.options && typeof this.props.options === 'object') {
        try {
          dynamicInput = JSON.stringify(this.props.options);
        } catch (error) {
          console.log(error); //eslint-disable-line no-console
          dynamicInput = [];
        }
      } else {
        dynamicInput = this.props.options || [];
      }
      this.setState({ dynamicInput });
    }
  }


  handleDesignerPropsChange = (updatedProps) => {
    const newOptions = {
      designerProps: updatedProps.designerProps,
      liveData: updatedProps.liveData
    };
    this.props.onChange && this.props.onChange(newOptions); //eslint-disable-line no-unused-expressions
  };


  deleteOption = (index, e) => {
    e.stopPropagation();
    const { options } = this.state;
    const newOptions = [...options];
    newOptions.splice(index, 1);
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  addOption = (index, e) => {
    e.stopPropagation();
    const { options } = this.state;
    const newOption = {
      icon: encodeURIComponent('<i className="bi bi-plus-circle-dotted"></i>'),
      label: 'option ' + (index + 1),
      label2: 'option2 ' + (index + 1),
      label3: 'option3 ' + (index + 1),
      link: '',
      target: '_self'
    };
    const newOptions = Array.isArray(options) ? [...options] : [];
    newOptions.splice(index, 0, newOption);
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleLinkChange = (index, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], link: e.target.value };
    this.setState({ options: newOptions });
    this.props.onChange && this.props.onChange(newOptions); //eslint-disable-line no-unused-expressions
  };

  handleTargetChange = (index, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], target: e.target.value };
    this.setState({ options: newOptions });
    this.props.onChange && this.props.onChange(newOptions); //eslint-disable-line no-unused-expressions
  };


  handleTextChange = (index, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], label: e.target.value };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleText2Change = (index, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], label2: e.target.value };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleText3Change = (index, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], label3: e.target.value };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleText4Change = (index, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], label4: e.target.value };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleIconChange = (index, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], icon: encodeURIComponent(e.target.value) };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  getSchema = (option) => {
    let result = cloneDeep(eventSchema);
    const pages = this.props.pages || [];
    const actions = this.props.actions || [];
    const dialogs = this.props.dialogs || [];
    const pageOptions = pages.map((pageItem) => {
      return {
        text: pageItem.name,
        value: `${pageItem.path}`
      };
    });
    const onClickQuestion = {
      questionId: `OnActionBtnClick__onClick`,
      question: `${option.label} on Click`,
      input: {
        name: `${option.label}__onClick`,
        type: 'materialSelect',
        props: {},
        options: [
          {
            text: 'Navigate to',
            value: 'NAVIGATE_PAGE',
            conditionalQuestions: [
              {
                questionId: `OnActionBtnClick__targetPage`,
                question: 'Select the page',
                input: {
                  type: 'materialSelect',
                  props: {}
                },
                validateOn: 'blur',
                validations: [
                  {
                    type: 'isLength',
                    params: [1]
                  }
                ]
              }
            ],
            validations: [
              {
                type: 'isLength',
                params: [1]
              }
            ]
          },
          {
            text: "Show a dialog",
            value: "DIALOG",
            conditionalQuestions: [
              {
                questionId: `OnActionBtnClick__targetDialog`,
                question: "Select the dialog",
                input: {
                  type: "materialSelect",
                  props: {}
                },
                validateOn: "blur",
                validations: [
                  {
                    type: "isLength",
                    params: [
                      1
                    ]
                  }
                ]
              }
            ],
            validations: [
              {
                type: "isLength",
                params: [
                  1
                ]
              }
            ]
          },
          {
            text: "Close dialog",
            value: "CLOSE_DIALOG"
          }
        ]
      }
    };
    result.questionSets[0].questions = [onClickQuestion];
    const questions = result.questionSets[0].questions;
    questions.forEach(question => {
      question.input.options.unshift({
        text: 'None',
        value: 'null'
      });
    });

    for (const question of questions) { //eslint-disable-line no-unused-vars
      const schemaPages = question.input.options[1].conditionalQuestions[0].input.options || [];
      const parentConditionalQuestions = question.input.options[1].conditionalQuestions;

      const schemaDialogs = question.input.options[2].conditionalQuestions[0].input.options || [];
      const dialogConditionalQuestions = question.input.options[2]?.conditionalQuestions;

      for (const pageItem2 of pageOptions) { //eslint-disable-line no-unused-vars
        const found = find(schemaPages, { value: pageItem2.value });
        if (!found) {
          const pathOptions = parseVariables(pageItem2.value) || [];
          const pageItemQuestion = Object.assign({}, pageItem2);
          if (pathOptions.length) {
            for (const pathItem of pathOptions) { //eslint-disable-line no-unused-vars
              parentConditionalQuestions.push({
                questionId: `${question.questionId}__${pathItem}`,
                question: `${pathItem}`,
                input: {
                  type: 'smartTextArea',
                  props: {
                    placeholder: 'Assign value'
                  }
                }
              });
            }
          }
          schemaPages.push(pageItemQuestion);
        }
      }
      delete question.input.options[0].conditionalQuestions;
      setInputValue(questions, question.questionId, schemaPages, 'options[1].conditionalQuestions[0].input.options');

      //add action options
      const projectActions = actions.map((actionItem) => {
        const parameters = getParametersForAction(actionItem);
        return {
          text: actionItem.name,
          value: actionItem.id || actionItem.name,
          parameters
        };
      });

      const schemaActions = question.input.options || [];
      for (const actionItem of projectActions) { // eslint-disable-line no-unused-vars
        let found = find(schemaActions, { value: actionItem.id }) || find(schemaActions, { value: actionItem.value });
        if (!found) {
          const actionItemQuestion = Object.assign({}, actionItem);
          actionItemQuestion.conditionalQuestions = [
            {
              questionId: `${question.questionId}__dataField`,
              question: 'Data Field',
              input: {
                type: 'smartTextArea',
                props: {
                  placeholder: 'Assign value'
                }
              }
            }
          ];

          const actionParameters = actionItem.parameters || [];
          if (actionParameters.length) {
            for (const parameterItem of actionParameters) { // eslint-disable-line no-unused-vars
              actionItemQuestion.conditionalQuestions.push({
                questionId: `${question.questionId}__${parameterItem.name}`,
                question: `${parameterItem.name}`,
                input: {
                  type: 'smartTextArea',
                  props: {
                    placeholder: 'Assign value'
                  }
                }
              });
            }
          }

          schemaActions.push(actionItemQuestion);
        }
      }
      setInputValueToQuestion(question, schemaActions, 'options');

      // add the dialog options
      const dialogOptions = dialogs.map((dialogItem) => {
        return {
          text: dialogItem.name,
          value: dialogItem.id
        };
      });

      if (dialogConditionalQuestions) {
        for (const dialogItem2 of dialogOptions) { // eslint-disable-line no-unused-vars
          const found = find(schemaDialogs, { value: dialogItem2.value });
          if (!found) {
            const pathOptions = parseVariables(dialogItem2.value) || [];
            const pageItemQuestion = Object.assign({}, dialogItem2);
            if (pathOptions.length) {
              for (const pathItem of pathOptions) { // eslint-disable-line no-unused-vars
                dialogConditionalQuestions.push({
                  questionId: `${question.questionId}__${pathItem}`,
                  question: `${pathItem}`,
                  input: {
                    type: 'smartTextArea',
                    props: {
                      placeholder: 'Assign value'
                    }
                  }
                });
              }
            }
            schemaDialogs.push(pageItemQuestion);
          }
        }
        setInputValue(questions, question.questionId, schemaDialogs, 'options[2].conditionalQuestions[0].input.options');
      }
    }
    return result;
  };

  onUpdate = (index, selectedOption) => {
    const { options } = this.state;
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], ...selectedOption };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  onUpdateSubMenu = (parentIndex, subIndex, selectedOption) => {
    const { options } = this.state;
    const updatedOptions = [...options];
    const updatedParent = { ...updatedOptions[parentIndex] };
    const updatedSubmenu = [...updatedParent.submenu];
    updatedSubmenu[subIndex] = { ...updatedSubmenu[subIndex], ...selectedOption };

    updatedParent.submenu = updatedSubmenu;
    updatedOptions[parentIndex] = updatedParent;

    this.setState({ options: updatedOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(updatedOptions);
    });
  };

  onupdateNastedSubMenu = (parentIndex, subIndex, nestedSubIndex, selectedOption) => {
    const { options } = this.state;
    const updatedOptions = [...options];
    const updatedParent = { ...updatedOptions[parentIndex] };
    const updatedSubmenu = [...updatedParent.submenu];
    const updatedNestedSubMenu = [...updatedSubmenu[subIndex].nestedSubMenu];
    updatedNestedSubMenu[nestedSubIndex] = { ...updatedNestedSubMenu[nestedSubIndex], ...selectedOption };

    updatedSubmenu[subIndex] = { ...updatedSubmenu[subIndex], nestedSubMenu: updatedNestedSubMenu };
    updatedParent.submenu = updatedSubmenu;
    updatedOptions[parentIndex] = updatedParent;
    this.setState({ options: updatedOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(updatedOptions);
    });
  };

  addSubMenuItem = (parentIndex, e) => {
    e.stopPropagation();
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...(newOptions[parentIndex].submenu || [])];
    const newSubMenuItem = { 
      icon: '', 
      label: 'SubOption',
      label2: 'SubOption2',
      label3: 'SubOption3',
      link: '', 
      target: '' 
    };
    newSubmenu.push(newSubMenuItem);
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  addNestedSubMenu = (parentIndex, subIndex, e) => {
    if (e && typeof e.stopPropagation === 'function') {
      e.stopPropagation();
      e.preventDefault();
    }
    const newNestedSubMenuItem = {
      icon: '',
      label: 'NestedSubOption',
      label2: 'NestedSubOption2',
      label3: 'NestedSubOption3',
      link: '',
      target: ''
    };
    this.setState((prevState) => {
      const options = [...prevState.options];
      const parentOption = { ...options[parentIndex] };
      const submenu = [...(parentOption.submenu || [])];
      if (submenu[subIndex]) {
        submenu[subIndex] = {
          ...submenu[subIndex],
          nestedSubMenu: [...(submenu[subIndex].nestedSubMenu || []), newNestedSubMenuItem]
        };
      }
      parentOption.submenu = submenu;
      options[parentIndex] = parentOption;
      return { options };
    }, () => {
      if (this.props.onChange) {
        this.props.onChange(this.state.options);
      }
    });
  };

  deleteNestedSubMenu = (parentIndex, subIndex, nestedSubIndex, e) => {
    e.stopPropagation();
    const { options } = this.state;
    const newOptions = [...options];
    if (!newOptions[parentIndex] || !newOptions[parentIndex].submenu) {
      return;
    }
    const newSubmenu = [...newOptions[parentIndex].submenu];
    if (newSubmenu[subIndex] && newSubmenu[subIndex].nestedSubMenu) {
      const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
      newNestedSubMenu.splice(nestedSubIndex, 1);
      newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    }
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  deleteSubMenuItem = (parentIndex, subIndex, e) => {
    e.stopPropagation();
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu.splice(subIndex, 1);
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleSubMenuItemTextChange = (parentIndex, subIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu[subIndex] = { ...newSubmenu[subIndex], label: e.target.value };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleSubMenuItemText2Change = (parentIndex, subIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu[subIndex] = { ...newSubmenu[subIndex], label2: e.target.value };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleSubMenuItemText3Change = (parentIndex, subIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu[subIndex] = { ...newSubmenu[subIndex], label3: e.target.value };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleSubMenuItemText4Change = (parentIndex, subIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu[subIndex] = { ...newSubmenu[subIndex], label4: e.target.value };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleNestedSubMenuTextChange = (parentIndex, subIndex, nestedSubIndex, e) => {
    if (!e || !e.target) return;
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];
    const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
    newNestedSubMenu[nestedSubIndex] = { ...newNestedSubMenu[nestedSubIndex], label: e.target.value };
    newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleNestedSubMenuText2Change = (parentIndex, subIndex, nestedSubIndex, e) => {
    if (!e || !e.target) return;
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];
    const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
    newNestedSubMenu[nestedSubIndex] = { ...newNestedSubMenu[nestedSubIndex], label2: e.target.value };
    newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleNestedSubMenuText3Change = (parentIndex, subIndex, nestedSubIndex, e) => {
    if (!e || !e.target) return;
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];
    const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
    newNestedSubMenu[nestedSubIndex] = { ...newNestedSubMenu[nestedSubIndex], label3: e.target.value };
    newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleNestedSubMenuText4Change = (parentIndex, subIndex, nestedSubIndex, e) => {
    if (!e || !e.target) return;
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];
    const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
    newNestedSubMenu[nestedSubIndex] = { ...newNestedSubMenu[nestedSubIndex], label4: e.target.value };
    newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleSubMenuLinkChange = (parentIndex, subIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu[subIndex] = { ...newSubmenu[subIndex], link: e.target.value };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleNestedSubMenuLinkChange = (parentIndex, subIndex, nestedSubIndex, e) => {
    if (!e || !e.target) return;

    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];
    const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
    newNestedSubMenu[nestedSubIndex] = { ...newNestedSubMenu[nestedSubIndex], link: e.target.value };
    newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleSubMenuTargetChange = (parentIndex, subIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu[subIndex] = { ...newSubmenu[subIndex], target: e.target.value };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleNastedSubMenuTargetChange = (parentIndex, subIndex, nestedSubIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];
    const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
    newNestedSubMenu[nestedSubIndex] = { ...newNestedSubMenu[nestedSubIndex], target: e.target.value };
    newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleSubMenuItemIconChange = (parentIndex, subIndex, e) => {
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];

    newSubmenu[subIndex] = { ...newSubmenu[subIndex], icon: encodeURIComponent(e.target.value) };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };

    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  handleNestedSubMenuIconChange = (parentIndex, subIndex, nestedSubIndex, e) => {
    if (!e || !e.target) return;
    const { options } = this.state;
    const newOptions = [...options];
    const newSubmenu = [...newOptions[parentIndex].submenu];
    const newNestedSubMenu = [...newSubmenu[subIndex].nestedSubMenu];
    newNestedSubMenu[nestedSubIndex] = {
      ...newNestedSubMenu[nestedSubIndex],
      icon: e.target.value
    };
    newSubmenu[subIndex] = { ...newSubmenu[subIndex], nestedSubMenu: newNestedSubMenu };
    newOptions[parentIndex] = { ...newOptions[parentIndex], submenu: newSubmenu };
    this.setState({ options: newOptions }, () => {
      // eslint-disable-next-line no-unused-expressions
      this.props.onChange && this.props.onChange(newOptions);
    });
  };

  onDragEnd = (result) => {
    if (!result.destination) {
      return;
    }

    const { options } = this.state;
    const updatedOptions = [...options];

    if (result.type === 'options') {
      const [removed] = updatedOptions.splice(result.source.index, 1);
      updatedOptions.splice(result.destination.index, 0, removed);
    } else if (result.type === 'submenus') {
      const { source, destination } = result;
      const sourceOptionIndex = parseInt(source.droppableId, 10);
      const destinationOptionIndex = parseInt(destination.droppableId, 10);

      const sourceOption = updatedOptions[sourceOptionIndex];
      const destinationOption = updatedOptions[destinationOptionIndex];

      if (sourceOption?.submenu && destinationOption?.submenu) {
        if (source.index < sourceOption.submenu.length) {
          const [movedSubmenu] = sourceOption.submenu.splice(source.index, 1);
          destinationOption.submenu.splice(destination.index, 0, movedSubmenu);
        }
      } else if (
        sourceOption?.submenu?.[source.index]?.nestedSubMenu &&
        destinationOption?.submenu?.[destination.index]?.nestedSubMenu
      ) {
        const sourceSubmenuItem = sourceOption.submenu[source.index];

        if (sourceSubmenuItem?.nestedSubMenu?.length > source.index) {
          const [movedNestedSubmenu] = sourceSubmenuItem.nestedSubMenu.splice(source.index, 1);
          destinationOption.submenu[destination.index].nestedSubMenu.push(movedNestedSubmenu);
        }
      }
    }

    this.setState({ options: updatedOptions });
    this.props.onChange && this.props.onChange(updatedOptions);//eslint-disable-line no-unused-expressions
  };


  renderNestedSubMenu = (nestedSubmenu, parentIndex, subIndex) => {
    return (<Droppable droppableId={`${parentIndex}${subIndex}`} type="submenus">
      {(provided) => (
        <div
          style={{ display: 'flex', flexDirection: 'column', marginLeft: '20px' }}
          ref={provided.innerRef}
          {...provided.droppableProps}
        >
          {nestedSubmenu.map((nestedSubItem, nestedSubIndex) => {
            return (
              <Draggable
                key={nestedSubIndex}
                draggableId={`${parentIndex}-${subIndex}-${nestedSubIndex}`}
                index={nestedSubIndex}
                type="submenus"
              >
                {(provided) => (
                  <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                    <details key={nestedSubIndex} className="group-child [&_summary::-webkit-details-marker]:hidden" style={{ marginBottom: '7px', width: '100%' }}>
                      <summary className="flex pointer items-center justify-between rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700">
                        <div className="flex items-center">
                          <span>
                            <img
                              id="draggable-icon"
                              src={'/img/Page-Selection-drag-icon.svg'}
                              width={'12px'}
                              height={'12px'}
                            />
                          </span>
                          <span className="shrink-0 group-child-open">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-6 w-6"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </span>
                          <span className="text-sm font-medium ml-2">{nestedSubItem.label}</span>
                        </div>
                        <div>
                          <IconButton sx={{ color: '#4477CE' }} onClick={this.addNestedSubMenu.bind(this, parentIndex, subIndex, nestedSubIndex)} >
                            <IconComponent iconName="ControlPoint" sx={{ fontSize: '20px' }} />
                          </IconButton>
                          <IconButton sx={{ color: '#D80032' }} onClick={this.deleteNestedSubMenu.bind(this, parentIndex, subIndex, nestedSubIndex)}>
                            <IconComponent iconName="Delete" sx={{ fontSize: '20px' }} />
                          </IconButton>
                        </div>
                      </summary>
                      <div className="rounded-md d-flex flex-column" style={{ padding: '6px 8px 10px 8px', backgroundColor: '#F9F9F9' }}>
                        <div className="d-flex flex-column">
                          <span style={{ color: '#343f4b', fontWeight: '500', marginBottom: '3px' }}>Icon</span>
                          <div className="d-flex flex-row items-center justify-content-between" style={{ border: '1px solid #dbdbdb', borderRadius: '10px', backgroundColor: '#f1f1f1' }}>
                            <div className="d-flex flex-row justify-content-center" style={{ marginLeft: '0px', width: '35px' }}>
                              {nestedSubItem.icon && (
                                <i className={decodeURIComponent(nestedSubItem.icon).replace(/<[^>]*>/g, '')}></i>
                              )}
                            </div>
                            <input
                              type="text"
                              value={decodeURIComponent(nestedSubItem.icon)}
                              className="outline-none p-2"
                              style={{ width: '82%', borderRadius: '0px 9px 9px 0px', backgroundColor: 'white' }}
                              onChange={(e) => this.handleNestedSubMenuIconChange(parentIndex, subIndex, nestedSubIndex, e)}
                              placeholder="Enter icon class (e.g., bi bi-plus)"
                            />
                          </div>
                        </div>
                        <div className="d-flex flex-column">
                          <span style={{ color: '#343f4b', fontWeight: '500', marginBottom: '3px' }}>Text</span>
                          <input type="text" value={nestedSubItem.label} className="outline-none p-2" style={{ border: '1px solid #dbdbdb', borderRadius: '10px', backgroundColor: 'white' }} onChange={this.handleNestedSubMenuTextChange.bind(this, parentIndex, subIndex, nestedSubIndex)}
                            placeholder="Enter text" />
                        </div>
                        <div className="d-flex flex-column">
                          <span style={{ color: '#343f4b', fontWeight: '500', marginBottom: '3px' }}>Text2</span>
                          <input type="text" value={nestedSubItem.label2} className="outline-none p-2" style={{ border: '1px solid #dbdbdb', borderRadius: '10px', backgroundColor: 'white' }} onChange={this.handleNestedSubMenuText2Change.bind(this, parentIndex, subIndex, nestedSubIndex)}
                            placeholder="Enter text2" />
                        </div>
                        <div className="d-flex flex-column">
                          <span style={{ color: '#343f4b', fontWeight: '500', marginBottom: '3px' }}>Text3</span>
                          <input type="text" value={nestedSubItem.label3} className="outline-none p-2" style={{ border: '1px solid #dbdbdb', borderRadius: '10px', backgroundColor: 'white' }} onChange={this.handleNestedSubMenuText3Change.bind(this, parentIndex, subIndex, nestedSubIndex)}
                            placeholder="Enter text3" />
                        </div>
                        <div className="d-flex flex-column">
                          <span style={{ color: '#343f4b', fontWeight: '500', marginBottom: '3px' }}>Text4</span>
                          <input type="text" value={nestedSubItem.label4} className="outline-none p-2" style={{ border: '1px solid #dbdbdb', borderRadius: '10px', backgroundColor: 'white' }} onChange={this.handleNestedSubMenuText4Change.bind(this, parentIndex, subIndex, nestedSubIndex)}
                            placeholder="Enter text4" />
                        </div>
                        <div className="d-flex flex-column">
                          <span
                            style={{
                              color: '#343f4b',
                              fontWeight: '500',
                              marginBottom: '3px'
                            }}
                          >
                            Link
                          </span>
                          <input
                            type="text"
                            value={nestedSubItem.link}
                            className="outline-none p-2 bg-white"
                            style={{
                              border: '1px solid #dbdbdb',
                              borderRadius: '10px'
                            }}
                            onChange={(e) => this.handleNestedSubMenuLinkChange(parentIndex, subIndex, nestedSubIndex, e)}
                            placeholder="Enter link"
                          />
                        </div>
                        <div className="d-flex flex-column">
                          <span
                            style={{
                              color: '#343f4b',
                              fontWeight: '500',
                              marginBottom: '3px'
                            }}
                          >
                            Target
                          </span>
                          <select
                            value={nestedSubItem.target}
                            className="outline-none p-2 bg-white"
                            style={{
                              border: '1px solid #dbdbdb',
                              borderRadius: '10px'
                            }}
                            onChange={this.handleNastedSubMenuTargetChange.bind(this, parentIndex, subIndex)}
                          >
                            <option value="_self">Same Window</option>
                            <option value="_blank">New Window</option>
                          </select>
                        </div>
                      </div>
                    </details>
                  </div>
                )}
              </Draggable>
            );
          }
          )}
          {provided.placeholder}
        </div>
      )}
    </Droppable>);
  };

  renderSubMenu = (submenu, parentIndex) => {
    return (
      <Droppable droppableId={`${parentIndex}`} type="submenus">
        {(provided) => (
          <div
            style={{ display: 'flex', flexDirection: 'column', marginLeft: '20px' }}
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {submenu.map((subItem, subIndex) => {
              const questionAnswers = subItem;
              return (
                <Draggable
                  key={subIndex}
                  draggableId={`${parentIndex}-${subIndex}`}
                  index={subIndex}
                  type="submenus"
                >
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                      <details key={subIndex} className="group-child [&_summary::-webkit-details-marker]:hidden" style={{ marginBottom: '7px', width: '100%' }}>
                        <summary className="flex pointer items-center justify-between rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700">
                          <div className="flex items-center">
                            <span className="">
                              <img
                                id="draggable-icon"
                                src={'/img/Page-Selection-drag-icon.svg'}
                                width={'12px'}
                                height={'12px'}
                              />
                            </span>
                            <span className="shrink-0 group-child-open">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-6 w-6"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                            </span>
                            <span className="text-sm font-medium ml-2">{subItem.label}</span>
                          </div>
                          <div>
                            <IconButton sx={{ color: '#4477CE' }} onClick={this.addSubMenuItem.bind(this, parentIndex)}>
                              <IconComponent iconName="ControlPoint" sx={{ fontSize: '20px' }} />
                            </IconButton>
                            <IconButton sx={{ color: '#D80032' }} onClick={this.deleteSubMenuItem.bind(this, parentIndex, subIndex)}>
                              <IconComponent iconName="Delete" sx={{ fontSize: '20px' }} />
                            </IconButton>
                          </div>
                        </summary>
                        <div className="rounded-md d-flex flex-column" style={{ padding: '6px 8px 10px 8px', backgroundColor: '#F9F9F9' }}>
                          <div className="d-flex flex-column">
                            <span style={{ color: '#343f4b', fontWeight: '500', marginBottom: '3px' }}>Icon</span>
                            <div className="d-flex flex-row items-center justify-content-between" style={{ border: '1px solid #dbdbdb', borderRadius: '10px', backgroundColor: '#f1f1f1' }}>
                              <div className="d-flex flex-row justify-content-center" style={{ marginLeft: '0px', width: '35px' }}>
                                {subItem.icon && (
                                  <i className={decodeURIComponent(subItem.icon).replace(/<[^>]*>/g, '')}></i>
                                )}
                              </div>
                              <input
                                type="text"
                                value={decodeURIComponent(subItem.icon)}
                                className="outline-none p-2"
                                style={{ width: '82%', borderRadius: '0px 9px 9px 0px', backgroundColor: 'white' }}
                                onChange={this.handleSubMenuItemIconChange.bind(this, parentIndex, subIndex)}
                                placeholder="Enter icon class (e.g., bi bi-plus)"
                              />
                            </div>
                          </div>
                          <div className="d-flex flex-column">
                            <span style={{ color: '#343f4b', fontWeight: '500', marginBottom: '3px' }}>Text</span>
                            <input
                              type="text"
                              value={subItem.label}
                              className="outline-none p-2"
                              style={{
                                border: '1px solid #dbdbdb',
                                borderRadius: '10px',
                                backgroundColor: 'white'
                              }}
                              onChange={this.handleSubMenuItemTextChange.bind(this, parentIndex, subIndex)}
                              placeholder="Enter text"
                            />
                          </div>
                          <div className="d-flex flex-column">
                            <span
                              style={{
                                color: '#343f4b',
                                fontWeight: '500',
                                marginBottom: '3px'
                              }}
                            >
                              Text2
                            </span>
                            <input
                              type="text"
                              value={subItem.label2}
                              className="outline-none p-2"
                              style={{
                                border: '1px solid #dbdbdb',
                                borderRadius: '10px',
                                backgroundColor: 'white'
                              }}
                              onChange={this.handleSubMenuItemText2Change.bind(this, parentIndex, subIndex)}
                              placeholder="Enter text2"
                            />
                          </div>
                          <div className="d-flex flex-column">
                            <span
                              style={{
                                color: '#343f4b',
                                fontWeight: '500',
                                marginBottom: '3px'
                              }}
                            >
                              Text3
                            </span>
                            <input
                              type="text"
                              value={subItem.label3}
                              className="outline-none p-2"
                              style={{
                                border: '1px solid #dbdbdb',
                                borderRadius: '10px',
                                backgroundColor: 'white'
                              }}
                              onChange={this.handleSubMenuItemText3Change.bind(this, parentIndex, subIndex)}
                              placeholder="Enter text3"
                            />
                          </div>
                          <div className="d-flex flex-column">
                            <span
                              style={{
                                color: '#343f4b',
                                fontWeight: '500',
                                marginBottom: '3px'
                              }}
                            >
                              Text4
                            </span>
                            <input
                              type="text"
                              value={subItem.label4}
                              className="outline-none p-2"
                              style={{
                                border: '1px solid #dbdbdb',
                                borderRadius: '10px',
                                backgroundColor: 'white'
                              }}
                              onChange={this.handleSubMenuItemText4Change.bind(this, parentIndex, subIndex)}
                              placeholder="Enter text4"
                            />
                          </div>
                          <div className="d-flex flex-column">
                            <span
                              style={{
                                color: '#343f4b',
                                fontWeight: '500',
                                marginBottom: '3px'
                              }}
                            >
                              Link
                            </span>
                            <input
                              type="text"
                              value={subItem.link}
                              className="outline-none p-2 bg-white"
                              style={{
                                border: '1px solid #dbdbdb',
                                borderRadius: '10px'
                              }}
                              onChange={this.handleSubMenuLinkChange.bind(this, parentIndex, subIndex)}
                              placeholder="Enter link"
                            />
                          </div>
                          <div className="d-flex flex-column">
                            <span
                              style={{
                                color: '#343f4b',
                                fontWeight: '500',
                                marginBottom: '3px'
                              }}
                            >
                              Target
                            </span>
                            <select
                              value={subItem.target}
                              className="outline-none p-2 bg-white"
                              style={{
                                border: '1px solid #dbdbdb',
                                borderRadius: '10px'
                              }}
                              onChange={this.handleSubMenuTargetChange.bind(this, parentIndex, subIndex)}
                            >
                              <option value="_self">Same Window</option>
                              <option value="_blank">New Window</option>
                            </select>
                          </div>
                          <div className="d-flex flex-column mt-1">
                            <EditableForm
                              data={this.getSchema(subItem)}
                              key={`${subItem.label}-${subIndex}`}
                              questionAnswers={questionAnswers}
                              dispatch={this.props.dispatch}
                              onUpdate={this.onUpdateSubMenu.bind(this, parentIndex, subIndex)}
                            />
                          </div>
                          {!subItem?.nestedSubMenu?.length && <div className="d-flex justify-content-center">
                            <button style={{ textTransform: 'none', color: '#326df6', fontWeight: '600' }} onClick={this.addNestedSubMenu.bind(this, parentIndex, subIndex)}>
                              <i className="bi bi-plus-circle mr-2"></i>Add nested sub-menu {subItem?.nestedSubMenu?.length}
                            </button>
                          </div>}
                          {subItem?.nestedSubMenu && this.renderNestedSubMenu(subItem.nestedSubMenu, parentIndex, subIndex)}
                        </div>
                      </details>
                    </div>
                  )}
                </Draggable>
              );
            }
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    );
  };

  render() {
    const { options } = this.state;
    const { optionType, selectedElement } = this.props;

    return (
      <DragDropContext onDragEnd={this.onDragEnd}>
        <Droppable droppableId="nestedOptionsList" type="options">
          {(provided) => (
            <div
              style={{ display: 'flex', flexDirection: 'column' }}
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {optionType === 'static' ? (
                options && options.length > 0 && Array.isArray(options) ? (options.map((option, index) => {
                  const questionAnswers = option;
                  return (
                    <Draggable key={index} draggableId={index.toString()} index={index} type="options">
                      {(provided) => (
                        <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                          <details className="group [&_summary::-webkit-details-marker]:hidden" style={{ marginBottom: '7px', width: '100%' }}>
                            <summary className="flex pointer items-center justify-between rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700">
                              <div className="flex items-center">
                                <span className="">
                                  <img
                                    id="draggable-icon"
                                    src={
                                      '/img/Page-Selection-drag-icon.svg'
                                    }
                                    width={'12px'}
                                    height={'12px'}
                                  />
                                </span>
                                <span className="shrink-0 transition duration-300 group-open:-rotate-180">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                                    <path
                                      fillRule="evenodd"
                                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                      clip-rule="evenodd"
                                    />
                                  </svg>
                                </span>
                                <span className="text-sm font-medium ml-2 break-words">{option.label}</span>
                              </div>
                              <div className='flex items-center'>
                                <IconButton sx={{ color: '#4477CE' }} onClick={this.addOption.bind(this, index + 1)}>
                                  <IconComponent iconName="ControlPoint" sx={{ fontSize: '20px' }} />
                                </IconButton>
                                <IconButton sx={{ color: '#D80032' }} onClick={this.deleteOption.bind(this, index)}>
                                  <IconComponent iconName="Delete" sx={{ fontSize: '20px' }} />
                                </IconButton>
                              </div>
                            </summary>
                            <div
                              className="rounded-md d-flex flex-column"
                              style={{
                                padding: '6px 8px 10px 8px',
                                backgroundColor: '#F9F9F9'
                              }}
                            >
                              <div className="d-flex flex-column">
                                <span
                                  style={{
                                    color: '#343f4b',
                                    fontWeight: '500',
                                    marginBottom: '3px'
                                  }}
                                >
                                  Icon
                                </span>
                                <div
                                  className="d-flex flex-row items-center justify-content-between"
                                  style={{
                                    border: '1px solid #dbdbdb',
                                    borderRadius: '10px',
                                    backgroundColor: '#f1f1f1'
                                  }}
                                >
                                  <div
                                    dangerouslySetInnerHTML={sanitizeIconData(decodeURIComponent(option.icon))}
                                    className="d-flex flex-row justify-content-center"
                                    style={{ marginLeft: '0px', width: '35px' }}
                                  />
                                  <input
                                    type="text"
                                    value={decodeURIComponent(option.icon)}
                                    className="outline-none p-2"
                                    style={{
                                      width: '82%',
                                      borderRadius: '0px 9px 9px 0px',
                                      backgroundColor: 'white'
                                    }}
                                    onChange={this.handleIconChange.bind(this, index)}
                                    placeholder="Enter icon tag"
                                  />
                                </div>
                              </div>
                              <div className="d-flex flex-column">
                                <span
                                  style={{
                                    color: '#343f4b',
                                    fontWeight: '500',
                                    marginBottom: '3px'
                                  }}
                                >
                                  Text
                                </span>
                                <input
                                  type="text"
                                  value={option.label}
                                  className="outline-none p-2"
                                  style={{
                                    border: '1px solid #dbdbdb',
                                    borderRadius: '10px',
                                    backgroundColor: 'white'
                                  }}
                                  onChange={this.handleTextChange.bind(this, index)}
                                  placeholder="Enter text"
                                />
                              </div>
                              <div className="d-flex flex-column">
                                <span
                                  style={{
                                    color: '#343f4b',
                                    fontWeight: '500',
                                    marginBottom: '3px'
                                  }}
                                >
                                  Text2
                                </span>
                                <input
                                  type="text"
                                  value={option.label2}
                                  className="outline-none p-2"
                                  style={{
                                    border: '1px solid #dbdbdb',
                                    borderRadius: '10px',
                                    backgroundColor: 'white'
                                  }}
                                  onChange={this.handleText2Change.bind(this, index)}
                                  placeholder="Enter text2"
                                />
                              </div>
                              <div className="d-flex flex-column">
                                <span
                                  style={{
                                    color: '#343f4b',
                                    fontWeight: '500',
                                    marginBottom: '3px'
                                  }}
                                >
                                  Text3
                                </span>
                                <input
                                  type="text"
                                  value={option.label3}
                                  className="outline-none p-2"
                                  style={{
                                    border: '1px solid #dbdbdb',
                                    borderRadius: '10px',
                                    backgroundColor: 'white'
                                  }}
                                  onChange={this.handleText3Change.bind(this, index)}
                                  placeholder="Enter text3"
                                />
                              </div>
                              <div className="d-flex flex-column">
                                <span
                                  style={{
                                    color: '#343f4b',
                                    fontWeight: '500',
                                    marginBottom: '3px'
                                  }}
                                >
                                  Text4
                                </span>
                                <input
                                  type="text"
                                  value={option.label4}
                                  className="outline-none p-2"
                                  style={{
                                    border: '1px solid #dbdbdb',
                                    borderRadius: '10px',
                                    backgroundColor: 'white'
                                  }}
                                  onChange={this.handleText4Change.bind(this, index)}
                                  placeholder="Enter text4"
                                />
                              </div>
                              <div className="d-flex flex-column">
                                <span
                                  style={{
                                    color: '#343f4b',
                                    fontWeight: '500',
                                    marginBottom: '3px'
                                  }}
                                >
                                  Link
                                </span>
                                <input
                                  type="text"
                                  value={option.link}
                                  className="outline-none p-2 bg-white"
                                  style={{
                                    border: '1px solid #dbdbdb',
                                    borderRadius: '10px'
                                  }}
                                  onChange={this.handleLinkChange.bind(this, index)}
                                  placeholder="Enter link"
                                />
                              </div>
                              <div className="d-flex flex-column">
                                <span
                                  style={{
                                    color: '#343f4b',
                                    fontWeight: '500',
                                    marginBottom: '3px'
                                  }}
                                >
                                  Target
                                </span>
                                <select
                                  value={option.target}
                                  className="outline-none p-2 bg-white"
                                  style={{
                                    border: '1px solid #dbdbdb',
                                    borderRadius: '10px'
                                  }}
                                  onChange={this.handleTargetChange.bind(this, index)}
                                >
                                  <option value="_self">Same Window</option>
                                  <option value="_blank">New Window</option>
                                </select>
                              </div>
                              <div className="d-flex flex-column mt-1">
                                <EditableForm
                                  data={this.getSchema(option)}
                                  key={`${option.label}-${index}`}
                                  questionAnswers={questionAnswers}
                                  dispatch={this.props.dispatch}
                                  onUpdate={this.onUpdate.bind(this, index)}
                                />
                              </div>
                              {!option?.submenu?.length && <div className="d-flex justify-content-center">
                                <button style={{ textTransform: 'none', color: '#326df6', fontWeight: '600' }} onClick={this.addSubMenuItem.bind(this, index)}>
                                  <i className="bi bi-plus-circle mr-2"></i>Add sub-menu
                                </button>
                              </div>}
                            </div>
                            {option.submenu && this.renderSubMenu(option.submenu, index)}
                          </details>
                        </div>
                      )}
                    </Draggable>
                  );
                })) : (
                  <div className="d-flex justify-content-end">
                    <Button style={{ textTransform: 'none' }} onClick={this.addOption.bind(this, 0)}>
                      <i className="bi bi-plus-circle mr-2"></i>Add options
                    </Button>
                  </div>
                )
              ) : (
                <>
                  <MoreOptions
                    selectedElement={selectedElement}
                    onChange={this.handleDesignerPropsChange}
                  />
                </>
              )}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    );
  }
}

module.exports = NestedMenuOptions;
