import React, { useState, useRef, useCallback, useEffect } from 'react';
import ReactDOM from 'react-dom';

import IconComponent from '../../../utils/icon-util';
import Tooltip from '@mui/material/Tooltip';
import Popover from '@mui/material/Popover';

import Combobox from './combo-box';
import { utils } from '@astrakraft/core-lib';
const { ariaUtils } = utils;
const { ariaKeyPress } = ariaUtils;

const ElementResize = (props) => {
  const {
    children,
    resizeData,
    showOutline,
    onResize,
    onResizeStop,
    unitPx,
    pages: pageList = [],
    selectedPage,
    selectedDialog,
    selectedElement,
    gridCount,
    triggerSpinner
    // sideNavPopupOpen
  } = props;

  const resizableRef = useRef(null);
  const dragInitCoordinatesRef = useRef({ x: 0, y: 0 });
  const [popoverAnchor, setPopoverAnchor] = useState(null);
  const [dropdownOptions, setDropdownOptions] = useState([]);

  // Simple state for toolbar position
  const [toolbarPosition, setToolbarPosition] = useState({
    x: 600,
    y: 500
  });
  const dragOffset = useRef({ x: 0, y: 0 });
  const toolbarRef = useRef(null);
  const canvasRef = useRef(null);

  // Add effect to find and store canvas reference
  useEffect(() => {
    const findCanvas = () => {
      const canvas = document.querySelector('div.dnd__wrapper');
      const canvasById = document.getElementById('dnd__wrapper');
      const foundCanvas = canvasById || canvas;
      if (foundCanvas) {
        canvasRef.current = foundCanvas;
        return true;
      }
      return false;
    };

    if (!findCanvas()) {
      const retryInterval = setInterval(() => {
        if (findCanvas()) {
          clearInterval(retryInterval);
        }
      }, 100);

      // Clean up the interval
      return () => clearInterval(retryInterval);
    }

    return undefined;
  }, []);

  // Create toolbar container
  useEffect(() => {
    // Create or access the toolbar container div
    let toolbarContainer = document.getElementById('canvas-toolbar-container');
    if (!toolbarContainer && canvasRef.current) {
      toolbarContainer = document.createElement('div');
      toolbarContainer.id = 'canvas-toolbar-container';
      // Position it absolutely within the canvas
      toolbarContainer.style.position = 'absolute';
      toolbarContainer.style.top = '0';
      toolbarContainer.style.left = '0';
      toolbarContainer.style.width = '100%';
      toolbarContainer.style.height = '100%';
      toolbarContainer.style.pointerEvents = 'none';
      toolbarContainer.style.zIndex = '9000';
      canvasRef.current.appendChild(toolbarContainer);
    }

    return () => {
      // Clean up if needed when component unmounts
      if (!document.getElementById('element-resize-active')) {
        const container = document.getElementById('canvas-toolbar-container');
        if (container && container.parentNode) {
          container.parentNode.removeChild(container);
        }
      }
    };
  }, [canvasRef.current]);

  const snapToGrid = (value) => {
    return Math.ceil(value / unitPx) * unitPx;
  };

  const getStepMove = (value) => {
    return Math.ceil(value / unitPx);
  };

  const getNewLength = (originalLength, newLength, cellLength) => {
    // Enforce minimum width of 20px
    const minPixelLength = 20;

    if (newLength < minPixelLength) {
      return `${minPixelLength}px`;
    }

    return originalLength.includes('%') && cellLength ? `${((newLength / unitPx) * 100) / cellLength}%` : `${newLength}px`;
  };

  const handleResize = useCallback(
    (e, direction) => {
      const { width, height, originalWidth, originalHeight } = resizeData;
      const offsetWidth = parseInt(width, 10);
      const offsetHeight = parseInt(height, 10);

      const { horizontalCellLength, verticalCellLength } = gridCount || {};


      let diffMoveX = 0,
        diffMoveY = 0;

      switch (direction) {
        case 'right': {
          diffMoveX = e.pageX - dragInitCoordinatesRef.current.x;
          const newWidthRight = snapToGrid(offsetWidth + diffMoveX);
          onResize && //eslint-disable-line no-unused-expressions
            onResize({
              width: getNewLength(originalWidth, newWidthRight, horizontalCellLength),
              height: resizeData.originalHeight
            });
          break;
        }
        case 'bottom': {
          diffMoveY = e.pageY - dragInitCoordinatesRef.current.y;
          const newHeightBottom = snapToGrid(offsetHeight + diffMoveY);
          onResize && //eslint-disable-line no-unused-expressions
            onResize({
              width: resizeData.originalWidth,
              height: getNewLength(originalHeight, newHeightBottom, verticalCellLength)
            });
          break;
        }
        case 'bottom-right': {
          diffMoveX = e.pageX - dragInitCoordinatesRef.current.x;
          diffMoveY = e.pageY - dragInitCoordinatesRef.current.y;
          const newWidthBottomRight = snapToGrid(offsetWidth + diffMoveX);
          const newHeightBottomRight = snapToGrid(offsetHeight + diffMoveY);
          onResize && //eslint-disable-line no-unused-expressions
            onResize({
              width: getNewLength(originalWidth, newWidthBottomRight, horizontalCellLength),
              height: getNewLength(originalHeight, newHeightBottomRight, verticalCellLength)
            });
          break;
        }
        case 'top': {
          const positionY = resizeData.y;
          diffMoveY = dragInitCoordinatesRef.current.y - e.pageY;
          const stepY = getStepMove(diffMoveY);
          const resolvedY = positionY - stepY;
          const finalY = Math.max(resolvedY, 0);
          if (resolvedY >= 0) {
            const newHeightTop = snapToGrid(offsetHeight + diffMoveY);
            onResize && //eslint-disable-line no-unused-expressions
              onResize({
                width: resizeData.originalWidth,
                height: getNewLength(originalHeight, newHeightTop, verticalCellLength),
                y: finalY
              });
          }
          break;
        }
        case 'left': {
          const positionX = resizeData.x;
          diffMoveX = dragInitCoordinatesRef.current.x - e.pageX;
          const stepX = getStepMove(diffMoveX);
          const resolvedX = positionX - stepX;
          const finalX = Math.max(resolvedX, 0);
          if (resolvedX >= 0) {
            const newWidthLeft = snapToGrid(offsetWidth + diffMoveX);
            onResize && //eslint-disable-line no-unused-expressions
              onResize({
                width: getNewLength(originalWidth, newWidthLeft, horizontalCellLength),
                height: resizeData.originalHeight,
                x: finalX
              });
          }
          break;
        }
        case 'bottom-left': {
          diffMoveX = dragInitCoordinatesRef.current.x - e.pageX;
          diffMoveY = e.pageY - dragInitCoordinatesRef.current.y;
          const positionXBottomLeft = resizeData.x;
          const stepXBottomLeft = getStepMove(diffMoveX);
          const resolvedXBottomLeft = positionXBottomLeft - stepXBottomLeft;
          const finalXBottomLeft = Math.max(resolvedXBottomLeft, 0);

          if (resolvedXBottomLeft >= 0) {
            const newWidthBottomLeft = snapToGrid(offsetWidth + diffMoveX);
            const newHeightBottomLeft = snapToGrid(offsetHeight + diffMoveY);
            onResize && //eslint-disable-line no-unused-expressions
              onResize({
                width: getNewLength(originalWidth, newWidthBottomLeft, horizontalCellLength),
                height: getNewLength(originalHeight, newHeightBottomLeft, verticalCellLength),
                x: finalXBottomLeft
              });
          }
          break;
        }

        case 'top-left': {
          diffMoveX = dragInitCoordinatesRef.current.x - e.pageX;
          diffMoveY = dragInitCoordinatesRef.current.y - e.pageY;
          const positionXTopLeft = resizeData.x;
          const stepXTopLeft = getStepMove(diffMoveX);
          const resolvedXTopLeft = positionXTopLeft - stepXTopLeft;
          const finalXTopLeft = Math.max(resolvedXTopLeft, 0);

          const positionYTopLeft = resizeData.y;
          const stepYTopLeft = getStepMove(diffMoveY);
          const resolvedYTopLeft = positionYTopLeft - stepYTopLeft;
          const finalYTopLeft = Math.max(resolvedYTopLeft, 0);

          if (resolvedXTopLeft >= 0 && resolvedYTopLeft >= 0) {
            const newWidthTopLeft = snapToGrid(offsetWidth + diffMoveX);
            const newHeightTopLeft = snapToGrid(offsetHeight + diffMoveY);
            onResize && //eslint-disable-line no-unused-expressions
              onResize({
                width: getNewLength(originalWidth, newWidthTopLeft, horizontalCellLength),
                height: getNewLength(originalHeight, newHeightTopLeft, verticalCellLength),
                x: finalXTopLeft,
                y: finalYTopLeft
              });
          }
          break;
        }

        case 'top-right': {
          diffMoveX = e.pageX - dragInitCoordinatesRef.current.x;
          diffMoveY = dragInitCoordinatesRef.current.y - e.pageY;

          const stepYTopRight = getStepMove(diffMoveY);
          const resolvedYTopRight = resizeData.y - stepYTopRight;
          const finalYTopRight = Math.max(resolvedYTopRight, 0);

          if (resolvedYTopRight >= 0) {
            const newWidthTopRight = snapToGrid(offsetWidth + diffMoveX);
            const newHeightTopRight = snapToGrid(offsetHeight + diffMoveY);
            onResize && //eslint-disable-line no-unused-expressions
              onResize({
                width: getNewLength(originalWidth, newWidthTopRight, horizontalCellLength),
                height: getNewLength(originalHeight, newHeightTopRight, verticalCellLength),
                y: finalYTopRight
              });
          }
          break;
        }
        // Add cases for other handles as needed
        default:
          break;
      }
    },
    [resizeData]
  );

  const startResize = (e, direction) => {
    e.stopPropagation();
    e.preventDefault();
    dragInitCoordinatesRef.current = { x: e.pageX, y: e.pageY };

    const onMouseMove = (event) => handleResize(event, direction);
    const onMouseUp = () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      // onResize && onResize(dimensions.width, dimensions.height);
      onResizeStop && onResizeStop(e); //eslint-disable-line no-unused-expressions
    };
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  };

  const ensureToolbarContainerExists = () => {
    const existing = document.getElementById('canvas-toolbar-container');
    if (!existing && canvasRef.current) {
      const container = document.createElement('div');
      container.id = 'canvas-toolbar-container';
      Object.assign(container.style, {
        position: 'absolute',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: '9000'
      });
      canvasRef.current.appendChild(container);
    }
  };


  useEffect(() => {
    if (!canvasRef.current) return;

    const observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) { //eslint-disable-line no-unused-vars
        if (
          mutation.type === 'childList' &&
          !document.getElementById('canvas-toolbar-container')
        ) {
          // Toolbar container was removed — recreate it
          ensureToolbarContainerExists();
        }
      }
    });

    observer.observe(canvasRef.current, {
      childList: true,
      subtree: false // we only care about direct children of the canvas
    });

     return () => observer.disconnect(); //eslint-disable-line consistent-return

  }, [canvasRef.current]);

  const deleteElement = () => {
    props.onDeleteClick && props.onDeleteClick(); // eslint-disable-line no-unused-expressions
  };

  const handleMoreOptions = (event) => {
    setPopoverAnchor(event.currentTarget);
  };

  const handleOptionsClose = () => {
    setPopoverAnchor(null);
    setDropdownOptions([]);
  };

  const handleSelectAll = () => {
    const pageList = props.pages.filter((each) => {
      if (props.selectedPage) {
        return each.id !== props.selectedPage.id;
      } else if (props.selectedDialog) {
        return each.id !== props.selectedDialog.id;
      }
      return true;
    });
    const selectedPageIds = pageList.map((item) => item.id);
    if (dropdownOptions.length === pageList.length) {
      setDropdownOptions([]);
    } else {
      setDropdownOptions(selectedPageIds);
    }
  };

  const handleDropdownChange = (event) => {
    const { value, checked } = event.target;
    let newOptions = [...dropdownOptions];

    if (checked) {
      newOptions.push(value);
    } else {
      newOptions = newOptions.filter((option) => option !== value);
    }
    setDropdownOptions(newOptions);
  };

  const handleSave = () => {
    props.copyToSelectedPages && props.copyToSelectedPages(dropdownOptions); // eslint-disable-line no-unused-expressions
    setDropdownOptions([]);
    setPopoverAnchor(null);
  };

  const copyToReuseClick = () => {
    props.copyToReuse && props.copyToReuse(); //eslint-disable-line no-unused-expressions
  };

  const moveLayersClick = (moveType) => {
    props.moveLayers && props.moveLayers(moveType); //eslint-disable-line no-unused-expressions
  };

  const alignElement = (alignmentType) => {
    props.alignElement && props.alignElement(alignmentType); //eslint-disable-line no-unused-expressions
  };

  const getAlignmentColor = (alignmentType) => {
    const selectedAlignment = selectedElement?.data.renderProps.alignHorizontal;
    return selectedAlignment === alignmentType ? `hover:text-sky-600 text-sky-500` : `hover:text-sky-600 text-gray-400`;
  };

  const containerSelectStyle = {
    border: '2px solid blue'
  };

  const containerDefaultStyle = {
    width: resizeData.width === '0px' || resizeData.width === '0%' ? '20px' : resizeData.width,
    height: resizeData.height,
    position: 'relative',
    display: 'inline-block',
    minWidth: '20px' // Ensure minimum width even if width is set to 0
  };

  const containerStyle = showOutline
    ? { ...containerDefaultStyle, ...containerSelectStyle }
    : { ...containerDefaultStyle };

  const open = Boolean(popoverAnchor);
  const id = open ? 'simple-popover' : undefined;

  const generatePixelOptions = (width) => {
    // Ensure minimum width is 20px
    const minWidth = 20;
    const min = Math.max(minWidth, width - 100);
    const max = width + 100;
    const step = 20;
    const options = [];
    for (let i = min; i <= max; i += step) {
      options.push(`${i}px`);
    }
    return options;
  };
  const percentageWidthOptions = ['25%', '50%', '75%', '100%'];
  const pixelWidthOptions = generatePixelOptions(parseInt(selectedElement?.data?.renderProps?.width, 10));

  const handleWidthChange = (e, newWidth) => {
    // Enforce minimum width of 20px for elements to prevent them becoming unclickable
    let enforcedWidth = newWidth;

    if (newWidth.includes('px')) {
      const numericWidth = parseInt(newWidth, 10);
      if (numericWidth < 20) {
        enforcedWidth = '20px';
      }
    } else if (newWidth === '0%') {
      enforcedWidth = '20px';
    }

    const newResizeData = {
      ...resizeData,
      width: enforcedWidth
    };

    onResize && onResize(newResizeData); //eslint-disable-line no-unused-expressions
    onResizeStop && onResizeStop(e); //eslint-disable-line no-unused-expressions
  };

  // Checks if the target is an interactive element that should not trigger dragging
  const isInteractiveElement = (target) => {
    // Check tag names
    if (['BUTTON', 'SVG', 'PATH', 'CIRCLE', 'INPUT'].includes(target.tagName)) {
      return true;
    }

    // Check closest selectors
    if (
      target.closest('[role="tooltip"]') ||
      target.closest('.MuiPopover-root') ||
      target.closest('span[style*="width: 130px"]') || // Width control
      target.closest('.MuiTooltip-popper')
    ) {
      return true;
    }

    // Check class lists
    if (
      target.classList.contains('hover:text-sky-600') ||
      target.classList.contains('text-gray-400')
    ) {
      return true;
    }

    // Check attributes
    if (target.getAttribute && target.getAttribute('role') === 'button') {
      return true;
    }

    return false;
  };

  const startToolbarDrag = (e) => {
    // Ignore if clicking on buttons or tooltips or any interactive element
    if (isInteractiveElement(e.target)) {
      return;
    }

    e.stopPropagation();
    e.preventDefault();

    if (!canvasRef.current || !toolbarRef.current) return;

    const toolbarRect = toolbarRef.current.getBoundingClientRect();

    // Calculate the offset between mouse position and toolbar center
    dragOffset.current = {
      x: e.clientX - toolbarRect.left - (toolbarRect.width / 2),
      y: e.clientY - toolbarRect.top - (toolbarRect.height / 2)
    };

    const handleMouseMove = (event) => {
      if (!canvasRef.current) return;

      // Get updated canvas position (it might move if container scrolls)
      const canvas = canvasRef.current.getBoundingClientRect();

      // Calculate new position relative to canvas
      const newX = event.clientX - canvas.left - dragOffset.current.x;
      const newY = event.clientY - canvas.top - dragOffset.current.y;

      // Toolbar dimensions (half width/height needed for bounds checking)
      const toolbarHalfWidth = toolbarRef.current ? toolbarRef.current.offsetWidth / 2 : 150;
      const toolbarHalfHeight = toolbarRef.current ? toolbarRef.current.offsetHeight / 2 : 30;

      // Keep toolbar fully within canvas bounds, accounting for the transform(-50%, -50%)
      const boundedX = Math.max(
        toolbarHalfWidth,
        Math.min(canvas.width - toolbarHalfWidth, newX)
      );

      const boundedY = Math.max(
        toolbarHalfHeight,
        Math.min(canvas.height - toolbarHalfHeight, newY)
      );

      // Update position state - these are canvas-relative coordinates
      setToolbarPosition({
        x: boundedX,
        y: boundedY
      });
    };

    const handleMouseUp = () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
  };

  // Create toolbar component that will be rendered within the canvas
  const Toolbar = () => {
    // Only render toolbar if an element is selected and canvas is found
    if (!showOutline || !canvasRef.current) return null;

    const container = document.getElementById('canvas-toolbar-container');
    if (!container) return null;

    return ReactDOM.createPortal(
      <div
        ref={toolbarRef}
        id="element-resize-active"
        style={{
          position: 'absolute',
          zIndex: 9999,
          left: `${toolbarPosition.x}px`,
          top: `${toolbarPosition.y}px`,
          transform: 'translate(-50%, -50%)',
          transition: 'none',
          touchAction: 'none',
          userSelect: 'none',
          pointerEvents: 'auto' // Ensure it can receive pointer events
        }}
      >
        <div
          className="indicator-item indicator-top-end rounded-full shadow-lg p-2 backdrop-blur-sm bg-slate-900 w-auto right-auto"
          style={{
            display: 'flex',
            alignItems: 'center'
          }}
          onMouseDown={startToolbarDrag}
        >
          <IconComponent iconName="DragIndicator" style={{
            fontSize: '20px',
            marginRight: '10px',
            cursor: 'grab',
            pointerEvents: 'none' // Make drag icon non-interactive
          }}
            className="hover:text-sky-600 text-gray-400" />
          <span className="px-3 text-cyan-500 font-semibold text-sm">Settings</span>
          <Tooltip title="Delete" arrow placement="top">
            <IconComponent iconName="DeleteOutline"
              //eslint-disable-next-line react/jsx-no-bind
              onClick={deleteElement}
              style={{
                fontSize: '20px',
                marginLeft: '16px',
                cursor: 'pointer'
              }}
              className="hover:text-sky-600 text-gray-400"
            />
          </Tooltip>
          {((selectedPage && selectedPage?.name !== '_Theme') || selectedDialog) && (
            <Tooltip title={selectedDialog ? "Copy to selected dialogs" : "Copy to selected pages"} arrow placement="top">
              <IconComponent iconName="SelectAll"
                style={{
                  fontSize: '20px',
                  marginLeft: '10px',
                  cursor: 'pointer'
                }}
                className="hover:text-sky-600 text-gray-400"
                onClick={handleMoreOptions} //eslint-disable-line react/jsx-no-bind
              />
            </Tooltip>
          )}
          {((selectedPage && selectedPage?.name !== '_Theme') || selectedDialog) && (
            <Tooltip title="Add to theme" arrow placement="top">
              <IconComponent iconName="Reuse"
                style={{
                  fontSize: '20px',
                  marginLeft: '10px',
                  cursor: 'pointer'
                }}
                className="hover:text-sky-600 text-gray-400"
                //eslint-disable-next-line react/jsx-no-bind
                onClick={copyToReuseClick} // eslint-disable-line no-unused-expressions
              />
            </Tooltip>
          )}
          <Tooltip title="move to back" arrow placement="top">
            <IconComponent
              iconName="KeyboardDoubleArrowUp"
              style={{
                fontSize: '20px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              className="hover:text-sky-600 text-gray-400"
              onClick={moveLayersClick.bind(this, 'moveToback')}
            />
          </Tooltip>
          <Tooltip title="move to front" arrow placement="top">
            <IconComponent
              iconName="KeyboardDoubleArrowDown"
              style={{
                fontSize: '20px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              className="hover:text-sky-600 text-gray-400"
              onClick={moveLayersClick.bind(this, 'moveToFront')}
            />
          </Tooltip>
          <Tooltip title="shift one layer back" arrow placement="top">
            <IconComponent
              iconName="North"
              style={{
                fontSize: '20px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              className="hover:text-sky-600 text-gray-400"
              onClick={moveLayersClick.bind(this, 'shiftOneLayerBack')}
            />
          </Tooltip>
          <Tooltip title="shift one layer front" arrow placement="top">
            <IconComponent
              iconName="South"
              style={{
                fontSize: '20px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              className="hover:text-sky-600 text-gray-400"
              onClick={moveLayersClick.bind(this, 'shiftOneLayerFront')}
            />
          </Tooltip>

          <Tooltip title="Align left" arrow placement="top">
            <IconComponent
              iconName="FormatAlignLeft"
              style={{
                fontSize: '20px',
                marginLeft: '40px',
                cursor: 'pointer'
              }}
              className={getAlignmentColor('left')}
              onClick={alignElement.bind(this, 'left')}
            />
          </Tooltip>

          <Tooltip title="Align middle" arrow placement="top">
            <IconComponent
              iconName="FormatAlignCenter"
              style={{
                fontSize: '20px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              className={getAlignmentColor('center')}
              onClick={alignElement.bind(this, 'center')}
            />
          </Tooltip>

          <Tooltip title="Align right" arrow placement="top">
            <IconComponent
              iconName="FormatAlignRight"
              style={{
                fontSize: '20px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              className={getAlignmentColor('right')}
              onClick={alignElement.bind(this, 'right')}
            />
          </Tooltip>
          <Tooltip title="Width" arrow placement="top">
            <span
              style={{
                marginLeft: '30px',
                width: '130px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                pointerEvents: 'auto' // Ensure the width control is interactive
              }}
              onClick={(e) => e.stopPropagation()} // Prevent click propagation
            >
              <Combobox
                value={selectedElement.data.renderProps.width}
                onChange={handleWidthChange} //eslint-disable-line react/jsx-no-bind
                options={[
                  percentageWidthOptions, // first tab options
                  pixelWidthOptions // second tab options
                ]}
                tabs={['%', 'Px']}
                label="W :"
                width="130px"
                height="40px"
              />
            </span>
          </Tooltip>
          <Tooltip title="Trigger loading" arrow placement="top">
            <IconComponent
              iconName="SettingsBackupRestore"
              style={{
                fontSize: '20px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              className="hover:text-sky-600 text-gray-400"
              onClick={triggerSpinner}
            />
          </Tooltip>
          <Popover
            id={id}
            open={open}
            anchorEl={popoverAnchor}
            onClose={handleOptionsClose} //eslint-disable-line react/jsx-no-bind
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right'
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left'
            }}
          >
            <ul
              style={{
                padding: '10px',
                margin: '10px',
                width: '200px',
                backgroundColor: 'whiteSmoke'
              }}
              role="menu"
            >
              <li
                key="selectAll"
                onClick={handleSelectAll}
                tabIndex="0"
                role="menuitem"
                onKeyDown={ariaKeyPress(handleSelectAll)}
              >
                <input
                  className="mr-2"
                  type="checkbox"
                  id="selectAll"
                  value="selectAll"
                  name="selectAll"
                  checked={dropdownOptions.length === pageList?.length - 1}
                ></input>
                <label
                  for="SelectAll"
                  htmlFor="selectAll"
                  className={dropdownOptions.length === pageList.length - 1 ? 'text-blue-500 font-bold' : ''}
                >
                  Select All
                </label>
              </li>
              {pageList.map((page) => (
                <li key={page.id}>
                  <label
                    className={
                      dropdownOptions.includes(page.id)
                        ? 'font-bold text-blue-500'
                        : 'font-normal text-black-500' && (selectedPage?.id === page.id || selectedDialog?.id === page.id)
                          ? 'disabled text-gray-4000'
                          : ''
                    }
                  >
                    <input
                      type="checkbox"
                      className="mr-2"
                      id={page.id}
                      value={page.id}
                      name={page.id}
                      onChange={handleDropdownChange}
                      checked={dropdownOptions.includes(page.id) || (selectedPage?.id === page.id || selectedDialog?.id === page.id)}
                      disabled={(selectedPage?.id === page.id || selectedDialog?.id === page.id)}
                    ></input>
                    {page.name}
                  </label>
                </li>
              ))}
              <button
                onClick={handleSave}
                style={{
                  backgroundColor: '#0078d4',
                  color: 'white',
                  border: 'none',
                  borderRadius: '5px',
                  fontSize: '12px',
                  height: '35px',
                  width: '60px',
                  marginTop: '20px'
                }}
              >
                Save
              </button>
            </ul>
          </Popover>
        </div>
      </div>,
      container
    );
  };

  return (
    <>
      {/* Render the toolbar using portal */}
      <Toolbar />

      <div style={containerStyle} ref={resizableRef} className="pointer-events-none">
        {children}
      </div>
      {/* Resize Handles */}
      {showOutline ? (
        <>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle top-left"
            onMouseDown={(e) => startResize(e, 'top-left')}
          ></div>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle top-middle"
            onMouseDown={(e) => startResize(e, 'top')}
          ></div>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle top-right"
            onMouseDown={(e) => startResize(e, 'top-right')}
          ></div>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle middle-right"
            onMouseDown={(e) => startResize(e, 'right')}
          ></div>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle bottom-right"
            onMouseDown={(e) => startResize(e, 'bottom-right')}
          ></div>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle bottom-middle"
            onMouseDown={(e) => startResize(e, 'bottom')}
          ></div>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle bottom-left"
            onMouseDown={(e) => startResize(e, 'bottom-left')}
          ></div>
          <div
            role="button"
            tabIndex="0"
            className="resize-handle middle-left"
            onMouseDown={(e) => startResize(e, 'left')}
          ></div>
        </>
      ) : null}
    </>
  );
};

export default ElementResize;
